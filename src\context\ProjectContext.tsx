
import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback, useMemo } from 'react';
import { useToast } from "@/hooks/use-toast";
import {  getProjects, getStarredProjects } from '@/api/projectsApi';
import { Project } from '@/entities/Project';

interface ProjectContextType {
  projects: Project[];
  starredProjects: Project[];
  isLoading: boolean;
  loadProjects: (workspaceId?: number) => Promise<void>;
  loadStarredProjects: () => Promise<void>;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const useProjects = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProjects must be used within a ProjectProvider');
  }
  return context;
};

export const ProjectProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [starredProjects, setStarredProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { toast } = useToast();

  // Use useCallback to prevent unnecessary function recreations
  const loadProjects = useCallback(async (workspaceId?: number) => {
    try {
      setIsLoading(true);
      const data = await getProjects(workspaceId);
      setProjects(data);
    } catch (error) {
      console.error('Failed to load projects:', error);
      toast({
        variant: "destructive",
        title: "Failed to load projects",
        description: "An error occurred while loading projects.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const loadStarredProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getStarredProjects();
      setStarredProjects(data);
    } catch (error) {
      console.error('Failed to load starred projects:', error);
      toast({
        variant: "destructive",
        title: "Failed to load starred projects",
        description: "An error occurred while loading starred projects.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Load relevant projects data on component mount
  // useEffect(() => {
  //   const path = window.location.pathname;
  //   // Don't load projects data on login or index page
  //   if (path !== '/login' && path !== '/') {
  //     loadProjects();
      
  //     // Load starred projects specifically for dashboard
  //     if (path === '/dashboard' || path === '/projects') {
  //       loadStarredProjects();
  //     }
  //   }
  // }, [loadProjects, loadStarredProjects]);

  // Use useMemo to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    projects,
    starredProjects,
    isLoading,
    loadProjects,
    loadStarredProjects,
  }), [projects, starredProjects, isLoading, loadProjects, loadStarredProjects]);

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
};
