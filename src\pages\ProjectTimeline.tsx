
import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Filter,
  ArrowLeft,
  Download,
  Printer,
  Plus,
  CalendarDays
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { format, addDays, subDays, startOfMonth, endOfMonth, parseISO } from 'date-fns';
import Navbar from '@/components/Navbar';
import TimelineDisplay from '@/components/TimelineDisplay';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { getProject } from '@/api/projectsApi';
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { getProjectTimeline, createMilestone, TimelineData, TimelineItem } from '@/api/timelineApi';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const ProjectTimeline = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Fetch the specific project
  const { data: project, isLoading: projectLoading } = useQuery({
    queryKey: ['project', projectId],
    queryFn: async () => {
      if (!projectId) return null;
      return await getProject(parseInt(projectId));
    },
  });

  const [viewMode, setViewMode] = useState<'week' | 'month' | 'quarter'>('month');
  const [dateRange, setDateRange] = useState({
    startDate: startOfMonth(new Date()),
    endDate: endOfMonth(new Date())
  });
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [filterAssignee, setFilterAssignee] = useState<string | null>(null);
  const [milestoneData, setMilestoneData] = useState({
    title: '',
    date: format(new Date(), 'yyyy-MM-dd'),
    description: ''
  });
  const [milestoneDialogOpen, setMilestoneDialogOpen] = useState(false);

  // Update date range when view mode changes
  useEffect(() => {
    const today = new Date();

    if (viewMode === 'week') {
      // Set to current week (Sunday to Saturday)
      const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday
      const startDate = subDays(today, currentDay); // Go back to Sunday
      const endDate = addDays(startDate, 6); // Go forward to Saturday

      setDateRange({ startDate, endDate });
    } else if (viewMode === 'month') {
      // Set to current month
      setDateRange({
        startDate: startOfMonth(today),
        endDate: endOfMonth(today)
      });
    } else if (viewMode === 'quarter') {
      // Set to current quarter (3 months)
      const currentMonth = today.getMonth();
      const quarterStartMonth = Math.floor(currentMonth / 3) * 3;

      const startDate = new Date(today.getFullYear(), quarterStartMonth, 1);
      const endDate = new Date(today.getFullYear(), quarterStartMonth + 2, 0);
      endDate.setDate(endDate.getDate() + 1); // Last day of the 3rd month

      setDateRange({ startDate, endDate });
    }
  }, [viewMode]);

  // Format dates for query key to ensure proper cache invalidation
  const formattedStartDate = format(dateRange.startDate, 'yyyy-MM-dd');
  const formattedEndDate = format(dateRange.endDate, 'yyyy-MM-dd');

  // Fetch timeline data
  const { data: timelineData, isLoading: timelineLoading, refetch } = useQuery<TimelineData>({
    queryKey: ['project-timeline', projectId, formattedStartDate, formattedEndDate, filterStatus, filterAssignee],
    queryFn: () => {
      if (!projectId) {
        throw new Error('No project ID available');
      }

      console.log('Fetching project timeline data with params:', {
        projectId,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        status: filterStatus || 'undefined',
        assignee: filterAssignee || 'undefined'
      });

      return getProjectTimeline(
        parseInt(projectId),
        formattedStartDate,
        formattedEndDate,
        filterStatus || undefined,
        filterAssignee ? parseInt(filterAssignee) : undefined
      );
    },
    enabled: !!projectId,
    onError: (error) => {
      console.error('Error fetching timeline data:', error);
      toast({
        title: t('timeline.error'),
        description: t('timeline.failedToLoad'),
        variant: 'destructive',
      });
    }
  });

  // Explicitly refetch when date range changes
  useEffect(() => {
    if (projectId) {
      console.log('Project timeline date range changed, triggering refetch');
      refetch();
    }
  }, [formattedStartDate, formattedEndDate, projectId, refetch]);

  // Transform timeline items to format expected by TimelineDisplay
  const transformTimelineItems = (items: TimelineItem[] = []): any[] => {
    return items.map(item => ({
      id: item.id,
      title: item.title,
      startDate: item.start_date ? parseISO(item.start_date) : new Date(),
      endDate: item.end_date ? parseISO(item.end_date) : addDays(new Date(), 1),
      status: item.status,
      assignee: item.assignee,
      color: item.color,
      type: item.type,
    }));
  };

  // Handle changing the date range
  const handlePrevPeriod = () => {
    const { startDate, endDate } = dateRange;
    const days = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    setDateRange({
      startDate: subDays(startDate, days),
      endDate: subDays(endDate, days)
    });
  };

  const handleNextPeriod = () => {
    const { startDate, endDate } = dateRange;
    const days = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    setDateRange({
      startDate: addDays(startDate, days),
      endDate: addDays(endDate, days)
    });
  };

  // Handle going to today
  const handleGoToToday = () => {
    const today = new Date();

    if (viewMode === 'week') {
      // Set to current week (Sunday to Saturday)
      const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday
      const startDate = subDays(today, currentDay); // Go back to Sunday
      const endDate = addDays(startDate, 6); // Go forward to Saturday

      setDateRange({ startDate, endDate });
    } else if (viewMode === 'month') {
      // Set to current month
      setDateRange({
        startDate: startOfMonth(today),
        endDate: endOfMonth(today)
      });
    } else if (viewMode === 'quarter') {
      // Set to current quarter (3 months)
      const currentMonth = today.getMonth();
      const quarterStartMonth = Math.floor(currentMonth / 3) * 3;

      const startDate = new Date(today.getFullYear(), quarterStartMonth, 1);
      const endDate = new Date(today.getFullYear(), quarterStartMonth + 2, 0);
      endDate.setDate(endDate.getDate() + 1); // Last day of the 3rd month

      setDateRange({ startDate, endDate });
    }

    setSelectedDate(today);
  };

  // Handle creating a milestone
  const handleCreateMilestone = async () => {
    if (!projectId) return;

    try {
      await createMilestone(parseInt(projectId), milestoneData);
      toast({
        title: t('timeline.success'),
        description: t('timeline.milestoneCreated'),
      });
      setMilestoneDialogOpen(false);
      refetch(); // Refresh timeline data

      // Reset form
      setMilestoneData({
        title: '',
        date: format(new Date(), 'yyyy-MM-dd'),
        description: ''
      });
    } catch (error) {
      console.error('Error creating milestone:', error);
      toast({
        title: t('timeline.error'),
        description: t('timeline.failedToCreate'),
        variant: 'destructive',
      });
    }
  };

  // Navigate back to projects list
  const handleBackToProjects = () => {
    navigate('/projects');
  };

  const isLoading = projectLoading || timelineLoading;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center h-64">
            <p className="text-muted-foreground">{t('timeline.loading')}</p>
          </div>
        </main>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <h3 className="text-lg font-medium mb-2">{t('timeline.projectNotFound')}</h3>
            <p className="text-muted-foreground max-w-md mb-6">
              {t('timeline.projectNotFoundDesc')}
            </p>
            <Button onClick={handleBackToProjects}>
              {t('timeline.backToProjects')}
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col space-y-6">
          {/* Header with back button */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Button variant="ghost" size="sm" onClick={handleBackToProjects} className="p-0 h-8 w-8">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                <h1 className="text-3xl font-bold">{project.name} {t('timeline.title')}</h1>
              </div>
              <p className="text-muted-foreground mt-1">
                {t('timeline.visualize')}
              </p>
            </div>

            <div className="flex items-center space-x-3">
              <Dialog open={milestoneDialogOpen} onOpenChange={setMilestoneDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" className="flex items-center">
                    <Plus className="h-4 w-4 mr-1.5" />
                    {t('timeline.addMilestone')}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{t('timeline.addProjectMilestone')}</DialogTitle>
                    <DialogDescription>
                      {t('timeline.createMilestone')}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="milestone-title">{t('timeline.milestoneTitle')}</Label>
                      <Input
                        id="milestone-title"
                        value={milestoneData.title}
                        onChange={(e) => setMilestoneData({ ...milestoneData, title: e.target.value })}
                        placeholder={t('timeline.projectLaunch')}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="milestone-date">{t('timeline.date')}</Label>
                      <Input
                        id="milestone-date"
                        type="date"
                        value={milestoneData.date}
                        onChange={(e) => setMilestoneData({ ...milestoneData, date: e.target.value })}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="milestone-description">{t('timeline.description')}</Label>
                      <Textarea
                        id="milestone-description"
                        value={milestoneData.description}
                        onChange={(e) => setMilestoneData({ ...milestoneData, description: e.target.value })}
                        placeholder={t('timeline.milestoneDetails')}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setMilestoneDialogOpen(false)}>{t('timeline.cancel')}</Button>
                    <Button onClick={handleCreateMilestone}>{t('timeline.createMilestoneButton')}</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Button variant="outline" size="sm" onClick={handlePrevPeriod}>
                <ChevronLeft className="h-4 w-4 mr-1" />
                {t('timeline.previous')}
              </Button>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="px-3">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    {format(dateRange.startDate, 'MMM dd')} - {format(dateRange.endDate, 'MMM dd, yyyy')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="center">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => {
                      if (date) {
                        console.log('Project timeline calendar date selected:', date);
                        setSelectedDate(date);
                        const newStartDate = startOfMonth(date);
                        const newEndDate = endOfMonth(date);
                        console.log('Setting new project timeline date range:', {
                          startDate: format(newStartDate, 'yyyy-MM-dd'),
                          endDate: format(newEndDate, 'yyyy-MM-dd')
                        });
                        setDateRange({
                          startDate: newStartDate,
                          endDate: newEndDate
                        });

                        // Close the popover after selection
                        document.body.click();
                      }
                    }}
                    initialFocus
                    className="p-3 pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>

              <Button variant="outline" size="sm" onClick={handleNextPeriod}>
                {t('timeline.next')}
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>

              <Button variant="outline" size="sm" onClick={handleGoToToday}>
                {t('timeline.today')}
              </Button>
            </div>
          </div>

          {/* Filters and View controls */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 bg-background/60 backdrop-blur-sm border rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('timeline.filters')}:</span>
            </div>

            <div className="flex items-center space-x-3 flex-wrap gap-y-2">
              <Select value={filterStatus ?? "all"} onValueChange={(value) => setFilterStatus(value === "all" ? null : value)}>
                <SelectTrigger className="h-8 w-[150px]">
                  <SelectValue placeholder={t('timeline.status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('timeline.allStatuses')}</SelectItem>
                  <SelectItem value="completed">{t('timeline.completed')}</SelectItem>
                  <SelectItem value="in-progress">{t('timeline.inProgress')}</SelectItem>
                  <SelectItem value="not-started">{t('timeline.notStarted')}</SelectItem>
                  <SelectItem value="at-risk">{t('timeline.atRisk')}</SelectItem>
                </SelectContent>
              </Select>

              {timelineData?.team_members && timelineData.team_members.length > 0 && (
                <Select value={filterAssignee ?? "all"} onValueChange={(value) => setFilterAssignee(value === "all" ? null : value)}>
                  <SelectTrigger className="h-8 w-[150px] ml-2">
                    <SelectValue placeholder={t('timeline.assignee')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('timeline.allAssignees')}</SelectItem>
                    {timelineData.team_members.map(member => (
                      <SelectItem key={member.id} value={member.id.toString()}>
                        {member.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              <div className="flex items-center space-x-2 ml-4">
                <span className="text-sm font-medium">{t('timeline.view')}:</span>
                <div className="flex items-center space-x-1">
                  {(["week", "month", "quarter"] as const).map((mode) => (
                    <Button
                      key={mode}
                      variant={viewMode === mode ? "default" : "outline"}
                      size="sm"
                      className="h-8 px-3"
                      onClick={() => setViewMode(mode)}
                    >
                      {t(`timeline.${mode}`)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Status Legend */}
          <div className="flex items-center space-x-4 text-sm">
            <span className="font-medium">{t('timeline.status')}:</span>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="bg-green-500/10 text-green-600 border-green-500/30">{t('timeline.completed')}</Badge>
              <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">{t('timeline.inProgress')}</Badge>
              <Badge variant="outline" className="bg-orange-500/10 text-orange-500 border-orange-500/30">{t('timeline.notStarted')}</Badge>
              <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/30">{t('timeline.atRisk')}</Badge>
            </div>
          </div>

          {/* Timeline Display */}
          <div className="bg-card border rounded-lg p-1 overflow-hidden shadow-sm">
            {timelineData && timelineData.timeline_items && timelineData.timeline_items.length > 0 ? (
              <TimelineDisplay
                items={transformTimelineItems(timelineData.timeline_items)}
                startDate={dateRange.startDate}
                endDate={dateRange.endDate}
                viewMode={viewMode}
              />
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center px-4">
                <p className="text-muted-foreground mb-4">{t('timeline.noTimelineItems')}</p>
                <Button onClick={() => navigate(`/projects/${projectId}/board`)}>
                  {t('project.addTasks')}
                </Button>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default ProjectTimeline;
