import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';
import { toast } from '@/components/ui/use-toast';

export interface WorkspaceSettings {
  id: number;
  workspace_id: number;
  default_language: string;
  enabled_languages: string[];
  theme_settings: {
    default_theme: string;
    allow_user_theme: boolean;
    [key: string]: any;
  };
  notification_settings: {
    email_notifications: boolean;
    in_app_notifications: boolean;
    [key: string]: any;
  };
  custom_settings: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Cache for workspace settings to prevent excessive API calls
const settingsCache = new Map<number, { data: WorkspaceSettings, timestamp: number }>();
const CACHE_TTL = 60000; // 1 minute cache TTL

// Get workspace settings
export const getWorkspaceSettings = async (workspaceId: number): Promise<WorkspaceSettings> => {
  // Check if we have cached settings that are still valid
  const cachedSettings = settingsCache.get(workspaceId);
  const now = Date.now();

  if (cachedSettings && (now - cachedSettings.timestamp < CACHE_TTL)) {
    console.log(`Using cached workspace settings for workspace ID: ${workspaceId}`);
    return cachedSettings.data;
  }

  try {
    console.log(`API call: Getting workspace settings for workspace ID: ${workspaceId}`);
    const response = await api.get(`/workspace/${workspaceId}/settings`);
    console.log('API response for workspace settings:', response.data);

    // Cache the settings
    settingsCache.set(workspaceId, {
      data: response.data,
      timestamp: now
    });

    return response.data;
  } catch (error: any) {
    console.error(`Error fetching workspace settings for workspace ID ${workspaceId}:`, error);

    // If the error is a 429 (Too Many Requests), use cached settings if available
    if (error.response && error.response.status === 429) {
      if (cachedSettings) {
        console.log(`Using cached settings due to rate limiting for workspace ID: ${workspaceId}`);
        return cachedSettings.data;
      }

      console.log('Rate limited and no cached settings available, using defaults');
    }

    // If the error is a 404, it might mean the settings don't exist yet
    if (error.response && error.response.status === 404) {
      console.log('Settings not found, will use defaults');
      // Create default settings
      const defaultSettings = {
        id: 0,
        workspace_id: workspaceId,
        default_language: 'en',
        enabled_languages: ['en', 'tr'],
        theme_settings: {
          default_theme: 'light',
          allow_user_theme: true
        },
        notification_settings: {
          email_notifications: true,
          in_app_notifications: true
        },
        custom_settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Cache the default settings
      settingsCache.set(workspaceId, {
        data: defaultSettings,
        timestamp: now
      });

      return defaultSettings;
    }

    return handleApiError(
      error,
      "Failed to fetch workspace settings",
      "Could not load workspace settings"
    );
  }
};

// Update workspace settings
export const updateWorkspaceSettings = async (
  workspaceId: number,
  settingsData: Partial<WorkspaceSettings>
): Promise<WorkspaceSettings> => {
  try {
    console.log(`API call: Updating workspace settings for workspace ID: ${workspaceId}`, settingsData);
    const response = await api.put(`/workspace/${workspaceId}/settings`, settingsData);
    console.log('API response for updating workspace settings:', response.data);

    // Update the cache with the new settings
    const now = Date.now();
    settingsCache.set(workspaceId, {
      data: response.data,
      timestamp: now
    });

    toast({
      title: "Settings updated",
      description: "Workspace settings have been updated successfully.",
    });
    return response.data;
  } catch (error: any) {
    console.error(`Error updating workspace settings for workspace ID ${workspaceId}:`, error);

    // If the error is a 429 (Too Many Requests), inform the user
    if (error.response && error.response.status === 429) {
      toast({
        title: "Rate limit exceeded",
        description: "Too many requests. Please wait a moment and try again.",
        variant: "destructive",
      });

      // Return the cached settings if available
      const cachedSettings = settingsCache.get(workspaceId);
      if (cachedSettings) {
        return cachedSettings.data;
      }
    }

    // If the error is a 404, it might mean the settings don't exist yet
    if (error.response && error.response.status === 404) {
      console.log('Settings not found, trying to create them instead');

      // Try to create settings by using POST instead
      try {
        const createResponse = await api.post(`/workspace/${workspaceId}/settings`, settingsData);
        console.log('Created new workspace settings:', createResponse.data);

        // Update the cache with the new settings
        const now = Date.now();
        settingsCache.set(workspaceId, {
          data: createResponse.data,
          timestamp: now
        });

        toast({
          title: "Settings created",
          description: "Workspace settings have been created successfully.",
        });
        return createResponse.data;
      } catch (createError) {
        console.error('Failed to create settings:', createError);
        return handleApiError(
          createError,
          "Failed to create workspace settings",
          "Could not create workspace settings"
        );
      }
    }

    return handleApiError(
      error,
      "Failed to update workspace settings",
      "Could not update workspace settings"
    );
  }
};
