{"search": {"title": "Search", "button": "Search", "placeholder": "Search for projects, tasks, or people...", "tip": "Tip: Use Ctrl+K for faster searching.", "tipTitle": "Pro tip:"}, "dashboard": {"totalProjects": "Total Projects", "inProgress": "In Progress", "completed": "Completed", "upcomingDeadlines": "Upcoming Deadlines", "percentOfAllProjects": "{{percent}}% of all projects", "completionRate": "{{percent}}% completion rate", "dueInNextWeek": "Due in the next 7 days"}, "settings": {"title": "Settings", "subtitle": "Manage your account settings and preferences", "tabs": {"general": "General", "notifications": "Notifications", "security": "Security"}, "appearance": {"title": "Appearance", "subtitle": "Customize the look and feel of the application", "darkMode": "Dark Mode", "language": "Language", "lightModeActivated": "Light mode activated", "darkModeActivated": "Dark mode activated", "themeChanged": "The application theme has been changed to {{mode}} mode."}, "emailNotifications": {"title": "Email Notifications", "subtitle": "Choose what types of emails you receive", "comments": {"title": "Comments and Mentions", "description": "Get notified when someone mentions you or comments on your projects"}, "projects": {"title": "Project Updates", "description": "Get notified about project status changes and milestones"}, "newsletter": {"title": "Newsletter and Updates", "description": "Receive product updates and newsletters"}, "saveButton": "Save Notification Preferences"}, "inAppNotifications": {"title": "In-App Notifications", "subtitle": "Configure your in-app notification settings", "taskAssigned": "Task Assigned", "taskDue": "Task Due Soon", "projectActivity": "Project Activity"}, "security": {"title": "Password", "subtitle": "Update your password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "updateButton": "Update Password"}, "languages": {"english": "English", "spanish": "Spanish", "french": "French", "turkish": "Turkish", "changed": "Language changed", "changedDescription": "The application language has been changed to {{language}}."}}, "profile": {"title": "Profile", "subtitle": "Manage your personal information", "photo": {"title": "Profile Photo", "subtitle": "Update your profile picture", "uploadButton": "Upload Photo"}, "personalInfo": {"title": "Personal Information", "subtitle": "Update your personal details", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone Number", "jobTitle": "Job Title", "birthDate": "Birth Date", "bio": "Bio", "saveButton": "Save Changes"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "submit": "Submit", "loading": "Loading...", "retry": "Retry", "error": "Error", "create": "Create", "creating": "Creating..."}, "navbar": {"suite": "Suite", "workspace": "Workspace", "timeline": "Timeline", "calendar": "Calendar", "team": "Team", "activity": "Activity", "search": "Search...", "help": "Help", "profile": "Profile", "userSettings": "User Settings", "workspaceSettings": "Workspace Settings", "settings": "Settings", "logout": "Logout", "signOut": "Sign out", "lightMode": "Switch to light mode", "darkMode": "Switch to dark mode"}, "workspace": {"welcome": "Welcome back, {{name}}! Here's what's happening today.", "allProjects": "All Projects", "inProgress": "In Progress", "completed": "Completed", "atRisk": "At Risk", "notStarted": "Not Started", "projectsOverview": "Projects Overview", "viewAll": "View All", "createNewProject": "Create New Project", "upcomingDeadlines": "Upcoming Deadlines", "calendarView": "Calendar View", "noUpcomingDeadlines": "No upcoming deadlines found.", "team": "Team", "administrator": "Administrator", "moreMembersCount": "+{{count}} more members", "comparedToLastMonth": "compared to last month", "loadingError": "Failed to load workspace. Please try again.", "searchProjects": "Search projects...", "retry": "Retry", "notFound": "Workspace not found or you don't have access to it.", "notFoundTitle": "Workspace not found", "notFoundDescription": "The workspace you're trying to access doesn't exist or you don't have permission to view it.", "overallProgress": "Overall Progress", "averageCompletion": "Average Completion", "thisWeek": "This week", "projectTimeline": "Project Timeline", "dueDate": "Due", "noDueDate": "No due date", "select": "Select workspace", "search": "Search workspaces...", "noResults": "No workspaces found", "workspaces": "Workspaces", "createNew": "Create New Workspace", "settings": "Workspace Settings", "selectWorkspaceForTimeline": "You need to select a workspace to view the timeline.", "goToWorkspaces": "Go to Workspaces", "backToWorkspace": "Back to Workspace", "statistics": {"allProjects": "All Projects", "inProgress": "In Progress", "completed": "Completed", "atRisk": "At Risk"}}, "project": {"name": "Project Name", "namePlaceholder": "Enter project name", "description": "Project Description", "descriptionPlaceholder": "Describe your project", "startDate": "Start Date", "endDate": "End Date", "status": {"notStarted": "Not Started", "inProgress": "In Progress", "underReview": "Under Review", "completed": "Completed", "unknown": "Unknown Status"}, "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "selectPriority": "Select priority", "members": "Members", "progress": "Progress", "taskCompletion": "Task Completion", "completed": "Completed", "create": "Create Project", "createDescription": "Fill in the details below to create a new project.", "creating": "Creating...", "edit": "Edit Project", "delete": "Delete Project", "group": "Group", "personal": "Personal", "high": "High", "medium": "Medium", "low": "Low", "viewTimeline": "View timeline", "timeline": "Timeline", "noDueDate": "No due date", "addTasks": "Add Project Tasks", "viewOptions": "View Options", "views": "Views", "kanban": "Kanban Board", "list": "List View", "calendar": "Calendar View", "reports": "Reports", "selectView": "Select View", "errors": {"nameRequired": "Project name is required", "nameLength": "Project name must be at least 3 characters"}, "toast": {"created": "Project created", "createdDescription": "Your new project has been created successfully.", "failed": "Failed to create project", "failedDescription": "There was an error creating your project. Please try again."}}, "task": {"title": "Task Title", "description": "Description", "status": "Status", "priority": "Priority", "dueDate": "Due Date", "assignees": "Assignees", "unassigned": "Unassigned", "addTask": "Add Task", "editTask": "Edit Task", "deleteTask": "Delete Task", "deleteConfirmation": "Are you sure you want to delete this task?", "deleteSuccess": "Task deleted successfully", "createSuccess": "Task created successfully", "updateSuccess": "Task updated successfully", "noTasks": "No tasks found", "dragAndDrop": "Drag and drop tasks to reorder", "searchTasks": "Search tasks...", "filterByStatus": "Filter by status", "filterByPriority": "Filter by priority", "filterByAssignee": "Filter by assignee", "clearFilters": "Clear filters", "sortBy": "Sort by", "ascending": "Ascending", "descending": "Descending", "titlePlaceholder": "Enter task title", "descriptionPlaceholder": "Enter task description", "selectStatus": "Select status", "selectPriority": "Select priority", "selectAssignee": "Select assignee", "startDate": "Start Date", "endDate": "End Date", "pickDate": "Pick a date", "noDueDate": "No due date", "createSuccessDescription": "Your new task has been created.", "updateSuccessDescription": "Your task has been updated.", "view": "View", "edit": "Edit", "delete": "Delete", "actions": "Actions", "createNew": "Create New Task", "createDescription": "Add a new task to your project. Fill in the details below.", "details": "Details", "people": "People", "assignPeople": "Assign people to this task", "assigneesDescription": "Assign team members to work on this task.", "allStatuses": "All Statuses", "allPriorities": "All Priorities", "tasksFound": "tasks found"}, "status": {"todo": "To Do", "inProgress": "In Progress", "completed": "Completed", "atRisk": "At Risk"}, "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "activity": {"title": "Activity Feed", "refresh": "Refresh", "noActivities": "No activity to display", "checkBackLater": "Recent activities will appear here as your team makes progress.", "pageTitle": "Activity Feed", "pageDescription": "Track all activities across your projects and team.", "clearFilters": "Clear Filters", "sortOrder": "Sort order", "commentedOn": "commented on", "unknownActivity": "Unknown activity"}, "timeline": {"title": "Timeline", "visualize": "Visualize project schedule and track progress", "addMilestone": "Add Milestone", "addProjectMilestone": "Add Project Milestone", "createMilestone": "Create a milestone to mark important dates in your project timeline.", "milestoneTitle": "Title", "date": "Date", "description": "Description (optional)", "projectLaunch": "e.g., Project Launch", "milestoneDetails": "Add details about this milestone", "cancel": "Cancel", "createMilestoneButton": "Create Milestone", "previous": "Previous", "next": "Next", "today": "Today", "filters": "Filters", "status": "Status", "allStatuses": "All Statuses", "completed": "Completed", "inProgress": "In Progress", "notStarted": "Not Started", "atRisk": "At Risk", "assignee": "Assignee", "allAssignees": "All Assignees", "view": "View", "week": "Week", "month": "Month", "quarter": "Quarter", "loading": "Loading project timeline...", "projectNotFound": "Project not found", "projectNotFoundDesc": "The project you are looking for does not exist or you don't have access to it.", "backToProjects": "Back to Projects", "success": "Success", "milestoneCreated": "Milestone created successfully", "error": "Error", "failedToCreate": "Failed to create milestone. Please try again.", "failedToLoad": "Failed to load timeline data. Please try again.", "noProjectsMatchFilter": "No projects match the selected filter", "noProjectsAvailable": "No projects available yet", "noTimelineItems": "No timeline items available for this project"}}