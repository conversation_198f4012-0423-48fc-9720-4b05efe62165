import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import api from '@/api/api';
import { API_BASE_URL } from '@/config';

const AcceptProjectInvitation = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [invitedByName, setInvitedByName] = useState('');

  useEffect(() => {
    const checkInvitation = async () => {
      if (!token) {
        setError('Invalid invitation link. No token provided.');
        setLoading(false);
        return;
      }

      // Log authentication status and token
      console.log('Authentication status:', isAuthenticated);
      console.log('Current user:', user);

      try {
        console.log('Checking invitation with token:', token);

        // First check if the invitation is valid without accepting it
        try {
          const checkResponse = await api.get(`/check-project-invitation?token=${token}`);
          console.log('Check invitation response:', checkResponse.data);

          // If we get here, the invitation is valid
          if (!isAuthenticated) {
            console.log('User not authenticated, redirecting to login');
            // Redirect to login with the invitation token as a redirect parameter
            navigate(`/login?redirect=${encodeURIComponent(`/accept-project-invitation?token=${token}`)}`);
            return;
          }

          console.log('User authenticated, proceeding to accept invitation');
          // User is authenticated, proceed to accept the invitation
          try {
            const response = await api.get(`/accept-project-invitation?token=${token}`);
            console.log('Accept invitation response:', response.data);

            setSuccess(true);
            setProjectName(response.data.project?.name || 'the project');
            setInvitedByName(response.data.invited_by?.name || 'Someone');
            setLoading(false);
          } catch (acceptErr: any) {
            console.error('Error accepting invitation:', acceptErr);

            // Check if the error is because the invitation was already accepted
            if (acceptErr.response?.data?.error?.includes('already accepted')) {
              // If already accepted, still show success message
              setSuccess(true);
              setProjectName(acceptErr.response?.data?.details?.project_name || 'the project');
              setInvitedByName(acceptErr.response?.data?.details?.invited_by_name || 'Someone');
              setLoading(false);
            } else {
              // For other errors, show the error message
              setError(acceptErr.response?.data?.error || 'An error occurred while accepting the invitation.');
              setErrorDetails(acceptErr.response?.data?.details || null);
              setLoading(false);
            }
          }
        } catch (checkErr: any) {
          console.error('Error checking invitation:', checkErr);

          // Check if the error is because the invitation was already accepted
          if (checkErr.response?.data?.error?.includes('already accepted') && isAuthenticated) {
            // If already accepted, show success message
            setSuccess(true);
            setProjectName(checkErr.response?.data?.details?.project_name || 'the project');
            setInvitedByName(checkErr.response?.data?.details?.invited_by_name || 'Someone');
            setLoading(false);
          } else if (isAuthenticated) {
            // Only try direct acceptance if the user is authenticated and the error is not "already accepted"
            try {
              console.log('Trying to accept invitation directly');
              const response = await api.get(`/accept-project-invitation?token=${token}`);
              console.log('Direct accept response:', response.data);

              setSuccess(true);
              setProjectName(response.data.project?.name || 'the project');
              setInvitedByName(response.data.invited_by?.name || 'Someone');
              setLoading(false);
            } catch (directAcceptErr: any) {
              console.error('Error with direct accept:', directAcceptErr);

              // Check if the error is because the invitation was already accepted
              if (directAcceptErr.response?.data?.error?.includes('already accepted')) {
                // If already accepted, still show success message
                setSuccess(true);
                setProjectName(directAcceptErr.response?.data?.details?.project_name || 'the project');
                setInvitedByName(directAcceptErr.response?.data?.details?.invited_by_name || 'Someone');
                setLoading(false);
              } else {
                // For other errors, show the error message
                setError(directAcceptErr.response?.data?.error || 'The invitation is invalid or has expired.');
                setErrorDetails(directAcceptErr.response?.data?.details || null);
                setLoading(false);
              }
            }
          } else {
            // Not authenticated and check failed, show error
            setError(checkErr.response?.data?.error || 'The invitation is invalid or has expired.');
            setErrorDetails(checkErr.response?.data?.details || null);
            setLoading(false);
          }
        }
      } catch (err: any) {
        console.error('Unexpected error:', err);
        setError('An unexpected error occurred. Please try again later.');
        setErrorDetails('Please check the console for more details or try again with a new invitation.');
        setLoading(false);
      }
    };

    checkInvitation();
  }, [token, isAuthenticated, navigate, user]);

  const handleGoToProject = () => {
    navigate('/projects');
  };

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Processing Invitation</CardTitle>
            <CardDescription>Please wait while we process your invitation...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-6">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center text-destructive">
              <AlertCircle className="mr-2 h-6 w-6" />
              Invitation Error
            </CardTitle>
            <CardDescription>There was a problem with your invitation.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="font-medium text-destructive">{error}</p>
            {errorDetails && (
              <p className="mt-2 text-sm text-muted-foreground">{errorDetails}</p>
            )}
          </CardContent>
          <CardFooter>
            <Button onClick={handleGoToDashboard} className="w-full">
              Go to Dashboard
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center text-green-600">
            <CheckCircle className="mr-2 h-6 w-6" />
            Invitation Accepted
          </CardTitle>
          <CardDescription>You have successfully joined the project.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            You have been added to <strong>{projectName}</strong> by <strong>{invitedByName}</strong>.
          </p>
          <p>You can now access the project and start collaborating with the team.</p>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
          <Button onClick={handleGoToProject} className="w-full">
            Go to Projects
          </Button>
          <Button onClick={handleGoToDashboard} variant="outline" className="w-full">
            Go to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AcceptProjectInvitation;
