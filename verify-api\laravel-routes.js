
const fs = require('fs');

/**
 * Parse Laravel routes from api.php
 * @param {string} routesFilePath Path to the api.php file
 * @returns {Array} Array of route objects
 */
function parseLaravelRoutes(routesFilePath) {
  const content = fs.readFileSync(routesFilePath, 'utf-8');
  const routes = [];
  
  // Match route definitions like Route::get('/users', [UserController::class, 'index']);
  const routeRegex = /Route::(\w+)\(['"]([^'"]+)['"]/g;
  let match;
  
  while ((match = routeRegex.exec(content)) !== null) {
    const method = match[1].toUpperCase();
    let path = match[2];
    
    // Convert Laravel route parameters to actual values for testing
    path = path.replace(/{([^}]+)}/g, (_, param) => `:${param}`);
    
    routes.push({
      method,
      path,
      original: match[0]
    });
  }
  
  // Also handle route groups and apiResource
  const resourceRegex = /Route::apiResource\(['"]([^'"]+)['"]/g;
  while ((match = resourceRegex.exec(content)) !== null) {
    const resource = match[1];
    
    // API resource creates multiple routes
    routes.push(
      { method: 'GET', path: `/${resource}`, original: match[0] },
      { method: 'POST', path: `/${resource}`, original: match[0] },
      { method: 'GET', path: `/${resource}/:id`, original: match[0] },
      { method: 'PUT', path: `/${resource}/:id`, original: match[0] },
      { method: 'DELETE', path: `/${resource}/:id`, original: match[0] }
    );
  }
  
  return routes;
}

/**
 * Check if an endpoint exists in the Laravel routes
 * @param {string} method HTTP method
 * @param {string} path Endpoint path
 * @param {Array} routes Laravel routes
 * @returns {boolean} True if the endpoint exists
 */
function endpointExistsInBackend(method, path, routes) {
  // Normalize path for comparison
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  return routes.some(route => {
    // Direct match
    if (route.method === method && route.path === normalizedPath) {
      return true;
    }
    
    // Check for param patterns (e.g., /users/:id vs /users/{id})
    const routePattern = route.path.replace(/:[^/]+/g, '[^/]+');
    const regex = new RegExp(`^${routePattern}$`);
    
    return route.method === method && regex.test(normalizedPath);
  });
}

module.exports = {
  parseLaravelRoutes,
  endpointExistsInBackend
};
