import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Users, Clock, ArrowUp, ArrowRight, ArrowDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { createTask } from '@/api/tasksApi';
import { useToast } from '@/components/ui/use-toast';
import { Project } from '@/entities/Project';
import { StatusBadge } from '@/components/ui/status-badge';
import { PriorityIndicator } from '@/components/ui/priority-indicator';
import UserMultiSelect from '@/components/UserMultiSelect';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Define the form schema
const taskFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  status: z.string().default('todo'),
  priority: z.string().default('medium'),
  start_date: z.date().optional().nullable(),
  end_date: z.date().optional().nullable(),
  assignees: z.array(z.number()).optional(),
});

type TaskFormValues = z.infer<typeof taskFormSchema>;

interface CreateTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  project: Project;
  columnId?: string;
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  project,
  columnId,
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  // Initialize the form with default values
  const form = useForm<TaskFormValues>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: '',
      description: '',
      status: columnId || 'todo',
      priority: 'medium',
      start_date: null,
      end_date: null,
      assignees: [],
    },
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      form.reset({
        title: '',
        description: '',
        status: columnId || 'todo',
        priority: 'medium',
        start_date: null,
        end_date: null,
        assignees: [],
      });
      setActiveTab('details');
    }
  }, [isOpen, columnId, form]);

  // Handle form submission
  const onSubmit = async (values: TaskFormValues) => {
    setIsSubmitting(true);
    try {
      // Get the actual list ID from the columnId prop if available
      let list_id: number | undefined;

      // If columnId is provided directly, use it
      if (columnId && !isNaN(Number(columnId))) {
        list_id = Number(columnId);
        console.log(`Using provided columnId: ${list_id}`);
      } else {
        // Otherwise, fetch the lists for this project to find an appropriate list
        try {
          const response = await fetch(`/api/projects/${project.id}/lists`);
          if (response.ok) {
            const lists = await response.json();
            if (lists && lists.length > 0) {
              // Use the first list by default
              list_id = lists[0].id;
              console.log(`Using first list from project: ${list_id}`);
            }
          }
        } catch (error) {
          console.error("Error fetching lists:", error);
        }

        // If we still don't have a list_id, use a fallback approach
        if (!list_id) {
          console.log("No valid list found, using fallback approach");
          // Try to find a list with a matching status name
          const response = await fetch(`/api/projects/${project.id}/lists`);
          if (response.ok) {
            const lists = await response.json();
            // Find a list with a title that matches the status
            const matchingList = lists.find((list: any) =>
              list.title.toLowerCase().includes(values.status.toLowerCase())
            );
            if (matchingList) {
              list_id = matchingList.id;
              console.log(`Found matching list by status: ${list_id}`);
            } else if (lists.length > 0) {
              // If no matching list, use the first list
              list_id = lists[0].id;
              console.log(`Using first available list: ${list_id}`);
            }
          }
        }
      }

      // If we still don't have a list_id, show an error
      if (!list_id) {
        throw new Error("No valid list found for this project. Please create a list first.");
      }

      console.log(`Creating task with list_id: ${list_id}`);
      await createTask({
        ...values,
        project_id: project.id,
        list_id: list_id
      });

      toast({
        title: t('task.createSuccess', 'Task created successfully'),
        description: t('task.createSuccessDescription', 'Your new task has been created.'),
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating task:', error);

      // Check if the error is about invalid list_id
      let errorMessage = t('error.createTask', 'Failed to create task. Please try again.');

      if (error instanceof Error) {
        if (error.message.includes("No valid list found")) {
          errorMessage = "Please create a list first before adding tasks.";
        } else if (error.message.includes("list id is invalid")) {
          errorMessage = "The selected list is invalid. Please create a valid list first.";
        }
      }

      // Check if it's an API error with a response
      const axiosError = error as any;
      if (axiosError.response && axiosError.response.data) {
        console.error('API Error details:', axiosError.response.data);

        // If there's a specific message about list_id
        if (axiosError.response.data.errors && axiosError.response.data.errors.list_id) {
          errorMessage = `List error: ${axiosError.response.data.errors.list_id[0]}`;
        } else if (axiosError.response.data.message) {
          errorMessage = axiosError.response.data.message;
        }
      }

      toast({
        title: t('error.title', 'Error'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto" closeButton={false}>
        <DialogHeader>
          <DialogTitle className="text-xl">{t('task.createNew', 'Create New Task')}</DialogTitle>
          <DialogDescription>
            {t('task.createDescription', 'Add a new task to your project. Fill in the details below.')}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details">{t('task.details', 'Details')}</TabsTrigger>
            <TabsTrigger value="people">{t('task.people', 'People')}</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-4">
              <TabsContent value="details" className="space-y-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('task.title', 'Title')}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('task.titlePlaceholder', 'Enter task title')}
                          {...field}
                          autoFocus
                          className="text-base"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('task.description', 'Description')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('task.descriptionPlaceholder', 'Enter task description')}
                          className="min-h-[120px] text-base"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('task.status', 'Status')}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('task.selectStatus', 'Select status')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="todo">
                              <div className="flex items-center gap-2">
                                <StatusBadge status="todo" size="sm" />
                                <span>{t('status.todo', 'To Do')}</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="in-progress">
                              <div className="flex items-center gap-2">
                                <StatusBadge status="in-progress" size="sm" />
                                <span>{t('status.inProgress', 'In Progress')}</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="completed">
                              <div className="flex items-center gap-2">
                                <StatusBadge status="completed" size="sm" />
                                <span>{t('status.completed', 'Completed')}</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="at-risk">
                              <div className="flex items-center gap-2">
                                <StatusBadge status="at-risk" size="sm" />
                                <span>{t('status.atRisk', 'At Risk')}</span>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('task.priority', 'Priority')}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('task.selectPriority', 'Select priority')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">
                              <div className="flex items-center gap-2">
                                <PriorityIndicator priority="low" size="sm" />
                                <span>{t('priority.low', 'Low')}</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="medium">
                              <div className="flex items-center gap-2">
                                <PriorityIndicator priority="medium" size="sm" />
                                <span>{t('priority.medium', 'Medium')}</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="high">
                              <div className="flex items-center gap-2">
                                <PriorityIndicator priority="high" size="sm" />
                                <span>{t('priority.high', 'High')}</span>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="start_date"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t('task.startDate', 'Start Date')}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>{t('task.pickDate', 'Pick a date')}</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value || undefined}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="end_date"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t('task.dueDate', 'Due Date')}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>{t('task.pickDate', 'Pick a date')}</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value || undefined}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="people" className="space-y-6">
                <FormField
                  control={form.control}
                  name="assignees"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        {t('task.assignees', 'Assignees')}
                      </FormLabel>
                      <FormControl>
                        <UserMultiSelect
                          users={project.members || []}
                          selectedUserIds={field.value || []}
                          onChange={field.onChange}
                          placeholder={t('task.assignPeople', 'Assign people to this task')}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('task.assigneesDescription', 'Assign team members to work on this task.')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <DialogFooter className="pt-4 border-t mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  {t('common.cancel', 'Cancel')}
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? t('common.creating', 'Creating...') : t('common.create', 'Create')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTaskModal;
