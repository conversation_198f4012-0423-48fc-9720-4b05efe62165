export class EnglishDateHelper {
    /**
     * Formats date to English locale string
     * @param date Date object, string, or timestamp
     * @param options Intl.DateTimeFormatOptions
     * @returns Formatted English date string
     */
    static formatDate(
        date: Date | string | number,
        options: Intl.DateTimeFormatOptions = {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        }
    ): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', options);
    }

    /**
     * Gets day number with English month name (e.g., "5 April")
     */
    static getMonthNameWithDay(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        const day = parsedDate.getDate();
        const monthName = parsedDate.toLocaleDateString('en-US', { month: 'long' });
        return `${day} ${monthName}`;
    }

    /**
     * Formats date to English short date (MM/dd/yyyy)
     */
    static formatShortDate(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    /**
     * Formats date to English long date (MMMM dd, yyyy, EEEE)
     */
    static formatLongDate(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            weekday: 'long'
        });
    }

    /**
     * Formats date to English datetime (MM/dd/yyyy, HH:mm)
     */
    static formatDateTime(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Formats date to English time (HH:mm:ss)
     */
    static formatTime(date: Date | string | number, showSeconds = false): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: showSeconds ? '2-digit' : undefined
        });
    }

    /**
     * Converts any date input to Date object
     */
    private static parseDate(date: Date | string | number): Date {
        if (date instanceof Date) return date;
        if (typeof date === 'number') return new Date(date);
        if (typeof date === 'string') {
            // Handle ISO strings and English locale strings
            const parsed = new Date(date);
            if (!isNaN(parsed.getTime())) return parsed;

            // Try parsing MM/dd/yyyy format
            const parts = date.split('/');
            if (parts.length === 3) {
                return new Date(Number(parts[2]), Number(parts[0]) - 1, Number(parts[1]));
            }
        }
        throw new Error('Invalid date format');
    }

    /**
     * Gets English day name
     */
    static getDayName(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', { weekday: 'long' });
    }

    /**
     * Gets English month name
     */
    static getMonthName(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', { month: 'long' });
    }

    /**
     * Gets short English day name
     */
    static getShortDayName(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', { weekday: 'short' });
    }

    /**
     * Gets short English month name
     */
    static getShortMonthName(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', { month: 'short' });
    }

    /**
     * Formats relative time (e.g., "2 days ago", "in 3 hours")
     */
    static formatRelativeTime(date: Date | string | number): string {
        const parsedDate = this.parseDate(date);
        const now = new Date();
        const diffMs = parsedDate.getTime() - now.getTime();
        const diffSec = Math.round(diffMs / 1000);
        const diffMin = Math.round(diffSec / 60);
        const diffHour = Math.round(diffMin / 60);
        const diffDay = Math.round(diffHour / 24);
        const diffMonth = Math.round(diffDay / 30);
        const diffYear = Math.round(diffDay / 365);

        if (Math.abs(diffSec) < 60) {
            return diffSec >= 0 ? 'just now' : 'just now';
        } else if (Math.abs(diffMin) < 60) {
            return diffMin >= 0 ? `in ${diffMin} minute(s)` : `${Math.abs(diffMin)} minute(s) ago`;
        } else if (Math.abs(diffHour) < 24) {
            return diffHour >= 0 ? `in ${diffHour} hour(s)` : `${Math.abs(diffHour)} hour(s) ago`;
        } else if (Math.abs(diffDay) < 30) {
            return diffDay >= 0 ? `in ${diffDay} day(s)` : `${Math.abs(diffDay)} day(s) ago`;
        } else if (Math.abs(diffMonth) < 12) {
            return diffMonth >= 0 ? `in ${diffMonth} month(s)` : `${Math.abs(diffMonth)} month(s) ago`;
        } else {
            return diffYear >= 0 ? `in ${diffYear} year(s)` : `${Math.abs(diffYear)} year(s) ago`;
        }
    }
}
