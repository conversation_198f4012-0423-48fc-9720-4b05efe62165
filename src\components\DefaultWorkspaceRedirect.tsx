import React, { useEffect, useState, useRef } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { getWorkspaces, createWorkspace } from '@/api/workspacesApi';
import { getLocalStorageData, setLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import Loader from './Loader';
import { useToast } from './ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { Button } from './ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

/**
 * Component that redirects to the default workspace
 * It first checks if there's a workspace in localStorage,
 * then fetches workspaces if needed and redirects to the first one
 */
const DefaultWorkspaceRedirect: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const attemptCountRef = useRef(0);

  // Function to create a default workspace if none exists
  const createDefaultWorkspace = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user || !user.id) {
        throw new Error('User not authenticated');
      }

      console.log('Creating default workspace for user:', user.id);

      // Create a default workspace
      const newWorkspace = await createWorkspace({
        name: 'My Workspace',
        description: 'My personal workspace',
        user_id: user.id
      });

      console.log('Default workspace created:', newWorkspace);

      // Save to localStorage with user-specific key
      const userWorkspaceKey = `workspace_${user.id}`;
      setLocalStorageData(userWorkspaceKey, newWorkspace);

      // Also save to regular workspace key for backward compatibility
      setLocalStorageData('workspace', newWorkspace);

      toast({
        title: "Default workspace created",
        description: "A default workspace has been created for you.",
      });

      setRedirectPath(`/workspace/${newWorkspace.id}`);
    } catch (error) {
      console.error('Error creating default workspace:', error);
      setError('Failed to create a default workspace. Please try again or contact support.');
      toast({
        title: "Error creating workspace",
        description: "Could not create a default workspace. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Check if user is authenticated first
    if (!isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      navigate('/login');
      return;
    }

    // Increment attempt counter to prevent infinite loops
    attemptCountRef.current += 1;

    // If we've tried too many times, show an error
    if (attemptCountRef.current > 3) {
      setError('Too many attempts to load workspace. Please try again later or contact support.');
      setIsLoading(false);
      return;
    }

    const redirectToWorkspace = async () => {
      try {
        setError(null);
        console.log('Attempting to find workspace for user:', user?.id);

        // First check if we have a user-specific workspace in localStorage
        let savedWorkspace = null;

        if (user?.id) {
          // Try to get the user-specific workspace first
          const userWorkspaceKey = `workspace_${user.id}`;
          savedWorkspace = getLocalStorageData(userWorkspaceKey);
          console.log('Found user-specific workspace in localStorage:', savedWorkspace?.id);

          // If not found, fall back to the regular workspace key
          if (!savedWorkspace || !savedWorkspace.id) {
            savedWorkspace = getLocalStorageData('workspace');
            console.log('Falling back to regular workspace key:', savedWorkspace?.id);

            // If we found a workspace in the regular key, validate it belongs to the current user
            if (savedWorkspace && savedWorkspace.owner && savedWorkspace.owner.id !== user.id) {
              console.warn('Found workspace belonging to another user, ignoring it');
              savedWorkspace = null;
              localStorage.removeItem('workspace');
            }
          }
        } else {
          console.warn('No user available, trying regular workspace key');
          // If no user is available, try the regular workspace key as fallback
          savedWorkspace = getLocalStorageData('workspace');
        }

        if (savedWorkspace && savedWorkspace.id) {
          console.log('Using saved workspace:', savedWorkspace.id);
          setRedirectPath(`/workspace/${savedWorkspace.id}`);
          return;
        }

        // If no workspace in localStorage, fetch workspaces
        console.log('No workspace in localStorage, fetching from API');
        const workspaces = await getWorkspaces();
        console.log('Fetched workspaces:', workspaces?.length);

        if (workspaces && workspaces.length > 0) {
          // Redirect to the first workspace
          const firstWorkspace = workspaces[0];
          console.log('Using first workspace:', firstWorkspace.id);

          // Save to localStorage with user-specific key if user is available
          if (user?.id) {
            const userWorkspaceKey = `workspace_${user.id}`;
            setLocalStorageData(userWorkspaceKey, firstWorkspace);
          }

          // Also save to regular workspace key for backward compatibility
          setLocalStorageData('workspace', firstWorkspace);

          setRedirectPath(`/workspace/${firstWorkspace.id}`);
        } else {
          console.log('No workspaces found, creating default workspace');
          // No workspaces found, create a default one
          await createDefaultWorkspace();
        }
      } catch (error) {
        console.error('Error redirecting to workspace:', error);

        // Check if it's a 403 error (permission issue)
        if (error.response && error.response.status === 403) {
          console.log('Permission issue detected, clearing tokens and redirecting to login');
          // Clear tokens and redirect to login
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login');
          return;
        }

        setError('Failed to load workspaces. Please try again or contact support.');
        toast({
          title: "Error loading workspaces",
          description: "Could not load your workspaces. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    redirectToWorkspace();
  }, [toast, user, isAuthenticated, navigate]);

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-8 bg-background">
        <div className="w-full max-w-md shadow-lg border border-border rounded-lg p-6">
          <div className="flex items-center gap-2 text-destructive mb-4">
            <AlertTriangle className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Workspace Error</h2>
          </div>

          <p className="text-muted-foreground mb-6">{error}</p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={() => {
                setIsLoading(true);
                setError(null);
                attemptCountRef.current = 0;
                // Force a re-render to trigger the useEffect again
                navigate('/dashboard');
              }}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>

            <Button
              variant="outline"
              onClick={() => navigate('/login')}
            >
              Go to Login
            </Button>

            <Button
              variant="outline"
              onClick={() => navigate('/projects')}
            >
              Go to Projects
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (redirectPath) {
    return <Navigate to={redirectPath} replace />;
  }

  // This should never happen, but just in case
  return <Navigate to="/projects" replace />;
};

export default DefaultWorkspaceRedirect;
