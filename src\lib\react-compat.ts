/**
 * React Compatibility Layer
 * 
 * This file ensures that React.forwardRef and other React APIs are properly
 * available throughout the application, especially when bundled for production.
 */

import * as React from 'react';

// Explicitly re-export forwardRef to ensure it's available
export const forwardRef = React.forwardRef;

// Re-export other commonly used React APIs that might be needed
export const { 
  useState, 
  useEffect, 
  useContext, 
  useRef, 
  useMemo, 
  useCallback,
  createContext,
  Fragment,
  Suspense,
  lazy,
  memo,
  createElement,
  cloneElement,
  isValidElement,
  Children
} = React;

// Export the entire React object as default
export default React;
