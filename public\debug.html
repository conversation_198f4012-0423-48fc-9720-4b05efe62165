<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Debug Page</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
    .error {
      color: red;
      font-weight: bold;
    }
    button {
      padding: 8px 16px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
  </style>
</head>
<body>
  <h1>Debug Information</h1>
  
  <h2>Environment</h2>
  <pre id="env-info">Loading...</pre>
  
  <h2>Console Output</h2>
  <pre id="console-output">No console output captured yet.</pre>
  
  <h2>Actions</h2>
  <button onclick="testRouting()">Test Routing</button>
  <button onclick="testAssetLoading()">Test Asset Loading</button>
  <button onclick="clearConsole()">Clear Console</button>
  
  <script>
    // Capture console output
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info
    };
    
    const consoleOutput = document.getElementById('console-output');
    
    function appendToConsole(type, args) {
      const timestamp = new Date().toISOString();
      const message = Array.from(args).map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      
      const line = document.createElement('div');
      line.textContent = `[${timestamp}] [${type}] ${message}`;
      if (type === 'error') {
        line.className = 'error';
      }
      
      consoleOutput.appendChild(line);
      consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }
    
    console.log = function() {
      originalConsole.log.apply(console, arguments);
      appendToConsole('log', arguments);
    };
    
    console.error = function() {
      originalConsole.error.apply(console, arguments);
      appendToConsole('error', arguments);
    };
    
    console.warn = function() {
      originalConsole.warn.apply(console, arguments);
      appendToConsole('warn', arguments);
    };
    
    console.info = function() {
      originalConsole.info.apply(console, arguments);
      appendToConsole('info', arguments);
    };
    
    // Display environment info
    function displayEnvInfo() {
      const envInfo = {
        userAgent: navigator.userAgent,
        windowLocation: window.location.href,
        windowOrigin: window.location.origin,
        windowPathname: window.location.pathname,
        windowHash: window.location.hash,
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight
      };
      
      document.getElementById('env-info').textContent = JSON.stringify(envInfo, null, 2);
    }
    
    // Test routing
    function testRouting() {
      console.log('Testing routing...');
      try {
        const testRoutes = ['/', '/projects', '/dashboard', '/login'];
        testRoutes.forEach(route => {
          const link = document.createElement('a');
          link.href = route;
          link.textContent = `Test ${route}`;
          link.style.display = 'block';
          link.style.marginBottom = '10px';
          document.body.appendChild(link);
        });
        console.log('Created test route links');
      } catch (error) {
        console.error('Error testing routing:', error);
      }
    }
    
    // Test asset loading
    function testAssetLoading() {
      console.log('Testing asset loading...');
      try {
        // Test loading CSS
        const cssTest = document.createElement('link');
        cssTest.rel = 'stylesheet';
        cssTest.href = './assets/index-f-R4QF4F.css';
        document.head.appendChild(cssTest);
        console.log('CSS loading test initiated');
        
        // Test loading JS
        const jsTest = document.createElement('script');
        jsTest.src = './assets/index-ClHyVbiV.js';
        jsTest.onload = () => console.log('JS loaded successfully');
        jsTest.onerror = (e) => console.error('JS failed to load', e);
        document.body.appendChild(jsTest);
        console.log('JS loading test initiated');
        
        // Test loading image
        const imgTest = document.createElement('img');
        imgTest.src = './assets/logo.png';
        imgTest.style.width = '100px';
        imgTest.style.height = 'auto';
        imgTest.onload = () => console.log('Image loaded successfully');
        imgTest.onerror = (e) => console.error('Image failed to load', e);
        document.body.appendChild(imgTest);
        console.log('Image loading test initiated');
      } catch (error) {
        console.error('Error testing asset loading:', error);
      }
    }
    
    // Clear console
    function clearConsole() {
      consoleOutput.textContent = '';
    }
    
    // Initialize
    window.onload = function() {
      displayEnvInfo();
      console.log('Debug page loaded');
    };
  </script>
</body>
</html>
