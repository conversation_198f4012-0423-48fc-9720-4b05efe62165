import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode, useRef } from 'react';
import {
  Notification,
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  NotificationType
} from '@/api/notificationsApi';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from './AuthContext';
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';
import { getLocalStorageData } from '@/utils/sessionLocalStorageUtil';

// Add TypeScript declaration for Pusher
declare global {
  interface Window {
    Pusher: typeof Pusher;
    Echo: Echo<any>;
  }
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  fetchNotifications: () => Promise<void>;
  markNotificationAsRead: (id: number) => Promise<void>;
  markAllNotificationsAsRead: () => Promise<void>;
  removeNotification: (id: number) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const echoRef = useRef<Echo<any> | null>(null);

  // Get a user-friendly title based on notification type
  const getNotificationTitle = (type: NotificationType) => {
    try {
      switch (type) {
        case 'task_assigned':
          return 'New Task Assignment';
        case 'task_due_soon':
          return 'Task Due Soon';
        case 'task_completed':
          return 'Task Completed';
        case 'comment_added':
          return 'New Comment';
        case 'project_invitation':
          return 'Project Invitation';
        case 'project_update':
          return 'Project Update';
        case 'mention':
          return 'You were mentioned';
        case 'role_updated':
          return 'Role Updated';
        case 'removed_from_project':
          return 'Removed from Project';
        case 'system':
          return 'System Notification';
        default:
          console.log(`Unknown notification type: ${type}`);
          return 'New Notification';
      }
    } catch (error) {
      console.error('Error getting notification title:', error);
      return 'New Notification';
    }
  };

  // Handle new notification received via WebSocket
  const handleNewNotification = useCallback((notification: Notification) => {
    try {
      console.log('Processing new notification:', notification);

      if (!notification) {
        console.error('Received empty notification');
        return;
      }

      // Add the new notification to the list
      setNotifications(prev => [notification, ...prev]);

      // Increment unread count
      setUnreadCount(prev => prev + 1);

      // Show a toast notification
      const notificationTitle = getNotificationTitle(notification.type);
      toast({
        title: notificationTitle,
        description: notification.message,
        variant: "default",
        duration: 5000,
      });
    } catch (error) {
      console.error('Error handling new notification:', error);
    }
  }, [toast]);

  // Initialize Echo for real-time notifications
  const initializeEcho = useCallback(() => {
    // Only initialize Echo if user is authenticated and we have a valid user object with ID
    if (!user || !user.id || echoRef.current) return;

    // Check for token to ensure we're authenticated
    const token = getLocalStorageData('token');
    if (!token) return;

    // Initialize Laravel Echo
    window.Pusher = Pusher;
    try {
      console.log('Initializing Echo for notifications with Pusher credentials');
      // Log Pusher credentials for debugging
      const pusherKey = import.meta.env.VITE_PUSHER_APP_KEY || '4860b3ed2c1c0628cc5a';
      const pusherCluster = import.meta.env.VITE_PUSHER_APP_CLUSTER || 'eu';
      console.log('NotificationContext - Using Pusher key:', pusherKey);
      console.log('NotificationContext - Using Pusher cluster:', pusherCluster);

      echoRef.current = new Echo({
        broadcaster: 'pusher',
        key: pusherKey,
        cluster: pusherCluster,
        forceTLS: true,
        encrypted: true,
        disableStats: true,
        authEndpoint: `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/broadcasting/auth`,
        auth: {
          headers: {
            Authorization: `Bearer ${token?.toString() || ''}`,
            Accept: 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json',
          },
        },
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      });
      console.log('Echo initialized successfully for notifications');

      // Listen for private notifications channel
      try {
        console.log(`Subscribing to private-notifications.user.${user.id} channel`);
        echoRef.current.private(`notifications.user.${user.id}`)
          .listen('.notification.created', (e: { notification: Notification }) => {
            console.log('Received notification via WebSocket:', e);
            handleNewNotification(e.notification);
          });
      } catch (error) {
        console.error('Error setting up notification listener:', error);
      }
    } catch (error) {
      console.error('Failed to initialize Echo for notifications:', error);
      // Reset echoRef if initialization fails
      echoRef.current = null;
    }
  }, [user, handleNewNotification]);

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    try {
      const data = await getNotifications();
      setNotifications(data);

      // Update unread count
      const count = await getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast({
        title: "Failed to load notifications",
        description: "Could not retrieve your notifications. Please try again later.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, toast]);

  // Mark a notification as read
  const markNotificationAsRead = useCallback(async (id: number) => {
    try {
      await markAsRead(id);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id
            ? { ...notification, read: true }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllNotificationsAsRead = useCallback(async () => {
    try {
      await markAllAsRead();

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      // Reset unread count
      setUnreadCount(0);

      toast({
        title: "All notifications marked as read",
        description: "Your notifications have been updated.",
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, [toast]);

  // Remove a notification
  const removeNotification = useCallback(async (id: number) => {
    try {
      await deleteNotification(id);

      // Update local state
      const notificationToRemove = notifications.find(n => n.id === id);
      setNotifications(prev => prev.filter(notification => notification.id !== id));

      // Update unread count if the removed notification was unread
      if (notificationToRemove && !notificationToRemove.read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error removing notification:', error);
    }
  }, [notifications]);

  // Fetch notifications on initial load and when auth state changes
  useEffect(() => {
    if (isAuthenticated && user) {
      // Make sure we have both authentication and a valid user object
      fetchNotifications();
      initializeEcho();
    } else {
      // If not authenticated, reset notifications state
      setNotifications([]);
      setUnreadCount(0);

      // Disconnect Echo if it exists
      if (echoRef.current) {
        try {
          console.log('Disconnecting Echo for notifications - user not authenticated');
          echoRef.current.disconnect();
          echoRef.current = null;
        } catch (error) {
          console.error('Error disconnecting Echo for notifications:', error);
        }
      }
    }

    // Cleanup Echo on unmount
    return () => {
      try {
        if (echoRef.current) {
          console.log('Disconnecting Echo for notifications');
          echoRef.current.disconnect();
          echoRef.current = null;
        }
      } catch (error) {
        console.error('Error disconnecting Echo for notifications:', error);
      }
    };
  }, [isAuthenticated, user, fetchNotifications, initializeEcho]);

  // Set up polling for new notifications (every 30 seconds) as fallback
  useEffect(() => {
    // Only set up polling if user is authenticated and we have a valid user object
    if (!isAuthenticated || !user) return;

    // Check for token to ensure we're authenticated
    const token = getLocalStorageData('token');
    if (!token) return;

    const interval = setInterval(() => {
      // Wrap in try/catch to prevent unhandled promise rejections
      try {
        getUnreadCount().then(count => {
          if (count > unreadCount) {
            // If there are new notifications, fetch them
            fetchNotifications();
          } else {
            // Otherwise just update the count
            setUnreadCount(count);
          }
        }).catch(error => {
          console.error('Error in notification polling:', error);
        });
      } catch (error) {
        console.error('Error setting up notification polling:', error);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isAuthenticated, user, unreadCount, fetchNotifications]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        isLoading,
        fetchNotifications,
        markNotificationAsRead,
        markAllNotificationsAsRead,
        removeNotification,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
