import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { User } from '@/entities/User';
import { Project } from '@/entities/Project';
import { Role } from '@/entities/Role';
import { Users, Search, Plus, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  removeProjectUser,
  getRoles,
  sendProjectInvitation,
  getProjectInvitations,
  cancelProjectInvitation
} from '@/api/projectsApi';
import { searchUserByEmail } from '@/api/usersApi';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission, getRoleName, RoleId } from '@/utils/permissionUtils';
import { getFullImageUrl } from '@/utils/imageUtils';

interface ProjectInvitation {
  id: number;
  project_id: number;
  invited_by_user_id: number;
  email: string;
  role_id: number;
  token: string;
  expires_at: string;
  created_at: string;
  updated_at: string;
  invitedBy?: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    profile_picture?: string;
  };
  role?: {
    id: number;
    name: string;
  };
}

interface TeamMembersModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | undefined;
  onMemberUpdated: () => void;
}

const TeamMembersModal: React.FC<TeamMembersModalProps> = ({
  isOpen,
  onClose,
  project,
  onMemberUpdated
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddMember, setShowAddMember] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState('');
  // Default to Editor role (ID: 2)
  const [newMemberRole, setNewMemberRole] = useState<string>("2");
  console.log('Initial newMemberRole state:', newMemberRole);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [pendingInvitations, setPendingInvitations] = useState<ProjectInvitation[]>([]);
  const [isLoadingInvitations, setIsLoadingInvitations] = useState(false);
  const [showInvitations, setShowInvitations] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const canViewMembers = hasProjectPermission(project, user, ProjectPermission.VIEW_MEMBERS);
  const canAddMember = hasProjectPermission(project, user, ProjectPermission.ADD_MEMBER);
  const canRemoveMember = hasProjectPermission(project, user, ProjectPermission.REMOVE_MEMBER);
  const canChangeRole = hasProjectPermission(project, user, ProjectPermission.CHANGE_MEMBER_ROLE);

  // Debug permissions in development mode - using useEffect to prevent infinite renders
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && project) {
      console.log('TeamMembersModal Permissions:', {
        canViewMembers,
        canAddMember,
        canRemoveMember,
        canChangeRole,
        roleId: project?.members?.find(m => m.id === user?.id)?.pivot?.role_id
      });
    }
  }, [canViewMembers, canAddMember, canRemoveMember, canChangeRole, project, user]);

  // Fetch available roles and pending invitations when component mounts
  // Use a ref to track if we've already fetched data to prevent duplicate requests
  const dataFetchedRef = React.useRef(false);

  useEffect(() => {
    // Only fetch data when the modal is open and we have a project
    if (!isOpen || !project) {
      dataFetchedRef.current = false;
      return;
    }

    // Skip if we've already fetched data for this session
    if (dataFetchedRef.current) return;

    const fetchData = async () => {
      try {
        setIsLoadingRoles(true);
        setIsLoadingInvitations(true);
        dataFetchedRef.current = true;

        // Fetch roles
        const roles = await getRoles();
        console.log('Fetched roles:', roles);
        setAvailableRoles(roles);

        // Fetch pending invitations
        try {
          const invitations = await getProjectInvitations(project.id);
          setPendingInvitations(invitations || []);
        } catch (invitationError) {
          console.error('Failed to load invitations:', invitationError);
          // Don't show an error toast for this, as it's not critical
          setPendingInvitations([]);
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load data. Please try again.",
          variant: "destructive"
        });
        dataFetchedRef.current = false; // Reset so we can try again
      } finally {
        setIsLoadingRoles(false);
        setIsLoadingInvitations(false);
      }
    };

    fetchData();

    // Clean up function to reset the ref when the component unmounts
    return () => {
      dataFetchedRef.current = false;
    };
  }, [isOpen, project, toast]);

  if (!project) return null;

  // Check if this is a group project
  const isGroupProject = project.is_group_project;

  const members = project.members || [];

  const filteredMembers = members.filter(member =>
    member.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddMember = async () => {
    if (!project || !newMemberEmail) return;

    setIsSubmitting(true);

    try {
      // Make sure to convert the role ID to an integer
      const roleId = Number(newMemberRole);

      // Validate that roleId is a valid number (1, 2, or 3)
      if (isNaN(roleId) || ![1, 2, 3].includes(roleId)) {
        console.error(`Invalid role ID: ${roleId} (original value: ${newMemberRole})`);
        toast({
          title: "Error",
          description: "Please select a valid role (Admin, Editor, or Viewer).",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      console.log(`Sending invitation with role ID: ${roleId} (type: ${typeof roleId})`);

      // Ensure roleId is a valid integer (1, 2, or 3)
      const validRoleId = roleId === 1 ? 1 : (roleId === 2 ? 2 : 3);

      // Send an invitation to the email address
      const response = await sendProjectInvitation(
        project.id,
        newMemberEmail,
        validRoleId
      );

      // If the response has an error property, it means the API call failed but already showed a toast
      if (response && response.error) {
        // Just reset the form state, don't show another toast
        setNewMemberEmail('');
        setNewMemberRole('2'); // Reset to Editor role (ID: 2)
        return;
      }

      // Check if the user exists (this info comes from the backend)
      const userExists = response.user_exists;

      toast({
        title: "Invitation sent",
        description: userExists
          ? `An invitation has been sent to ${newMemberEmail}. They will be added to the project once they accept.`
          : `An invitation has been sent to ${newMemberEmail}. They will need to create an account and accept the invitation.`,
      });

      // Refresh the invitations list
      const invitations = await getProjectInvitations(project.id);
      setPendingInvitations(invitations);

      // Reset form state
      setNewMemberEmail('');
      setNewMemberRole('2'); // Reset to Editor role (ID: 2)
      setShowAddMember(false);
      setShowInvitations(true); // Show the invitations section
    } catch (error: any) {
      // This catch block should rarely be hit now since we're handling errors in the API function
      // But we'll keep it as a fallback
      console.error("Unhandled error in handleAddMember:", error);

      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveMember = async (userId: number) => {
    if (!project) return;

    try {
      await removeProjectUser(project.id, userId);

      toast({
        title: "Member removed",
        description: "User has been successfully removed from the project.",
      });

      onMemberUpdated();
    } catch (error: any) {
      let errorMessage = "Failed to remove user from the project.";

      if (error.response?.status === 403) {
        errorMessage = "You don't have permission to remove members from this project.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleCancelInvitation = async (invitationId: number) => {
    if (!project) return;

    try {
      await cancelProjectInvitation(project.id, invitationId);

      toast({
        title: "Invitation cancelled",
        description: "The invitation has been cancelled successfully.",
      });

      // Refresh the invitations list
      const invitations = await getProjectInvitations(project.id);
      setPendingInvitations(invitations);
    } catch (error: any) {
      let errorMessage = "Failed to cancel the invitation.";

      if (error.response?.status === 403) {
        errorMessage = "You don't have permission to cancel invitations for this project.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  // Get role name based on the unified role system
  const getRoleNameFromId = (roleId: number, memberId?: number) => {
    // Make sure we're working with a number
    const numericRoleId = Number(roleId);

    // Handle special cases
    if (!roleId || roleId === 0) {
      // If the user is the project owner, they're an admin
      if (project && memberId && memberId === project.user_id) {
        return 'Admin';
      }
      // Default to viewer for unknown roles
      return 'Viewer';
    }

    // Use the utility function from permissionUtils
    return getRoleName(numericRoleId as RoleId);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Project Members</DialogTitle>
          <DialogDescription>
            Manage team members and their roles for this project.
          </DialogDescription>
        </DialogHeader>

        {!isGroupProject ? (
          <div className="py-6 text-center">
            <Users size={48} className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">This is a personal project</h3>
            <p className="text-muted-foreground mb-4">
              This project is not set up for team collaboration. Convert it to a group project to add team members.
            </p>
            {project.user_id === user?.id && (
              <Button
                onClick={() => {
                  onClose();
                  // We'll need to handle this conversion from the parent component
                  // For now, just show a toast message
                  toast({
                    title: "Action required",
                    description: "Please use the 'Convert to Group Project' button on the project page.",
                  });
                }}
              >
                Convert to Group Project
              </Button>
            )}
          </div>
        ) : canViewMembers && (!canAddMember && !canRemoveMember && !canChangeRole) ? (
          <>
            <div className="py-4 text-center mb-4">
              <h3 className="text-lg font-medium mb-2">View-Only Access</h3>
              <p className="text-muted-foreground">
                As a viewer, you can see the team members but cannot add, remove, or change roles of team members.
              </p>
            </div>

            <div className="relative flex-1 mr-2 mb-4">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search members..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>

            <div className="max-h-[400px] overflow-y-auto">
              <div className="space-y-2">
                {filteredMembers.length > 0 ? (
                  filteredMembers.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/10 transition-colors"
                    >
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full overflow-hidden bg-primary/10 dark:bg-white/90 flex items-center justify-center">
                          {member.profile_picture ? (
                            <img
                              src={getFullImageUrl(member.profile_picture)}
                              alt={`${member.first_name} ${member.last_name}`}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <Users size={20} className="text-primary dark:text-primary-foreground" />
                          )}
                        </div>
                        <div className="ml-3">
                          <h4 className="font-medium">{member.first_name} {member.last_name}</h4>
                          <p className="text-xs text-muted-foreground">{member.email}</p>
                          <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full mt-1 inline-block">
                            {member.role_name || getRoleNameFromId(member.pivot?.role_id || 0, member.id)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No members found matching your search criteria.
                  </div>
                )}
              </div>
            </div>
          </>
        ) : canViewMembers ? (
          <>
            <div className="flex justify-between items-center mb-4">
              <div className="relative flex-1 mr-2">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search members..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <div className="flex gap-2">
                {pendingInvitations.length > 0 && (
                  <Button
                    variant={showInvitations ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setShowInvitations(!showInvitations);
                      if (showAddMember) setShowAddMember(false);
                    }}
                  >
                    Pending ({pendingInvitations.length})
                  </Button>
                )}
                {canAddMember && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowAddMember(!showAddMember);
                      if (showInvitations) setShowInvitations(false);
                    }}
                  >
                    <Plus size={16} className="mr-1" />
                    Invite Member
                  </Button>
                )}
              </div>
            </div>

            {/* Always show the members list for editors and admins */}
            <div className="max-h-[400px] overflow-y-auto">
              <div className="space-y-2">
                {filteredMembers.length > 0 ? (
                  filteredMembers.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/10 transition-colors"
                    >
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full overflow-hidden bg-primary/10 dark:bg-white/90 flex items-center justify-center">
                          {member.profile_picture ? (
                            <img
                              src={getFullImageUrl(member.profile_picture)}
                              alt={`${member.first_name} ${member.last_name}`}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <Users size={20} className="text-primary dark:text-primary-foreground" />
                          )}
                        </div>
                        <div className="ml-3">
                          <h4 className="font-medium">{member.first_name} {member.last_name}</h4>
                          <p className="text-xs text-muted-foreground">{member.email}</p>
                          <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full mt-1 inline-block">
                            {member.role_name || getRoleNameFromId(member.pivot?.role_id || 0, member.id)}
                          </span>

                        </div>
                      </div>

                      {/* Only show delete button if:
                          1. Not removing the project creator
                          2. Current user is the project owner OR has REMOVE_MEMBER permission
                      */}
                      {project &&
                       member.id !== project.user_id &&
                       (user?.id === project.user_id ||
                        (user && hasProjectPermission(project, user, ProjectPermission.REMOVE_MEMBER))) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveMember(member.id)}
                        >
                          <Trash2 size={16} className="text-destructive" />
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No members found matching your search criteria.
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="py-6 text-center">
            <Users size={48} className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Access</h3>
            <p className="text-muted-foreground mb-4">
              You don't have permission to view team members for this project.
            </p>
          </div>
        )}

        {isGroupProject && (
          <>
            {showInvitations && pendingInvitations.length > 0 && (
              <div className="bg-muted/30 p-4 rounded-lg mb-4 border border-border">
                <h3 className="text-sm font-medium mb-2">Pending Invitations</h3>
                <div className="space-y-2 max-h-[200px] overflow-y-auto">
                  {pendingInvitations.map((invitation) => (
                    <div
                      key={invitation.id}
                      className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/10 transition-colors"
                    >
                      <div className="flex-1">
                        <p className="font-medium text-sm">{invitation.email}</p>
                        <div className="flex items-center text-xs text-muted-foreground mt-1">
                          <span className="bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                            {invitation.role?.name || getRoleNameFromId(invitation.role_id)}
                          </span>
                          <span className="ml-2">
                            Invited {new Date(invitation.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCancelInvitation(invitation.id)}
                      >
                        <Trash2 size={16} className="text-destructive" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {showAddMember && (
              <div className="bg-muted/30 p-4 rounded-lg mb-4 border border-border">
                <h3 className="text-sm font-medium mb-2">Add New Member</h3>
                <div className="space-y-3">
                  <div>
                    <Input
                      placeholder="Email address"
                      value={newMemberEmail}
                      onChange={(e) => setNewMemberEmail(e.target.value)}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Select
                        value={newMemberRole}
                        defaultValue="2"
                        onValueChange={(value) => {
                          console.log('Selected role value:', value, 'type:', typeof value);
                          // Ensure value is one of the valid role IDs
                          if (['1', '2', '3'].includes(value)) {
                            setNewMemberRole(value);
                          } else {
                            console.error('Invalid role value selected:', value);
                            // Default to Editor if invalid value
                            setNewMemberRole('2');
                          }
                        }}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem key="admin-role" value="1">Admin</SelectItem>
                          <SelectItem key="editor-role" value="2">Editor</SelectItem>
                          <SelectItem key="viewer-role" value="3">Viewer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      onClick={handleAddMember}
                      disabled={!newMemberEmail || isSubmitting}
                    >
                      {isSubmitting ? 'Adding...' : 'Add'}
                    </Button>
                  </div>
                </div>
              </div>
            )}


          </>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TeamMembersModal;
