import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { format } from 'date-fns';
import { CalendarIcon, Loader2, Users, Repeat } from 'lucide-react';

import Navbar from '@/components/Navbar';
import Loader from '@/components/Loader';
import RecurringEventForm from '@/components/calendar/RecurringEventForm';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { createCalendarEvent } from '@/services/calendarApi';
import { getAllProjects } from '@/api/projectsApi';
import { getAllUsers } from '@/api/usersApi';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import { RecurringEventFormData } from '@/types/calendar';

const CreateEvent = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { date } = useParams<{ date?: string }>();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const projectIdParam = queryParams.get('project_id');

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [showAssignees, setShowAssignees] = useState(false);
  const [showRecurring, setShowRecurring] = useState(false);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<string>('task');
  const [priority, setPriority] = useState<string>('medium');
  const [status, setStatus] = useState<string>('not-started');
  const [projectId, setProjectId] = useState<string>(projectIdParam || '');
  const [startDate, setStartDate] = useState<Date | undefined>(
    date ? new Date(date) : new Date()
  );
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [assignees, setAssignees] = useState<number[]>([]);
  const [recurringEventData, setRecurringEventData] = useState<RecurringEventFormData | null>(null);

  // Load projects and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [projectsData, usersData] = await Promise.all([
          getAllProjects(),
          getAllUsers()
        ]);

        setProjects(projectsData);
        setUsers(usersData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title || !startDate) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSaving(true);

      const eventData = {
        title,
        description,
        type,
        priority,
        status,
        project_id: projectId && projectId !== 'none' ? parseInt(projectId) : undefined,
        start_date: startDate ? format(startDate, 'yyyy-MM-dd HH:mm:ss') : undefined,
        end_date: endDate ? format(endDate, 'yyyy-MM-dd HH:mm:ss') : undefined,
        assignees: assignees.length > 0 ? assignees : undefined,
      };

      // Add recurring event data if enabled
      if (showRecurring && recurringEventData) {
        Object.assign(eventData, {
          is_recurring: true,
          recurring_frequency: recurringEventData.recurring_frequency,
          recurring_interval: recurringEventData.recurring_interval,
          recurring_days_of_week: recurringEventData.recurring_days_of_week,
          recurring_day_of_month: recurringEventData.recurring_day_of_month,
          recurring_month_of_year: recurringEventData.recurring_month_of_year,
          recurring_ends_on: recurringEventData.recurring_ends_on,
          recurring_count: recurringEventData.recurring_count,
        });
      }

      await createCalendarEvent(eventData);

      toast({
        title: 'Success',
        description: 'Event created successfully',
      });

      // Navigate back to calendar
      navigate('/calendar');
    } catch (error) {
      console.error('Error creating event:', error);
      toast({
        title: 'Error',
        description: 'Failed to create event',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle recurring event form submission
  const handleRecurringSubmit = (data: RecurringEventFormData) => {
    setRecurringEventData(data);
  };

  // Handle assignee selection
  const toggleAssignee = (userId: number) => {
    setAssignees(prev => {
      if (prev.includes(userId)) {
        return prev.filter(id => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Loader />
        </div>
      )}

      <main className="container max-w-4xl mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Create New Event</h1>
          <Button variant="outline" onClick={() => navigate('/calendar')}>
            Cancel
          </Button>
        </div>

        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle>Event Details</CardTitle>
              <CardDescription>
                Enter the details for your new event
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="space-y-2">
                <label htmlFor="title" className="text-sm font-medium">
                  Event Title *
                </label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter event title"
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium">
                  Description
                </label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter event description"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="type" className="text-sm font-medium">
                    Event Type
                  </label>
                  <Select value={type} onValueChange={setType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select event type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="task">Task</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="deadline">Deadline</SelectItem>
                      <SelectItem value="milestone">Milestone</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="project" className="text-sm font-medium">
                    Project
                  </label>
                  <Select value={projectId} onValueChange={setProjectId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select project (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Project</SelectItem>
                      {projects?.map((project) => (
                        <SelectItem key={project.id} value={project.id.toString()}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="priority" className="text-sm font-medium">
                    Priority
                  </label>
                  <Select value={priority} onValueChange={setPriority}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="status" className="text-sm font-medium">
                    Status
                  </label>
                  <Select value={status} onValueChange={setStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="not-started">Not Started</SelectItem>
                      <SelectItem value="in-progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Start Date *
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, 'PPP') : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={(date) => {
                            setStartDate(date);
                            // If end date is before the new start date, update it
                            if (endDate && date && date > endDate) {
                              setEndDate(date);
                            }
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      End Date
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, 'PPP') : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                          disabled={(date) => startDate ? date < startDate : false}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {endDate && startDate && (
                  <div className="bg-muted/50 p-3 rounded-md">
                    <div className="flex items-center text-sm">
                      <span className="font-medium mr-2">Duration:</span>
                      {startDate && endDate && (
                        <span>
                          {format(startDate, 'MMM d, yyyy')} - {format(endDate, 'MMM d, yyyy')}
                          {' '}
                          ({Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1} days)
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="flex flex-col space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="assignees"
                      checked={showAssignees}
                      onCheckedChange={(checked) => setShowAssignees(checked as boolean)}
                    />
                    <label
                      htmlFor="assignees"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Assign Users
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="recurring"
                      checked={showRecurring}
                      onCheckedChange={(checked) => setShowRecurring(checked as boolean)}
                    />
                    <label
                      htmlFor="recurring"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                    >
                      <Repeat className="h-4 w-4 mr-2" />
                      Make this a recurring event
                    </label>
                  </div>
                </div>

                {showAssignees && (
                  <div className="border rounded-md p-4 space-y-3">
                    <h4 className="text-sm font-medium">Select Assignees</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                      {users.map(user => (
                        <div key={`user-${user.id}`} className="flex items-center space-x-2">
                          <Checkbox
                            id={`user-${user.id}`}
                            checked={assignees.includes(user.id)}
                            onCheckedChange={() => toggleAssignee(user.id)}
                          />
                          <label
                            htmlFor={`user-${user.id}`}
                            className="text-sm leading-none flex items-center space-x-2"
                          >
                            <Avatar className="h-6 w-6">
                              <AvatarFallback>{user.name ? user.name.charAt(0) : '?'}</AvatarFallback>
                            </Avatar>
                            <span>{user.name}</span>
                          </label>
                        </div>
                      ))}
                    </div>
                    {users.length === 0 && (
                      <p className="text-sm text-muted-foreground">No users available</p>
                    )}
                  </div>
                )}

                {showRecurring && startDate && (
                  <div className="border rounded-md p-4">
                    <RecurringEventForm
                      startDate={startDate}
                      initialValues={recurringEventData || undefined}
                      onSubmit={handleRecurringSubmit}
                      onCancel={() => setShowRecurring(false)}
                    />
                  </div>
                )}
              </div>
            </CardContent>

            <CardFooter className="flex justify-end space-x-3">
              <Button
                type="submit"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Event'
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </main>
    </div>
  );
};

export default CreateEvent;
