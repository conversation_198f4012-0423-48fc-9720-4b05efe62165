import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Home, ArrowLeft, LogOut } from 'lucide-react';
import Navbar from '@/components/Navbar';
import { useTranslation } from 'react-i18next';

const LeaveProjectSuccess = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  
  // Get project information from location state
  const { projectName } = location.state || { projectName: t('project.generic', 'the project') };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="flex flex-col items-center justify-center p-8 mt-8">
        <Card className="w-full max-w-md shadow-lg border border-border">
          <CardHeader className="text-center pb-2">
            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-green-100 mb-4">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-foreground mb-2">
              {t('project.leave.success.title', 'Project Left Successfully')}
            </CardTitle>
            <div className="w-16 h-1 bg-green-500 mx-auto my-4 rounded-full"></div>
          </CardHeader>

          <CardContent className="text-center">
            <p className="text-lg font-medium text-foreground mb-2">
              {t('project.leave.success.message', 'You have successfully left')} <span className="font-bold">{projectName}</span>
            </p>
            <p className="text-muted-foreground mb-6">
              {t('project.leave.success.description', 'You no longer have access to this project. You can continue working on your other projects or create a new one.')}
            </p>
          </CardContent>

          <CardFooter className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild variant="default" className="w-full sm:w-auto">
              <div onClick={() => navigate('/dashboard')}>
                <Home className="mr-2 h-4 w-4" />
                {t('common.dashboard', 'Dashboard')}
              </div>
            </Button>
            <Button asChild variant="outline" className="w-full sm:w-auto">
              <div onClick={() => navigate('/projects')}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t('common.allProjects', 'All Projects')}
              </div>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default LeaveProjectSuccess;
