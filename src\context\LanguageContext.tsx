import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { getLocalStorageData, setLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import i18n from '@/i18n';
import { useAuth } from './AuthContext';
import api from '@/api/api';
import { toast } from '@/components/ui/use-toast';

type Language = 'en' | 'es' | 'fr' | 'tr';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, isAuthenticated, updateProfile } = useAuth();
  const [language, setLanguageState] = useState<Language>(() => {
    // If user is logged in and has a language preference, use that
    if (user?.language) {
      return user.language as Language;
    }
    // Otherwise, try to get from localStorage
    const savedLanguage = getLocalStorageData('language') as Language;
    return savedLanguage || 'en';
  });

  // Update language in both localStorage and database (if user is logged in)
  const setLanguage = async (newLanguage: Language) => {
    setLanguageState(newLanguage);

    // Always update localStorage for quick access on page refresh
    setLocalStorageData('language', newLanguage, 365 * 24 * 60 * 60 * 1000); // Store for 1 year

    // Change i18n language
    i18n.changeLanguage(newLanguage);

    // If user is logged in, update their preference in the database
    if (isAuthenticated && user) {
      try {
        // Update user profile with new language preference
        // Include all required fields to satisfy validation
        await updateProfile({
          language: newLanguage,
          first_name: user.first_name,
          last_name: user.last_name,
          // Include other fields that might be required
          job_title: user.job_title,
          phone: user.phone,
          bio: user.bio,
          birth_date: user.birth_date
        });
      } catch (error) {
        console.error('Failed to update language preference in database:', error);
        toast({
          title: "Warning",
          description: "Your language preference was saved locally but couldn't be saved to your account.",
          variant: "destructive"
        });
      }
    }
  };

  // Initialize language based on user preference or saved preference
  useEffect(() => {
    // If user is logged in and has a language preference, use that
    if (user?.language) {
      // Only update if the language is different to avoid infinite loops
      if (user.language !== language) {
        // Just update the state and i18n, don't call the API again
        setLanguageState(user.language as Language);
        i18n.changeLanguage(user.language as Language);
        // Also update localStorage for consistency
        setLocalStorageData('language', user.language as Language, 365 * 24 * 60 * 60 * 1000);
      }
      return;
    }

    // Otherwise, try to get from localStorage
    const savedLanguage = getLocalStorageData('language') as Language;
    if (savedLanguage && savedLanguage !== language) {
      // Just update the state and i18n, don't call the API
      setLanguageState(savedLanguage);
      i18n.changeLanguage(savedLanguage);
    } else if (!savedLanguage) {
      // If no saved language, use browser language or default to English
      const browserLang = navigator.language.split('-')[0];
      const supportedLang = ['en', 'es', 'fr', 'tr'].includes(browserLang) ? browserLang as Language : 'en';

      // Just update the state and i18n, don't call the API
      setLanguageState(supportedLang);
      i18n.changeLanguage(supportedLang);
      // Also update localStorage
      setLocalStorageData('language', supportedLang, 365 * 24 * 60 * 60 * 1000);
    }
  }, [user, language]); // Re-run when user or language changes

  return (
    <LanguageContext.Provider value={{ language, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};
