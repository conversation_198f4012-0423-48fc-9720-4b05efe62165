import React, { useEffect, useState } from 'react';
import { ArrowUpRight, FileCheck } from 'lucide-react';
import ProjectCard, { ProjectCardProps } from '../ProjectCard';
import { Project, ProjectProgress } from '@/entities/Project';
import { useNavigate } from 'react-router-dom';
import { TurkishDateHelper } from '@/utils/dateUtils';


interface ProjectSectionProps {
    projects: Project[];
}

type StatusFilter = 'all' | 'completed' | 'in-progress' | 'not-started';


const WorkspaceProjectSection: React.FC<ProjectSectionProps> = ({
    projects
}) => {
    const navigate = useNavigate();
    const [filteredProjects, setFilteredProjects] = useState<Project[]>(projects);
    const [filter, setFilter] = useState<StatusFilter>('all');

    // Helper function to map Project to ProjectCardProps
    const mapProjectToCardProps = (project: Project): ProjectCardProps => {
        // Convert progress from string to number if needed
        const progressNumber = typeof project.progress === 'string'
            ? parseFloat(project.progress) || 0
            : project.progress || 0;

        return {
            id: project.id.toString(),
            title: project.name,
            description: project.description || '',
            progress: progressNumber,
            dueDate: project.end_date ? TurkishDateHelper.formatDate(project.end_date) : 'No date',
            priority: (project.priority as 'low' | 'medium' | 'high') || 'medium',
            status: (project.status as 'completed' | 'in-progress' | 'not-started' | 'at-risk') || 'not-started',
            members: project.members || [],
            isStarred: project.is_starred || false,
            is_group_project: project.is_group_project, // Added this property
            onStar: () => {}, // Empty function as we don't handle starring here
        };
    };

    const resetFilter = () => {
        if (filter !== 'all') {
            setFilter('all');
            setFilteredProjects(projects);
        }
    }



    const inProgress = () => {
        if (filter !== 'in-progress') {
            setFilter('in-progress');
            setFilteredProjects(projects.filter(project => {
                if (!project.progress) return false;
                return project.progress === ProjectProgress.InProgress;
            }));
        }
    }


    const completedProjects = () => {
        if (filter !== 'completed') {
            setFilter('completed');
            setFilteredProjects(projects.filter(project => {
                if (!project.progress) return false;
                return project.progress === ProjectProgress.Completed;
            }));
        }
    }


    const notStarted = () => {
        if (filter !== 'not-started') {
            setFilter('not-started');
            setFilteredProjects(projects.filter(project => {
                if (!project.progress) return false;
                return project.progress === ProjectProgress.NotStarted;
            }));
        }
    }


    useEffect(() => {
        setFilteredProjects(projects);
    }, [projects]);




    return (
        <>
            <div className="bg-card border border-border rounded-xl shadow-subtle overflow-hidden mb-6">
                <div className="p-5 border-b border-border flex flex-col sm:flex-row sm:items-center justify-between">
                    <h2 className="text-xl font-semibold mb-3 sm:mb-0">Projects Overview</h2>
                    <div className="flex flex-wrap gap-2">
                        <button
                            onClick={resetFilter}
                            className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'all' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                }`}
                        >
                            All Projects
                        </button>
                        <button
                            onClick={inProgress}
                            className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'in-progress' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                }`}
                        >
                            In Progress
                        </button>
                        <button
                            onClick={completedProjects}
                            className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'completed' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                }`}
                        >
                            Completed
                        </button>
                        <button
                            onClick={notStarted}
                            className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'not-started' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                }`}
                        >
                            Not Started
                        </button>
                    </div>
                </div>

                <div className="p-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                        {filteredProjects.length > 0 ? (
                            filteredProjects.map((project) => (
                                <ProjectCard key={project.id} {...project} />
                            ))
                        ) : (
                            <div className="md:col-span-2 py-8 flex flex-col items-center justify-center text-center">
                                <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-3">
                                    <FileCheck className="h-8 w-8 text-muted-foreground" />
                                </div>
                                <h3 className="text-lg font-medium text-foreground mb-1">No projects found</h3>
                                <p className="text-muted-foreground max-w-md">
                                    No projects match your current filter. Try selecting a different status or create a new project.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
};

export default WorkspaceProjectSection;
