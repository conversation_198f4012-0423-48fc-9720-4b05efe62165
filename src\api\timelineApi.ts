import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';

// Timeline item types
export interface TimelineItem {
  id: string;
  type: 'project' | 'task' | 'milestone';
  title: string;
  start_date: string | null;
  end_date: string | null;
  status: string;
  priority?: string;
  owner?: {
    id: number;
    name: string;
    avatar?: string;
  };
  assignee?: {
    id: number;
    name: string;
    avatar?: string;
  };
  color: string;
  project_id: number;
  task_id?: number;
}

export interface TimelineData {
  timeline_items: TimelineItem[];
  date_range: {
    start_date: string;
    end_date: string;
  };
  team_members?: {
    id: number;
    name: string;
    avatar?: string;
  }[];
}

export interface MilestoneData {
  title: string;
  date: string;
  description?: string;
  list_id?: number;
}

// Get workspace timeline
export const getWorkspaceTimeline = async (
  workspaceId: number,
  startDate?: string,
  endDate?: string,
  status?: string
): Promise<TimelineData> => {
  try {
    const params: Record<string, string> = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    if (status) params.status = status;

    console.log('API call: getWorkspaceTimeline with params:', { workspaceId, params });

    const response = await api.get(`/timeline/workspace/${workspaceId}`, { params });
    console.log('API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('API error in getWorkspaceTimeline:', error);
    return handleApiError(
      error,
      "Failed to fetch workspace timeline",
      "Could not load workspace timeline data"
    );
  }
};

// Get project timeline
export const getProjectTimeline = async (
  projectId: number,
  startDate?: string,
  endDate?: string,
  status?: string,
  assigneeId?: number
): Promise<TimelineData> => {
  try {
    const params: Record<string, string> = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    if (status) params.status = status;
    if (assigneeId) params.assignee_id = assigneeId.toString();

    const response = await api.get(`/timeline/project/${projectId}`, { params });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch project timeline",
      "Could not load project timeline data"
    );
  }
};

// Update timeline item dates
export const updateTimelineItem = async (
  itemId: string,
  startDate: string,
  endDate: string
): Promise<{ id: string; start_date: string; end_date: string }> => {
  try {
    const response = await api.put('/timeline/item', {
      item_id: itemId,
      start_date: startDate,
      end_date: endDate,
    });
    return response.data.item;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to update timeline item",
      "Could not update timeline item dates"
    );
  }
};

// Create a milestone
export const createMilestone = async (
  projectId: number,
  milestoneData: MilestoneData
): Promise<TimelineItem> => {
  try {
    const response = await api.post(`/timeline/project/${projectId}/milestone`, milestoneData);
    return response.data.milestone;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to create milestone",
      "Could not create project milestone"
    );
  }
};
