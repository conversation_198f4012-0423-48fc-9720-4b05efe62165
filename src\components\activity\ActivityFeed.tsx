import React, { useState, useEffect } from 'react';
import {
  Activity,
  User,
  FileText,
  CheckCircle,
  MessageSquare,
  Clock,
  Filter,
  RefreshCw,
  AlertTriangle,
  Edit,
  Plus,
  Trash2
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getRecentActivities, ActivityItem, ActivityFilter } from '@/api/activitiesApi';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { getFullImageUrl } from '@/utils/imageUtils';

// Activity icon component with improved styling
const ActivityIcon = ({ type }: { type: ActivityItem['type'] }) => {
  const iconClasses = "h-4 w-4";

  switch (type) {
    case 'project_created':
      return <FileText className={cn(iconClasses, "text-info")} />;
    case 'task_completed':
      return <CheckCircle className={cn(iconClasses, "text-success")} />;
    case 'user_joined':
      return <User className={cn(iconClasses, "text-primary")} />;
    case 'comment_added':
      return <MessageSquare className={cn(iconClasses, "text-accent")} />;
    default:
      return <Activity className={cn(iconClasses, "text-muted-foreground")} />;
  }
};

// Activity type badge component
const ActivityTypeBadge = ({ type }: { type: ActivityItem['type'] }) => {
  const { t } = useTranslation();

  const typeConfig = {
    'project_created': {
      label: t('activity.projectCreated', 'Project'),
      variant: 'info-subtle' as const,
      icon: <FileText className="h-3 w-3 mr-1" />
    },
    'task_completed': {
      label: t('activity.taskCompleted', 'Task'),
      variant: 'success-subtle' as const,
      icon: <CheckCircle className="h-3 w-3 mr-1" />
    },
    'user_joined': {
      label: t('activity.userJoined', 'Team'),
      variant: 'primary-subtle' as const,
      icon: <User className="h-3 w-3 mr-1" />
    },
    'comment_added': {
      label: t('activity.commentAdded', 'Comment'),
      variant: 'accent-subtle' as const,
      icon: <MessageSquare className="h-3 w-3 mr-1" />
    }
  };

  const config = typeConfig[type] || {
    label: t('activity.unknown', 'Activity'),
    variant: 'outline' as const,
    icon: <Activity className="h-3 w-3 mr-1" />
  };

  return (
    <Badge variant={config.variant} className="text-xs font-normal px-1.5 py-0.5 gap-0.5">
      {config.icon}
      {config.label}
    </Badge>
  );
};

// Activity message component with improved styling
const ActivityMessage = ({ item }: { item: ActivityItem }) => {
  const { t } = useTranslation();

  switch (item.type) {
    case 'project_created':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span>{' '}
          {t('activity.createdProject', 'created a new project')}{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    case 'task_completed':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span>{' '}
          {t('activity.completedTask', 'completed task')}{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    case 'user_joined':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span>{' '}
          {t('activity.joinedProject', 'joined project')}{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    case 'comment_added':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span>{' '}
          {t('activity.commentedOn', 'commented on')}{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    default:
      return <span>{t('activity.unknownActivity', 'Unknown activity')}</span>;
  }
};

interface ActivityFeedProps {
  projectId?: number;
  limit?: number;
  className?: string;
  showFilters?: boolean;
  activityType?: string;
  dateRange?: {
    from: Date | undefined;
    to: Date | undefined;
  };
  sortOrder?: 'asc' | 'desc';
}

const ActivityFeed: React.FC<ActivityFeedProps> = ({
  projectId,
  limit = 10,
  className,
  showFilters = true,
  activityType,
  dateRange,
  sortOrder
}) => {
  const { t } = useTranslation();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>(activityType || "all");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchActivities = async () => {
    try {
      setIsLoading(true);

      // Build filter object
      const filters: ActivityFilter = { limit };

      // Add type filter if specified (either from props or local state)
      const type = activityType || activeTab;
      if (type && type !== 'all') {
        filters.type = type;
      }

      // Add date range filters if specified
      if (dateRange?.from) {
        filters.from_date = dateRange.from.toISOString().split('T')[0];
      }

      if (dateRange?.to) {
        filters.to_date = dateRange.to.toISOString().split('T')[0];
      }

      // Add sort order if specified
      if (sortOrder) {
        filters.sort_order = sortOrder;
      }

      const data = await getRecentActivities(filters);
      setActivities(data);
    } catch (error) {
      // Error is already handled in the API function
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchActivities();
  }, [projectId, limit, activityType, dateRange?.from, dateRange?.to, sortOrder]);

  // Update activeTab when activityType prop changes
  useEffect(() => {
    if (activityType) {
      setActiveTab(activityType);
    }
  }, [activityType]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchActivities();
    setIsRefreshing(false);
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Only refetch if we're not using external filters
    if (!activityType) {
      const filters: ActivityFilter = {
        limit,
        type: value !== 'all' ? value : undefined,
        from_date: dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined,
        to_date: dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined,
        sort_order: sortOrder
      };

      setIsLoading(true);
      getRecentActivities(filters).then(data => {
        setActivities(data);
        setIsLoading(false);
      });
    }
  };

  // When using internal filters, filter activities based on the active tab
  const filteredActivities = activityType ? activities : activities.filter(activity => {
    if (activeTab === "all") return true;
    return activity.type === activeTab;
  });

  return (
    <div className={cn("bg-card border border-border rounded-xl overflow-hidden", className)}>
      <div className="p-4 border-b border-border bg-muted/20 flex items-center justify-between">
        <h2 className="text-lg font-semibold flex items-center">
          <Activity className="mr-2 h-5 w-5 text-primary" />
          {t('activity.title', 'Activity Feed')}
        </h2>

        <Button
          variant="ghost"
          size="icon"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="h-8 w-8 rounded-full"
        >
          <RefreshCw className={cn(
            "h-4 w-4",
            isRefreshing && "animate-spin"
          )} />
          <span className="sr-only">{t('activity.refresh', 'Refresh')}</span>
        </Button>
      </div>

      {showFilters && (
        <div className="px-4 py-2 border-b border-border bg-muted/10">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid grid-cols-5 h-8">
              <TabsTrigger value="all" className="text-xs">
                {t('activity.all', 'All')}
              </TabsTrigger>
              <TabsTrigger value="project_created" className="text-xs">
                {t('activity.projects', 'Projects')}
              </TabsTrigger>
              <TabsTrigger value="task_completed" className="text-xs">
                {t('activity.tasks', 'Tasks')}
              </TabsTrigger>
              <TabsTrigger value="user_joined" className="text-xs">
                {t('activity.team', 'Team')}
              </TabsTrigger>
              <TabsTrigger value="comment_added" className="text-xs">
                {t('activity.comments', 'Comments')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      )}

      <div className="p-4">
        <div className="space-y-4">
          {isLoading ? (
            // Loading skeleton
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="flex items-start space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-2 flex-1">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-[120px]" />
                    <Skeleton className="h-3 w-[80px]" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-[100px]" />
                </div>
              </div>
            ))
          ) : filteredActivities.length === 0 ? (
            // No activities state
            <div className="text-center py-8">
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-muted mb-4">
                <Clock className="h-6 w-6 text-muted-foreground" />
              </div>
              <p className="text-sm font-medium mb-1">
                {t('activity.noActivities', 'No activity to display')}
              </p>
              <p className="text-xs text-muted-foreground max-w-[250px] mx-auto">
                {t('activity.checkBackLater', 'Recent activities will appear here as your team makes progress.')}
              </p>
            </div>
          ) : (
            // Activities list
            filteredActivities.map(activity => (
              <div key={activity.id} className="flex items-start space-x-3 group">
                <div className="relative mt-1">
                  <Avatar className="h-8 w-8 border-2 border-background">
                    <AvatarImage src={activity.user.avatar ? getFullImageUrl(activity.user.avatar) : ''} alt={activity.user.name} />
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {activity.user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -right-1 -bottom-1 rounded-full bg-background p-0.5 shadow-sm">
                    <ActivityIcon type={activity.type} />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <ActivityTypeBadge type={activity.type} />
                    <p className="text-xs text-muted-foreground flex items-center">
                      <Clock className="h-3 w-3 mr-1 inline" />
                      {activity.timestamp}
                    </p>
                  </div>
                  <p className="text-sm mt-1">
                    <ActivityMessage item={activity} />
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivityFeed;
