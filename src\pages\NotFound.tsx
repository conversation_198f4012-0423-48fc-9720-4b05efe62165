import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Button } from '@/components/ui/button';
import { Home, ArrowLeft, FileQuestion } from 'lucide-react';
import { useTheme } from '@/context/ThemeContext';
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';

const NotFound = () => {
  const location = useLocation();
  const { isDarkMode } = useTheme();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className={`min-h-screen flex flex-col items-center justify-center p-8 ${isDarkMode ? 'bg-background' : 'bg-background'}`}>
      <Card className="w-full max-w-md shadow-lg border border-border">
        <CardHeader className="text-center pb-2">
          <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
            <FileQuestion className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-4xl font-bold text-foreground mb-2">404</CardTitle>
          <div className="w-16 h-1 bg-primary mx-auto my-4 rounded-full"></div>
        </CardHeader>

        <CardContent className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Page Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The page you're looking for doesn't exist or has been moved.
            Return to the home page or go back to the previous page.
          </p>
        </CardContent>

        <CardFooter className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild variant="default" className="w-full sm:w-auto">
            <Link to="/">
              <Home className="mr-2 h-4 w-4" />
              Home Page
            </Link>
          </Button>
          <Button asChild variant="outline" className="w-full sm:w-auto">
            <Link to="#" onClick={() => window.history.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default NotFound;
