
import React, { useState } from 'react';
import { MoreHorizontal, Star, Calendar, Trash2, Users, User, LogOut, Loader2, UserCog, FolderOutput } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { deleteProject, leaveProject, transferProjectToWorkspace, transferProjectOwnership } from '@/api/projectsApi';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ProjectPriority } from '@/entities/Project';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission, getRoleId, RoleId } from '@/utils/permissionUtils';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import WorkspaceSelectionModal from '@/components/workspace/WorkspaceSelectionModal';
import UserSelectionModal from '@/components/project/UserSelectionModal';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ProjectCardHeaderProps {
  title: string;
  id: number;
  priority: ProjectPriority | string; // Accept both enum and string values
  description?: string;
  isStarred?: boolean;
  isGroupProject?: boolean;
  ownerId?: number;
  project?: any; // Add project prop
  onStar?: (id: number) => void;
  onViewKanban?: (id: number) => void;
  onViewTimeline?: (id: number) => void;
}

const ProjectCardHeader: React.FC<ProjectCardHeaderProps> = ({
  title,
  description,
  id,
  priority,
  isStarred = false,
  isGroupProject = false,
  ownerId,
  project,
  onStar,
  onViewKanban,
  onViewTimeline
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Debug log to see project data and user role
  React.useEffect(() => {
    if (project && user) {
      console.log('ProjectCardHeader - Project:', project);
      console.log('ProjectCardHeader - User:', user);
      console.log('ProjectCardHeader - Is Owner:', ownerId && user.id === ownerId);
      if (project.members) {
        const userMember = project.members.find(member => member.id === user.id);
        console.log('ProjectCardHeader - User Member:', userMember);
        if (userMember && userMember.pivot) {
          console.log('ProjectCardHeader - User Role ID:', userMember.pivot.role_id);
          console.log('ProjectCardHeader - Is Admin:', userMember.pivot.role_id === RoleId.ADMIN);
        }
      }
    }
  }, [project, user, ownerId]);

  // State for modals
  const [isTransferModalOpen, setIsTransferModalOpen] = useState(false);
  const [isLeaveDialogOpen, setIsLeaveDialogOpen] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);
  const [isTransferOwnershipModalOpen, setIsTransferOwnershipModalOpen] = useState(false);
  const [isTransferringOwnership, setIsTransferringOwnership] = useState(false);

  // Export modal state to parent component to prevent navigation when modals are open
  React.useEffect(() => {
    // Set a global variable to indicate if any modal is open
    window.isProjectModalOpen = isTransferModalOpen || isLeaveDialogOpen || isTransferOwnershipModalOpen;

    // Cleanup function to reset the variable when component unmounts
    return () => {
      window.isProjectModalOpen = false;
    };
  }, [isTransferModalOpen, isLeaveDialogOpen, isTransferOwnershipModalOpen]);

  const handleStarClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onStar) {
      onStar(id);

      // If we're in the starred view and unstarring a project, we need to check
      // if we should refresh the view to immediately remove the project
      if (isStarred) {
        // Check if we're in a view that might be filtered by starred status
        const currentPath = window.location.pathname;
        const searchParams = new URLSearchParams(window.location.search);
        const filter = searchParams.get('filter');

        // If we're in the starred filter view and not using the parent's onStar handler
        // (which would already handle this), we should refresh the data
        if ((filter === 'starred' || currentPath.includes('/starred')) && !('onStar' in window)) {
          // Invalidate relevant queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['starredProjects'] });
          queryClient.invalidateQueries({ queryKey: ['projects'] });
        }
      }
    }
  };

  const handleViewKanban = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Navigate to kanban board view using React Router
    navigate(`/projects/${id}/board`);
  };

  const handleViewTimeline = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Navigate to timeline view using React Router
    navigate(`/projects/${id}/timeline`);
  };

  const handleViewCalendar = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Navigate to calendar view using React Router
    navigate(`/projects/${id}/calendar`);
  };

  const handleDeleteProject = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (confirm("Are you sure you want to delete this project? This action cannot be undone.")) {
      try {
        await deleteProject(id);

        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['projects'] });
        queryClient.invalidateQueries({ queryKey: ['starredProjects'] });

        toast({
          title: "Success",
          description: "Project deleted successfully",
        });

        // Navigate to projects page instead of reloading
        navigate('/projects');
      } catch (error) {
        console.error("Error deleting project:", error);
        toast({
          title: "Error",
          description: "Failed to delete the project",
          variant: "destructive",
        });
      }
    }
  };

  const handleLeaveProject = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLeaveDialogOpen(true);
  };

  const executeLeaveProject = async () => {
    try {
      setIsLeaving(true);
      await leaveProject(id);

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['starredProjects'] });

      // Close the dialog
      setIsLeaveDialogOpen(false);

      // Navigate to the success page with project information
      navigate('/projects/leave-success', {
        state: {
          projectName: title
        }
      });
    } catch (error: any) {
      console.error("Error leaving project:", error);

      // The error message is already displayed in the API function
      setIsLeaving(false);
      setIsLeaveDialogOpen(false);
    }
  };

  // Handler for transferring a project to a different workspace
  const handleTransferToWorkspace = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsTransferModalOpen(true);
  };

  // Execute the transfer when a workspace is selected
  const executeTransfer = async (workspaceId: number) => {
    try {
      const result = await transferProjectToWorkspace(id, workspaceId);

      toast({
        title: "Success",
        description: "Project transferred successfully",
        action: (
          <Button
            variant="outline"
            onClick={() => navigate(`/workspace/${workspaceId}`)}
          >
            View Workspace
          </Button>
        )
      });

      // Close the modal
      setIsTransferModalOpen(false);

      // No automatic navigation - user must click the button to navigate
    } catch (error: any) {
      // Check if it's a duplicate project name error
      if (error.response && error.response.status === 409) {
        toast({
          title: "Duplicate Project",
          description: "A project with this name already exists in the target workspace. Please rename the project before transferring.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to transfer the project",
          variant: "destructive",
        });
      }
    }
  };

  // Handler for transferring project ownership
  const handleTransferOwnership = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsTransferOwnershipModalOpen(true);
  };

  // Execute the ownership transfer when a user is selected
  const executeTransferOwnership = async (newOwnerId: number) => {
    try {
      setIsTransferringOwnership(true);

      // Get the user details for better feedback
      const selectedUser = project?.members?.find(member => member.id === newOwnerId);
      const userName = selectedUser ? `${selectedUser.first_name} ${selectedUser.last_name}` : 'selected user';

      const result = await transferProjectOwnership(id, newOwnerId);

      // Close the modal
      setIsTransferOwnershipModalOpen(false);

      toast({
        title: "Success",
        description: `Project ownership transferred successfully to ${userName}`,
      });

      // Refresh the project data
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', id] });
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });

      // Navigate to projects page since the user is no longer the owner
      navigate('/projects');
    } catch (error: any) {
      console.error("Error transferring project ownership:", error);

      // Get a more specific error message if available
      const errorMessage = error.response?.data?.error || error.response?.data?.message || "Failed to transfer project ownership";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      setIsTransferringOwnership(false);
    }
  };

  return (
    <>
      <div className="flex justify-between items-start mb-3">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <h3 className="text-lg font-extrabold line-clamp-1 text-foreground dark:text-foreground/95">{title || "Untitled Project"}</h3>
          </div>
          <p className="text-sm text-muted-foreground dark:text-muted-foreground/90 line-clamp-2 leading-relaxed">{description}</p>
        </div>
        <div className="flex items-center">
          {onStar && (
            <button
              onClick={handleStarClick}
              className="h-8 w-8 flex items-center justify-center rounded-full hover:bg-accent dark:hover:bg-accent/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/30 dark:focus:ring-primary/50 transition-colors mr-1"
              aria-label={isStarred ? "Unstar project" : "Star project"}
            >
              <Star
                size={18}
                className={cn(
                  "transition-colors",
                  isStarred ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground dark:text-muted-foreground/80 dark:hover:text-muted-foreground"
                )}
              />
            </button>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button
                className="h-8 w-8 flex items-center justify-center rounded-full hover:bg-accent dark:hover:bg-accent/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/30 dark:focus:ring-primary/50 transition-colors"
                onClick={(e) => e.stopPropagation()}
                aria-label="More options"
              >
                <MoreHorizontal size={18} className="text-muted-foreground dark:text-muted-foreground/80 dark:hover:text-muted-foreground" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
              <DropdownMenuItem onClick={handleViewKanban}>
                <Kanban className="mr-2 h-4 w-4 dark:text-primary/90" />
                Kanban Board
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleViewCalendar}>
                <Calendar className="mr-2 h-4 w-4 dark:text-success/90" />
                Calendar View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleViewTimeline}>
                <BarChart className="mr-2 h-4 w-4 dark:text-info/90" />
                Timeline View
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {/* Only show Edit Project option for users with edit permission */}
              {project && user && (
                // Project owners always see Edit Project option
                (ownerId && user.id === ownerId) ||
                // Users with edit permission also see it
                hasProjectPermission(project, user, ProjectPermission.EDIT_PROJECT)
              ) && (
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/projects/${id}/edit`);
                }}>
                  Edit Project
                </DropdownMenuItem>
              )}

              {/* Show workspace transfer/copy options for project owners and admins */}
              {user && project && (
                // Project owners or admins see these options
                ((ownerId && user.id === ownerId) ||
                 (project.members && project.members.some(member =>
                    member.id === user.id &&
                    member.pivot &&
                    member.pivot.role_id === RoleId.ADMIN
                 ))) && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleTransferOwnership}>
                    <UserCog className="mr-2 h-4 w-4 dark:text-warning/90" />
                    Transfer Ownership
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleTransferToWorkspace}>
                    <FolderOutput className="mr-2 h-4 w-4 dark:text-info/90" />
                    Transfer to Workspace
                  </DropdownMenuItem>
                </>
                )
              )}

              <DropdownMenuSeparator />
              {/* Show Delete Project for owners and admins, Leave Project for editors and viewers */}
              {user && project && (
                ((ownerId && user.id === ownerId) ||
                 (project.members && project.members.some(member =>
                    member.id === user.id &&
                    member.pivot &&
                    member.pivot.role_id === RoleId.ADMIN
                 ))) ? (
                <DropdownMenuItem
                  className="text-destructive dark:text-destructive-foreground"
                  onClick={handleDeleteProject}
                >
                  <Trash2 className="mr-2 h-4 w-4 dark:text-destructive-foreground" />
                  Delete Project
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  className="text-destructive dark:text-destructive-foreground"
                  onClick={handleLeaveProject}
                >
                  <LogOut className="mr-2 h-4 w-4 dark:text-destructive-foreground" />
                  Leave Project
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>



      {/* Workspace Selection Modal for Transfer */}
      <WorkspaceSelectionModal
        isOpen={isTransferModalOpen}
        onClose={() => setIsTransferModalOpen(false)}
        onSelect={executeTransfer}
        title="Transfer Project to Workspace"
        description="Select a workspace to transfer this project to. This will move the project and all its data to the selected workspace."
        actionText="Transfer Project"
        currentWorkspaceId={project?.workspace_id}
      />

      {/* Leave Project Confirmation Dialog */}
      <AlertDialog open={isLeaveDialogOpen} onOpenChange={setIsLeaveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Leave Project</AlertDialogTitle>
            {user && project && ((ownerId && user.id === ownerId) || (project.members && project.members.some(member => member.id === user.id && member.pivot && member.pivot.role_id === RoleId.ADMIN))) ? (
              <>
                <AlertDialogDescription className="mt-2">
                  <div className="bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-md p-3 mb-3">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">Project Owner/Admin Cannot Leave</h3>
                        <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                          <p>As the project owner or admin, you cannot leave this project. You have two options:</p>
                          <ul className="list-disc pl-5 mt-1 space-y-1">
                            <li>Transfer ownership to another admin user</li>
                            <li>Delete the project entirely</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </AlertDialogDescription>
                <div className="flex flex-col sm:flex-row gap-2 mt-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      setIsLeaveDialogOpen(false);
                      setIsTransferOwnershipModalOpen(true);
                    }}
                  >
                    <UserCog className="mr-2 h-4 w-4" />
                    Transfer Ownership
                  </Button>
                  <Button
                    variant="destructive"
                    className="w-full"
                    onClick={(e) => {
                      setIsLeaveDialogOpen(false);
                      handleDeleteProject(e as React.MouseEvent);
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Project
                  </Button>
                </div>
              </>
            ) : (
              <AlertDialogDescription>
                Are you sure you want to leave this project? You will lose access to it.
              </AlertDialogDescription>
            )}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {!(user && project && ((ownerId && user.id === ownerId) || (project.members && project.members.some(member => member.id === user.id && member.pivot && member.pivot.role_id === RoleId.ADMIN)))) && (
              <AlertDialogAction
                onClick={executeLeaveProject}
                disabled={isLeaving}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isLeaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Leaving...
                  </>
                ) : (
                  'Leave Project'
                )}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* User Selection Modal for Ownership Transfer */}
      {user && (
        <UserSelectionModal
          isOpen={isTransferOwnershipModalOpen}
          onClose={() => setIsTransferOwnershipModalOpen(false)}
          onSelect={executeTransferOwnership}
          title="Transfer Project Ownership"
          description="Select a user to transfer ownership of this project to. The selected user will automatically be given admin privileges. This action cannot be undone."
          actionText="Transfer Ownership"
          projectId={id}
          currentUserId={user.id}
        />
      )}
    </>
  );
};

export default ProjectCardHeader;
