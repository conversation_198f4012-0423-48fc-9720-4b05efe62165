import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { getLocalStorageData, setLocalStorageData } from '@/utils/sessionLocalStorageUtil';

// Add a global style element to handle theme transitions and ensure consistent dark mode styling
const addThemeTransitionStyles = () => {
  const style = document.createElement('style');
  style.textContent = `
    * {
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
    }

    /* Disable transitions during theme change to prevent flash */
    .disable-transitions * {
      transition: none !important;
    }

    /* Ensure consistent dark mode styling for all components */
    .dark body {
      color-scheme: dark;
    }

    /* Force dark mode styles on specific components that might be inconsistent */
    .dark .bg-card,
    .dark .bg-popover,
    .dark .bg-background {
      background-color: hsl(var(--card)) !important;
    }

    .dark .border-border {
      border-color: hsl(var(--border)) !important;
    }

    .dark .text-foreground {
      color: hsl(var(--foreground)) !important;
    }

    .dark .text-muted-foreground {
      color: hsl(var(--muted-foreground)) !important;
    }
  `;
  document.head.appendChild(style);
};

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    const savedTheme = getLocalStorageData('theme') as Theme;
    return savedTheme || 'system';
  });

  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);

  // Update localStorage when theme changes
  const setTheme = (newTheme: Theme) => {
    // Temporarily disable transitions to prevent flash
    document.documentElement.classList.add('disable-transitions');

    setThemeState(newTheme);
    setLocalStorageData('theme', newTheme, 365 * 24 * 60 * 60 * 1000); // Store for 1 year

    // Apply theme to document
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
      setIsDarkMode(true);
    } else if (newTheme === 'light') {
      document.documentElement.classList.remove('dark');
      setIsDarkMode(false);
    } else if (newTheme === 'system') {
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (systemPrefersDark) {
        document.documentElement.classList.add('dark');
        setIsDarkMode(true);
      } else {
        document.documentElement.classList.remove('dark');
        setIsDarkMode(false);
      }
    }

    // Re-enable transitions after a short delay
    setTimeout(() => {
      document.documentElement.classList.remove('disable-transitions');
    }, 10);
  };

  // Toggle between light and dark mode
  const toggleDarkMode = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  // Initialize theme based on system preference or saved preference
  useEffect(() => {
    // Add transition styles on initial load
    addThemeTransitionStyles();

    const savedTheme = getLocalStorageData('theme') as Theme;

    if (savedTheme) {
      setTheme(savedTheme);
    } else {
      // If no saved theme, use system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setTheme(systemPrefersDark ? 'dark' : 'light');
    }

    // Listen for system preference changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => {
      if (theme === 'system') {
        // Temporarily disable transitions for system theme changes
        document.documentElement.classList.add('disable-transitions');

        setIsDarkMode(mediaQuery.matches);
        if (mediaQuery.matches) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }

        // Re-enable transitions after a short delay
        setTimeout(() => {
          document.documentElement.classList.remove('disable-transitions');
        }, 10);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme, isDarkMode, toggleDarkMode }}>
      <NextThemesProvider attribute="class" defaultTheme={theme} enableSystem>
        {children}
      </NextThemesProvider>
    </ThemeContext.Provider>
  );
};
