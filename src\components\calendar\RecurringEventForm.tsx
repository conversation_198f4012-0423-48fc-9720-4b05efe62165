import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { RecurringEventFormData, RecurringFrequency } from '@/types/calendar';

interface RecurringEventFormProps {
  startDate: Date;
  initialValues?: RecurringEventFormData;
  onSubmit: (data: RecurringEventFormData) => void;
  onCancel: () => void;
}

const RecurringEventForm: React.FC<RecurringEventFormProps> = ({
  startDate,
  initialValues,
  onSubmit,
  onCancel,
}) => {
  // Form state
  const [frequency, setFrequency] = useState<RecurringFrequency>(
    initialValues?.recurring_frequency || 'weekly'
  );
  const [interval, setInterval] = useState<number>(
    initialValues?.recurring_interval || 1
  );
  const [daysOfWeek, setDaysOfWeek] = useState<number[]>(
    initialValues?.recurring_days_of_week || [startDate.getDay()]
  );
  const [dayOfMonth, setDayOfMonth] = useState<number>(
    initialValues?.recurring_day_of_month || startDate.getDate()
  );
  const [monthOfYear, setMonthOfYear] = useState<number>(
    initialValues?.recurring_month_of_year || startDate.getMonth() + 1
  );
  const [endType, setEndType] = useState<'never' | 'on' | 'after'>(
    initialValues?.recurring_ends_on ? 'on' :
    initialValues?.recurring_count ? 'after' : 'never'
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialValues?.recurring_ends_on ? new Date(initialValues.recurring_ends_on) : undefined
  );
  const [occurrences, setOccurrences] = useState<number>(
    initialValues?.recurring_count || 10
  );

  // Update form when initialValues change
  useEffect(() => {
    if (initialValues) {
      setFrequency(initialValues.recurring_frequency);
      setInterval(initialValues.recurring_interval);
      setDaysOfWeek(initialValues.recurring_days_of_week || [startDate.getDay()]);
      setDayOfMonth(initialValues.recurring_day_of_month || startDate.getDate());
      setMonthOfYear(initialValues.recurring_month_of_year || startDate.getMonth() + 1);
      setEndType(
        initialValues.recurring_ends_on ? 'on' :
        initialValues.recurring_count ? 'after' : 'never'
      );
      setEndDate(initialValues.recurring_ends_on ? new Date(initialValues.recurring_ends_on) : undefined);
      setOccurrences(initialValues.recurring_count || 10);
    }
  }, [initialValues, startDate]);

  // Handle form submission
  const handleSubmit = () => {
    const formData: RecurringEventFormData = {
      is_recurring: true,
      recurring_frequency: frequency,
      recurring_interval: interval,
      recurring_days_of_week: frequency === 'weekly' ? daysOfWeek : undefined,
      recurring_day_of_month: ['monthly', 'yearly'].includes(frequency) ? dayOfMonth : undefined,
      recurring_month_of_year: frequency === 'yearly' ? monthOfYear : undefined,
      recurring_ends_on: endType === 'on' && endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
      recurring_count: endType === 'after' ? occurrences : undefined,
    };

    onSubmit(formData);
  };

  // Toggle day of week selection
  const toggleDayOfWeek = (day: number) => {
    setDaysOfWeek(prev => {
      if (prev.includes(day)) {
        return prev.filter(d => d !== day);
      } else {
        return [...prev, day];
      }
    });
  };

  // Generate day of month options (1-31)
  const dayOfMonthOptions = Array.from({ length: 31 }, (_, i) => i + 1);

  // Generate month options
  const monthOptions = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' },
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">Recurring Event Settings</h3>

      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Frequency</label>
            <Select value={frequency} onValueChange={(value) => setFrequency(value as RecurringFrequency)}>
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Repeat every</label>
            <div className="flex items-center space-x-2">
              <Input
                type="number"
                min="1"
                max="99"
                value={interval}
                onChange={(e) => setInterval(parseInt(e.target.value) || 1)}
                className="w-20"
              />
              <span className="text-sm">
                {frequency === 'daily' && (interval === 1 ? 'day' : 'days')}
                {frequency === 'weekly' && (interval === 1 ? 'week' : 'weeks')}
                {frequency === 'monthly' && (interval === 1 ? 'month' : 'months')}
                {frequency === 'yearly' && (interval === 1 ? 'year' : 'years')}
              </span>
            </div>
          </div>
        </div>

        {/* Weekly options */}
        {frequency === 'weekly' && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Repeat on</label>
            <div className="flex flex-wrap gap-2">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                <div key={day} className="flex items-center space-x-2">
                  <Checkbox
                    id={`day-${index}`}
                    checked={daysOfWeek.includes(index)}
                    onCheckedChange={() => toggleDayOfWeek(index)}
                  />
                  <label
                    htmlFor={`day-${index}`}
                    className="text-sm cursor-pointer"
                  >
                    {day}
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Monthly options */}
        {frequency === 'monthly' && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Day of month</label>
            <Select
              value={dayOfMonth.toString()}
              onValueChange={(value) => setDayOfMonth(parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select day" />
              </SelectTrigger>
              <SelectContent>
                {dayOfMonthOptions.map((day) => (
                  <SelectItem key={day} value={day.toString()}>
                    {day}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Yearly options */}
        {frequency === 'yearly' && (
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Month</label>
              <Select
                value={monthOfYear.toString()}
                onValueChange={(value) => setMonthOfYear(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent>
                  {monthOptions.map((month) => (
                    <SelectItem key={month.value} value={month.value.toString()}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Day</label>
              <Select
                value={dayOfMonth.toString()}
                onValueChange={(value) => setDayOfMonth(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {dayOfMonthOptions.map((day) => (
                    <SelectItem key={day} value={day.toString()}>
                      {day}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* End options */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Ends</label>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="end-never"
              checked={endType === 'never'}
              onCheckedChange={() => setEndType('never')}
            />
            <label htmlFor="end-never" className="text-sm cursor-pointer">
              Never
            </label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="end-on"
              checked={endType === 'on'}
              onCheckedChange={() => setEndType('on')}
            />
            <label htmlFor="end-on" className="text-sm cursor-pointer">
              On date
            </label>
            
            {endType === 'on' && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="ml-2 w-[180px] justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                    disabled={(date) => date < startDate}
                  />
                </PopoverContent>
              </Popover>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="end-after"
              checked={endType === 'after'}
              onCheckedChange={() => setEndType('after')}
            />
            <label htmlFor="end-after" className="text-sm cursor-pointer">
              After
            </label>
            
            {endType === 'after' && (
              <div className="flex items-center space-x-2 ml-2">
                <Input
                  type="number"
                  min="1"
                  max="999"
                  value={occurrences}
                  onChange={(e) => setOccurrences(parseInt(e.target.value) || 1)}
                  className="w-20"
                />
                <span className="text-sm">occurrences</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>
          Apply
        </Button>
      </div>
    </div>
  );
};

export default RecurringEventForm;
