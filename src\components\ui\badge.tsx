import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80 dark:shadow-sm dark:shadow-primary/20",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 dark:shadow-sm dark:shadow-secondary/20",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 dark:shadow-sm dark:shadow-destructive/20",
        outline: "text-foreground dark:border-border/70 dark:text-foreground/90",
        // Status variants
        success:
          "border-transparent bg-success text-success-foreground hover:bg-success/80 dark:shadow-sm dark:shadow-success/20",
        warning:
          "border-transparent bg-warning text-warning-foreground hover:bg-warning/80 dark:shadow-sm dark:shadow-warning/20",
        info:
          "border-transparent bg-info text-info-foreground hover:bg-info/80 dark:shadow-sm dark:shadow-info/20",
        // Subtle status variants
        "success-subtle":
          "border-transparent bg-success/20 text-success dark:bg-success/30 dark:text-success-foreground hover:bg-success/30 dark:hover:bg-success/40",
        "warning-subtle":
          "border-transparent bg-warning/20 text-warning dark:bg-warning/30 dark:text-warning-foreground hover:bg-warning/30 dark:hover:bg-warning/40",
        "destructive-subtle":
          "border-transparent bg-destructive/20 text-destructive dark:bg-destructive/30 dark:text-destructive-foreground hover:bg-destructive/30 dark:hover:bg-destructive/40",
        "info-subtle":
          "border-transparent bg-info/20 text-info dark:bg-info/30 dark:text-info-foreground hover:bg-info/30 dark:hover:bg-info/40",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, ...props }, ref) => {
    return (
      <div
        className={cn(badgeVariants({ variant }), className)}
        ref={ref}
        {...props}
      />
    )
  }
)

Badge.displayName = "Badge"

export { Badge, badgeVariants }
