import React, { useEffect, useState } from 'react';
import { ArrowUpR<PERSON>, File<PERSON>heck, Plus, Search, Filter } from 'lucide-react';
import ProjectCard, { ProjectCardProps } from '../ProjectCard';
import { Project, ProjectProgress } from '@/entities/Project';
import { getAllProjects, getProjects } from '@/api/projectsApi';
import { useToast } from '../ui/use-toast';
import Loader from '../Loader';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { useNavigate } from 'react-router-dom';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import CreateProjectModal from '@/components/project/CreateProjectModal';
import { TurkishDateHelper } from '@/utils/dateUtils';
import { useWorkspace } from '@/context/WorkspaceContext';

type StatusFilter = 'all' | 'completed' | 'in-progress' | 'not-started';

interface AllUserProjectsProps {
  workspaceId: number | string;
}

const AllUserProjects: React.FC<AllUserProjectsProps> = ({ workspaceId }) => {
  const [allProjects, setAllProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [filter, setFilter] = useState<StatusFilter>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showAllProjects, setShowAllProjects] = useState<boolean>(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { projects: workspaceProjects } = useWorkspace();

  // Helper function to map Project to ProjectCardProps
  const mapProjectToCardProps = (project: Project): ProjectCardProps => {
    // Convert progress from string to number if needed
    const progressNumber = typeof project.progress === 'string'
      ? parseFloat(project.progress) || 0
      : project.progress || 0;

    return {
      id: project.id.toString(),
      title: project.name,
      description: project.description || '',
      progress: progressNumber,
      dueDate: project.end_date ? TurkishDateHelper.formatDate(project.end_date) : 'No date',
      priority: (project.priority as 'low' | 'medium' | 'high') || 'medium',
      status: (project.status as 'completed' | 'in-progress' | 'not-started' | 'at-risk') || 'not-started',
      members: project.members || [],
      isStarred: project.is_starred || false,
      is_group_project: project.is_group_project, // Added this property
      onStar: () => {}, // Empty function as we don't handle starring here
      completion_percentage: project.completion_percentage,
      completed_tasks: project.completed_tasks,
      total_tasks: project.total_tasks
    };
  };

  // Use projects from workspace context and fetch all projects if needed
  useEffect(() => {
    const fetchAllProjects = async () => {
      setIsLoading(true);
      try {
        // First, use the projects from the workspace context
        if (workspaceProjects && workspaceProjects.length > 0) {
          console.log("AllUserProjects - Using workspace projects:", workspaceProjects);
          setAllProjects(workspaceProjects);
        } else {
          // If no workspace projects, try to fetch them directly
          console.log("AllUserProjects - Fetching workspace projects for ID:", workspaceId);
          const data = await getProjects(Number(workspaceId));
          console.log("AllUserProjects - Fetched workspace projects:", data);
          setAllProjects(data);
        }

        // Also fetch all projects for the "All Projects" toggle
        if (showAllProjects) {
          const allData = await getAllProjects();
          console.log("AllUserProjects - All projects:", allData);
          setAllProjects(allData);
        }
      } catch (error) {
        console.error("Error fetching projects:", error);
        toast({
          title: "Failed to load projects",
          description: "Could not load your projects. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllProjects();
  }, [workspaceId, toast, workspaceProjects, showAllProjects]);

  // Function to apply project filters based on workspace and toggle state
  const applyProjectFilters = (projects: Project[], showAll: boolean) => {
    if (showAll) {
      // Show all projects
      setFilteredProjects(projects);
    } else {
      // Filter projects by workspace ID
      const workspaceProjects = projects.filter(project =>
        project.workspace_id === Number(workspaceId)
      );
      setFilteredProjects(workspaceProjects);
    }
  };

  // This effect is no longer needed as we handle all filtering in the other useEffect

  // Apply search and status filters
  useEffect(() => {
    // First, get the base set of projects based on the showAllProjects toggle
    let baseProjects = allProjects;

    if (!showAllProjects) {
      // Filter by workspace if not showing all projects
      baseProjects = allProjects.filter(project =>
        project.workspace_id === Number(workspaceId)
      );
    }

    // Then apply search filter
    let filteredResults = baseProjects;

    if (searchQuery.trim() !== '') {
      filteredResults = filteredResults.filter(project =>
        project.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Finally apply status filter
    if (filter !== 'all') {
      filteredResults = filteredResults.filter(project => {
        if (filter === 'completed') return project.progress === ProjectProgress.Completed;
        if (filter === 'in-progress') return project.progress === ProjectProgress.InProgress;
        if (filter === 'not-started') return project.progress === ProjectProgress.NotStarted;
        return true;
      });
    }

    setFilteredProjects(filteredResults);
  }, [searchQuery, filter, allProjects, showAllProjects, workspaceId]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const handleCreateProject = () => {
    // Open the create project modal
    setIsCreateModalOpen(true);
  };

  // We don't need this function anymore as filtering is handled in the useEffect
  const setStatusFilter = (statusFilter: StatusFilter) => {
    setFilter(statusFilter);
  };

  const resetFilter = () => {
    if (filter !== 'all') {
      setFilter('all');
    }
  }

  const inProgress = () => {
    if (filter !== 'in-progress') {
      setFilter('in-progress');
    }
  }

  const completedProjects = () => {
    if (filter !== 'completed') {
      setFilter('completed');
    }
  }

  const notStarted = () => {
    if (filter !== 'not-started') {
      setFilter('not-started');
    }
  }

  // Handle successful project creation
  const handleProjectCreated = async () => {
    // Refresh the projects list
    try {
      // If showing all projects, get all projects
      if (showAllProjects) {
        const data = await getAllProjects();
        setAllProjects(data);
      } else {
        // Otherwise just get workspace projects
        const data = await getProjects(Number(workspaceId));
        setAllProjects(data);
      }
    } catch (error) {
      console.error("Error refreshing projects:", error);
      toast({
        title: "Failed to refresh projects",
        description: "There was an error refreshing your projects. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <div className="bg-card border border-border rounded-xl shadow-subtle overflow-hidden mb-6">
        <div className="p-5 border-b border-border">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold mb-3 sm:mb-0">Projects Overview</h2>
              <Button
                variant="link"
                className="text-primary flex items-center p-0 h-auto font-medium"
                onClick={() => navigate('/projects')}
              >
                <ArrowUpRight className="h-4 w-4 ml-1" />
                Projects
              </Button>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <div className="flex items-center min-w-[200px]">
                  <Switch
                    id="show-all-projects"
                    checked={showAllProjects}
                    onCheckedChange={setShowAllProjects}
                    className="mr-2 flex-shrink-0"
                  />
                  <Label htmlFor="show-all-projects" className="cursor-pointer whitespace-nowrap">
                    {showAllProjects ? "All Projects" : "Workspace Projects"}
                  </Label>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search projects..."
                className="pl-8"
                value={searchQuery}
                onChange={handleSearch}
              />
            </div>

            <div className="flex flex-wrap gap-2">
              <button
                onClick={resetFilter}
                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'all' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                  }`}
              >
                All Projects
              </button>
              <button
                onClick={inProgress}
                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'in-progress' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                  }`}
              >
                In Progress
              </button>
              <button
                onClick={completedProjects}
                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'completed' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                  }`}
              >
                Completed
              </button>
              <button
                onClick={notStarted}
                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'not-started' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                  }`}
              >
                Not Started
              </button>
            </div>
          </div>
        </div>

        <div className="p-5">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              {filteredProjects.length > 0 ? (
                filteredProjects.map((project) => (
                  <ProjectCard key={project.id} {...mapProjectToCardProps(project)} />
                ))
              ) : (
                <div className="md:col-span-2 py-8 flex flex-col items-center justify-center text-center">
                  <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-3">
                    <FileCheck className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-1">No projects found</h3>
                  <p className="text-muted-foreground max-w-md">
                    No projects match your current filter. Try selecting a different status or create a new project.
                  </p>
                  <Button
                    onClick={() => setIsCreateModalOpen(true)}
                    variant="outline"
                    className="mt-4 flex items-center gap-1"
                  >
                    <Plus size={16} />
                    Create New Project
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleProjectCreated}
      />
    </>
  );
};

export default AllUserProjects;
