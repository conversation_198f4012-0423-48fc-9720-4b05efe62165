import React, { useEffect, useState } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths, isSameMonth, isSameDay, isToday, parseISO } from 'date-fns';
import { ChevronLeft, ChevronRight, Plus, Filter, User, Calendar as CalendarIcon, ArrowLeft, BarChart } from 'lucide-react';
import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useParams, useNavigate } from 'react-router-dom';
import { getProject } from '@/api/projectsApi';
import { getProjectEvents } from '@/api/calendarApi';
import { CalendarEvent } from '@/api/calendarApi';
import Loader from '@/components/Loader';

import echo from '@/utils/echo';

interface EventItem {
  id: number | string;
  title: string;
  description?: string;
  date: Date;
  endDate?: Date;
  type: 'task' | 'deadline' | 'meeting' | 'milestone';
  status: 'not-started' | 'in-progress' | 'completed';
  priority?: 'low' | 'medium' | 'high';
  project_id?: number;
  assignees: {
    id: number | string;
    name: string;
    avatar?: string;
  }[];
  is_task?: boolean;
}

// Helper function to ensure event type is valid
const ensureValidEventType = (type: string): 'task' | 'deadline' | 'meeting' | 'milestone' => {
  if (type === 'task' || type === 'deadline' || type === 'meeting' || type === 'milestone') {
    return type;
  }
  return 'task'; // Default to task if invalid type
};

// Helper function to ensure event status is valid
const ensureValidEventStatus = (status: string): 'not-started' | 'in-progress' | 'completed' => {
  if (status === 'not-started' || status === 'in-progress' || status === 'completed') {
    return status;
  }
  return 'not-started'; // Default to not-started if invalid status
};

// Helper function to ensure event priority is valid
const ensureValidEventPriority = (priority: string): 'low' | 'medium' | 'high' => {
  if (priority === 'low' || priority === 'medium' || priority === 'high') {
    return priority;
  }
  return 'medium'; // Default to medium if invalid priority
};

const ProjectCalendar = () => {
  const navigate = useNavigate();
  const { projectId } = useParams<{ projectId: string }>();
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [filterType, setFilterType] = useState<string | null>(null);
  const [filterAssignee, setFilterAssignee] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [project, setProject] = useState<any>(null);
  const [events, setEvents] = useState<EventItem[]>([]);

  // Generate days for calendar view
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Handle month navigation
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Go to today
  const goToToday = () => {
    const today = new Date();
    setCurrentMonth(today);
    setSelectedDate(today);
  };

  // Fetch project and calendar events
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        if (projectId) {
          // Fetch project details
          const projectData = await getProject(projectId);
          setProject(projectData);

          // Fetch calendar events for this project
          const eventsData = await getProjectEvents(Number(projectId));

          // Convert API events to local format
          const formattedEvents: EventItem[] = eventsData.map((event: CalendarEvent) => ({
            id: event.id,
            title: event.title,
            description: event.description,
            date: new Date(event.start_date),
            endDate: event.end_date ? new Date(event.end_date) : undefined,
            type: ensureValidEventType(event.type || 'task'),
            status: ensureValidEventStatus(event.status || 'not-started'),
            priority: ensureValidEventPriority(event.priority || 'medium'),
            project_id: event.project_id,
            assignees: event.assignees || [],
            is_task: event.is_task || false
          }));

          setEvents(formattedEvents);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [projectId, currentMonth]);

  // Set up real-time event listeners
  useEffect(() => {
    if (!projectId) return;

    try {
      // Check if echo has the private method (mock echo won't have it)
      if (typeof echo.private !== 'function') {
        console.warn('Real-time updates are disabled. Echo is not properly configured.');
        return;
      }

      // Listen for project-specific events
      const projectChannel = echo.private(`project.${projectId}`);

      // Listen for event creation
      projectChannel.listen('.calendar.event.created', (e: {
        id: number | string,
        title: string,
        start_date: string,
        end_date?: string,
        type: string,
        status: string,
        priority: string,
        project_id: number,
        project: any,
        assignees: any[],
        is_task?: boolean
      }) => {
        // Only add events for this project
        if (e.project_id === Number(projectId)) {
          const eventDate = new Date(e.start_date);
          if (isSameMonth(eventDate, currentMonth)) {
            // Add the new event to the list
            setEvents(prev => [
              ...prev,
              {
                id: e.id,
                title: e.title,
                date: eventDate,
                endDate: e.end_date ? new Date(e.end_date) : undefined,
                type: ensureValidEventType(e.type || 'task'),
                status: ensureValidEventStatus(e.status || 'not-started'),
                priority: ensureValidEventPriority(e.priority || 'medium'),
                project_id: e.project_id,
                assignees: e.assignees || [],
                is_task: e.is_task || false
              }
            ]);
          }
        }
      });

      // Listen for event updates
      projectChannel.listen('.calendar.event.updated', (e: {
        id: number | string,
        title: string,
        start_date: string,
        end_date?: string,
        type: string,
        status: string,
        priority: string,
        project_id: number,
        project: any,
        assignees: any[],
        is_task?: boolean
      }) => {
        // Only update events for this project
        if (e.project_id === Number(projectId)) {
          // Update the event in the list
          setEvents(prev => prev.map(event => {
            if (event.id === e.id) {
              return {
                id: e.id,
                title: e.title,
                date: new Date(e.start_date),
                endDate: e.end_date ? new Date(e.end_date) : undefined,
                type: ensureValidEventType(e.type || 'task'),
                status: ensureValidEventStatus(e.status || 'not-started'),
                priority: ensureValidEventPriority(e.priority || 'medium'),
                project_id: e.project_id,
                assignees: e.assignees || [],
                is_task: e.is_task || false
              };
            }
            return event;
          }));
        }
      });

      // Listen for event deletions
      projectChannel.listen('.calendar.event.deleted', (e: { id: number | string }) => {
        // Remove the event from the list
        setEvents(prev => prev.filter(event => event.id !== e.id));
      });

      // Clean up listeners when component unmounts
      return () => {
        projectChannel.stopListening('.calendar.event.created');
        projectChannel.stopListening('.calendar.event.updated');
        projectChannel.stopListening('.calendar.event.deleted');
      };
    } catch (error) {
      console.error('Error setting up real-time listeners:', error);
      // Continue without real-time updates
      return () => {};
    }
  }, [projectId, currentMonth]);

  // Get events for a specific day
  const getEventsForDay = (day: Date) => {
    return events.filter(event =>
      isSameDay(event.date, day) &&
      (filterType ? event.type === filterType : true) &&
      (filterAssignee ? event.assignees.some(assignee => assignee.id === filterAssignee) : true)
    );
  };

  // Get all events for the selected month, filtered
  const filteredEvents = events.filter(event =>
    isSameMonth(event.date, currentMonth) &&
    (filterType ? event.type === filterType : true) &&
    (filterAssignee ? event.assignees.some(assignee => assignee.id === filterAssignee) : true)
  );

  // Type color schemes
  const typeColors = {
    meeting: 'bg-purple-100 text-purple-800 border-purple-300 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700/50',
    deadline: 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700/50',
    milestone: 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700/50',
    task: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700/50',
  };

  // Status color schemes
  const statusColors = {
    completed: 'bg-green-500 dark:bg-green-600',
    'in-progress': 'bg-primary dark:bg-primary/80',
    'not-started': 'bg-orange-400 dark:bg-orange-500',
  };

  // Event item component
  const EventItem = ({ event }: { event: EventItem }) => {
    // Ensure we have the correct ID format for the API
    const getEventIdForUrl = () => {
      // If it's a task, we need to preserve the task_ prefix
      if (event.is_task) {
        // If it already has the task_ prefix, use it as is
        if (typeof event.id === 'string' && event.id.startsWith('task_')) {
          return event.id;
        }
        // Otherwise, add the prefix
        return `task_${event.id}`;
      }

      // For regular events, just use the ID as is
      return event.id;
    };

    return (
      <div
        className={cn(
          "px-1 py-0.5 rounded-md text-xs font-medium border mb-0.5 truncate hover:shadow-sm transition-shadow cursor-pointer",
          typeColors[event.type]
        )}
        onClick={(e) => {
          e.stopPropagation();
          navigate(`/calendar/events/${getEventIdForUrl()}`);
        }}
      >
        <div className="flex items-center space-x-1">
          {event.priority === 'high' && <span className="inline-block w-1.5 h-1.5 bg-red-500 dark:bg-red-400 rounded-full"></span>}
          <span className="truncate text-[10px]">{event.title}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader />
        </div>
      ) : (
        <main className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(-1)}
                    className="p-0 h-8 w-8"
                  >
                    <ArrowLeft className="h-5 w-5" />
                  </Button>
                  <h1 className="text-3xl font-bold">Project Calendar</h1>
                </div>
                <p className="text-muted-foreground mt-1">
                  {project?.name} - View and manage project tasks and deadlines
                </p>
              </div>

              <div className="flex items-center space-x-3">
                <Button variant="outline" size="sm" onClick={prevMonth}>
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm" className="font-normal">
                      {format(currentMonth, 'MMMM yyyy')}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="center">
                    <Calendar
                      mode="single"
                      defaultMonth={currentMonth}
                      onMonthChange={setCurrentMonth}
                      className="p-1 pointer-events-auto"
                    />
                  </PopoverContent>
                </Popover>

                <Button variant="outline" size="sm" onClick={nextMonth}>
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>

                <Button variant="outline" size="sm" onClick={goToToday}>
                  Today
                </Button>

                <Button
                  className="ml-2"
                  size="sm"
                  onClick={() => navigate(`/calendar/create?project_id=${projectId}`)}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  New Event
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={() => navigate(`/projects/${projectId}/reports`)}
                >
                  <BarChart className="h-4 w-4 mr-1" />
                  Reports
                </Button>
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 bg-background/60 backdrop-blur-sm border rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Filters:</span>
              </div>

              <div className="flex items-center space-x-3 flex-wrap gap-y-2">
                <Select value={filterType ?? "all"} onValueChange={(value) => setFilterType(value === "all" ? null : value)}>
                  <SelectTrigger className="h-8 w-[150px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="task">Tasks</SelectItem>
                    <SelectItem value="deadline">Deadlines</SelectItem>
                    <SelectItem value="meeting">Meetings</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filterAssignee ?? "all"} onValueChange={(value) => setFilterAssignee(value === "all" ? null : value)}>
                  <SelectTrigger className="h-8 w-[150px]">
                    <SelectValue placeholder="Assignee" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Assignees</SelectItem>
                    {project?.members && project.members.map(member => (
                      <SelectItem key={member.id} value={member.id.toString()}>
                        {member.first_name} {member.last_name}
                      </SelectItem>
                    ))}
                    {(!project?.members || project.members.length === 0) && (
                      <SelectItem value="none" disabled>No team members</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Status Legend */}
            <div className="flex items-center space-x-4 text-sm">
              <span className="font-medium">Status:</span>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/30">Completed</Badge>
                <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">In Progress</Badge>
                <Badge variant="outline" className="bg-orange-500/10 text-orange-500 dark:text-orange-400 border-orange-500/30">Not Started</Badge>
              </div>
            </div>

            {/* Calendar grid */}
            <div className="grid grid-cols-1 md:grid-cols-7 gap-2">
              {/* Day headers (only on md and up) */}
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="hidden md:flex justify-center items-center h-8 font-medium text-xs text-muted-foreground">
                  {day}
                </div>
              ))}

              {/* Calendar days */}
              {monthDays.map((day) => {
                const dayEvents = getEventsForDay(day);
                const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;

                return (
                  <div
                    key={day.toString()}
                    className={cn(
                      "min-h-24 p-1 border rounded-lg transition-colors",
                      isToday(day) ? "bg-primary/5 border-primary/20" : "bg-card hover:bg-accent/5",
                      isSelected ? "ring-1 ring-primary" : "",
                      !isSameMonth(day, currentMonth) ? "opacity-50" : ""
                    )}
                    onClick={() => setSelectedDate(day)}
                  >
                    {/* Day header with date */}
                    <div className="flex justify-between items-center mb-1">
                      <span className={cn(
                        "text-xs font-medium",
                        isToday(day) ? "text-primary" : ""
                      )}>
                        {format(day, 'd')}
                      </span>

                      {/* Show day of week on mobile */}
                      <span className="md:hidden text-xs text-muted-foreground">
                        {format(day, 'EEE')}
                      </span>

                      {/* Event counter */}
                      {dayEvents.length > 0 && (
                        <Badge variant="secondary" className="text-xs h-4 px-1">
                          {dayEvents.length}
                        </Badge>
                      )}
                    </div>

                    {/* Day events */}
                    <div className="space-y-0.5">
                      {dayEvents.slice(0, 2).map((event) => (
                        <EventItem key={event.id} event={event} />
                      ))}

                      {dayEvents.length > 2 && (
                        <div className="text-xs text-muted-foreground text-center">
                          +{dayEvents.length - 2} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Selected date details */}
            {selectedDate && (
              <div className="bg-card border rounded-lg p-3 mt-3">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-base font-semibold">
                    {format(selectedDate, 'EEEE, MMMM d, yyyy')}
                  </h2>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8"
                    onClick={() => navigate(`/calendar/create/${format(selectedDate, 'yyyy-MM-dd')}?project_id=${projectId}`)}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add Event
                  </Button>
                </div>

                {/* Events for selected date */}
                {getEventsForDay(selectedDate).length > 0 ? (
                  <div className="space-y-3">
                    {getEventsForDay(selectedDate).map((event) => (
                      <div key={event.id} className="p-3 border rounded-lg">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-medium">{event.title}</h3>
                            <p className="text-sm text-muted-foreground mt-1">{event.description}</p>
                          </div>
                          <Badge variant="outline" className={
                            event.status === 'completed' ? 'bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/30' :
                            event.status === 'in-progress' ? 'bg-primary/10 text-primary border-primary/30' :
                            'bg-orange-500/10 text-orange-500 dark:text-orange-400 border-orange-500/30'
                          }>
                            {event.status === 'not-started' ? 'Not Started' :
                             event.status === 'in-progress' ? 'In Progress' : 'Completed'}
                          </Badge>
                        </div>

                        {event.assignees.length > 0 && (
                          <div className="mt-3">
                            <p className="text-xs text-muted-foreground mb-1">Assignees:</p>
                            <div className="flex -space-x-2">
                              {event.assignees.map((assignee, index) => (
                                <Avatar key={index} className="h-6 w-6 border-2 border-background">
                                  {assignee.avatar ? (
                                    <AvatarImage src={assignee.avatar} alt={assignee.name} />
                                  ) : (
                                    <AvatarFallback className="text-xs">
                                      {assignee.name.charAt(0)}
                                    </AvatarFallback>
                                  )}
                                </Avatar>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <div className="flex justify-center mb-2">
                      <CalendarIcon className="h-10 w-10 opacity-20" />
                    </div>
                    <p>No events scheduled for this day</p>
                    <Button
                      variant="link"
                      size="sm"
                      className="mt-2"
                      onClick={() => navigate(`/calendar/create/${format(selectedDate, 'yyyy-MM-dd')}?project_id=${projectId}`)}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Create an event
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      )}
    </div>
  );
};

export default ProjectCalendar;
