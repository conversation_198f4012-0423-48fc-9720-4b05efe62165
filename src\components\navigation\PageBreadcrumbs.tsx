import React, { useMemo } from 'react';
import { Link, useLocation, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import {
  Home,
  Briefcase,
  LayoutDashboard,
  Kanban,
  List as ListIcon,
  Calendar,
  BarChart,
  Clock,
  Settings,
  User,
  Users,
  Activity,
  Search
} from 'lucide-react';
import { useWorkspace } from '@/context/WorkspaceContext';
import { cn } from '@/lib/utils';

interface BreadcrumbItemType {
  label: string;
  path: string;
  icon?: React.ReactNode;
  current?: boolean;
}

interface PageBreadcrumbsProps {
  className?: string;
  items?: BreadcrumbItemType[];
  projectName?: string;
  workspaceName?: string;
}

const PageBreadcrumbs: React.FC<PageBreadcrumbsProps> = ({
  className,
  items: customItems,
  projectName,
  workspaceName
}) => {
  const { t } = useTranslation();
  const location = useLocation();
  const params = useParams();
  const { currentWorkspace } = useWorkspace();

  // Generate breadcrumb items based on the current route
  const breadcrumbItems = useMemo(() => {
    if (customItems) return customItems;

    const items: BreadcrumbItemType[] = [];
    const pathSegments = location.pathname.split('/').filter(Boolean);

    // Always start with home
    items.push({
      label: t('breadcrumb.home', 'Home'),
      path: '/dashboard',
      icon: <Home className="h-4 w-4" />
    });

    // Build breadcrumb items based on path segments
    let currentPath = '';

    for (let i = 0; i < pathSegments.length; i++) {
      const segment = pathSegments[i];
      currentPath += `/${segment}`;

      // Skip certain segments that don't need to be in breadcrumbs
      if (['projects'].includes(segment)) continue;

      // Handle workspace
      if (segment === 'workspace') {
        const workspaceId = pathSegments[i + 1];
        if (workspaceId) {
          items.push({
            label: workspaceName || currentWorkspace?.name || t('breadcrumb.workspace', 'Workspace'),
            path: `/workspace/${workspaceId}`,
            icon: <Briefcase className="h-4 w-4" />
          });
          i++; // Skip the ID segment
        }
        continue;
      }

      // Handle project views
      if (params.projectId && ['board', 'list', 'calendar', 'reports', 'timeline'].includes(segment)) {
        const viewLabels: Record<string, { label: string, icon: React.ReactNode }> = {
          'board': { label: t('breadcrumb.kanban', 'Kanban'), icon: <Kanban className="h-4 w-4" /> },
          'list': { label: t('breadcrumb.list', 'List'), icon: <ListIcon className="h-4 w-4" /> },
          'calendar': { label: t('breadcrumb.calendar', 'Calendar'), icon: <Calendar className="h-4 w-4" /> },
          'reports': { label: t('breadcrumb.reports', 'Reports'), icon: <BarChart className="h-4 w-4" /> },
          'timeline': { label: t('breadcrumb.timeline', 'Timeline'), icon: <Clock className="h-4 w-4" /> }
        };

        items.push({
          label: viewLabels[segment].label,
          path: currentPath,
          icon: viewLabels[segment].icon,
          current: true
        });
        continue;
      }

      // Handle settings
      if (segment === 'settings') {
        items.push({
          label: t('breadcrumb.settings', 'Settings'),
          path: currentPath,
          icon: <Settings className="h-4 w-4" />,
          current: true
        });
        continue;
      }

      // Handle profile
      if (segment === 'profile') {
        items.push({
          label: t('breadcrumb.profile', 'Profile'),
          path: currentPath,
          icon: <User className="h-4 w-4" />,
          current: true
        });
        continue;
      }

      // Handle team
      if (segment === 'team') {
        items.push({
          label: t('breadcrumb.team', 'Team'),
          path: currentPath,
          icon: <Users className="h-4 w-4" />,
          current: true
        });
        continue;
      }

      // Handle activity
      if (segment === 'activity') {
        items.push({
          label: t('breadcrumb.activity', 'Activity'),
          path: currentPath,
          icon: <Activity className="h-4 w-4" />,
          current: true
        });
        continue;
      }

      // Handle search
      if (segment === 'search') {
        items.push({
          label: t('breadcrumb.search', 'Search Results'),
          path: currentPath,
          icon: <Search className="h-4 w-4" />,
          current: true
        });
        continue;
      }

      // Handle project ID (replace with project name if available)
      if (params.projectId && segment === params.projectId) {
        items.push({
          label: projectName || t('breadcrumb.project', 'Project'),
          path: `/projects/${segment}`,
          icon: <LayoutDashboard className="h-4 w-4" />,
          current: i === pathSegments.length - 1
        });
        continue;
      }

      // Default handling for other segments
      items.push({
        label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
        path: currentPath,
        current: i === pathSegments.length - 1
      });
    }

    return items;
  }, [location.pathname, params, customItems, currentWorkspace, projectName, workspaceName, t]);

  // Don't render if we only have the home item
  if (breadcrumbItems.length <= 1) return null;

  return (
    <Breadcrumb className={cn("mb-4", className)}>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={`item-${item.path}`}>
            <BreadcrumbItem>
              {item.current ? (
                <BreadcrumbPage className="flex items-center gap-1.5">
                  {item.icon}
                  <span>{item.label}</span>
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild className="flex items-center gap-1.5">
                  <Link to={item.path}>
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>

            {index < breadcrumbItems.length - 1 && (
              <BreadcrumbSeparator />
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default PageBreadcrumbs;
