import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

/**
 * Enhanced Progress component with robust value handling and visual feedback
 *
 * Features:
 * - Validates and normalizes the value to ensure it's between 0-100
 * - Provides color-coded feedback based on progress percentage
 * - Includes smooth animations for value changes
 * - Maintains accessibility features from Radix UI
 */
const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, value, ...props }, ref) => {
  // Ensure value is a valid number between 0-100
  const safeValue = React.useMemo(() => {
    // Handle various input types and edge cases
    if (typeof value !== 'number' || isNaN(value)) return 0;
    if (value < 0) return 0;
    if (value > 100) return 100;
    return value;
  }, [value]);

  // Determine color based on progress level
  const progressColor = React.useMemo(() => {
    if (safeValue === 100) return "bg-green-500";
    if (safeValue >= 75) return "bg-emerald-500";
    if (safeValue >= 50) return "bg-primary";
    if (safeValue >= 25) return "bg-amber-500";
    return "bg-primary";
  }, [safeValue]);

  return (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative h-4 w-full overflow-hidden rounded-full bg-secondary border border-border",
        className
      )}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn(
          "h-full w-full flex-1 transition-all duration-300 ease-in-out",
          progressColor
        )}
        style={{ transform: `translateX(-${100 - safeValue}%)` }}
      />
    </ProgressPrimitive.Root>
  );
})
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }
