/* Navbar responsive styles */
@media (max-width: 640px) {
  /* Small screens */
  .navbar-container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .navbar-logo {
    max-width: 120px;
  }

  .navbar-actions {
    gap: 0.25rem;
  }
}

/* Fix for search dropdown on small screens */
.search-dropdown {
  max-width: calc(100vw - 2rem);
  width: 100%;
  right: -0.5rem;
}

@media (min-width: 640px) {
  .search-dropdown {
    width: 16rem;
    right: 0;
  }
}

/* Fix for notification dropdown positioning */
.notification-dropdown {
  right: -0.5rem;
}

/* Fix for profile dropdown positioning */
.profile-dropdown {
  right: -0.5rem;
}

/* Ensure mobile menu has proper z-index */
.mobile-menu {
  z-index: 40;
}

/* Ensure proper spacing between navbar items */
.navbar-item {
  margin-right: 0.25rem;
}

@media (min-width: 640px) {
  .navbar-item {
    margin-right: 0.5rem;
  }
}

/* Fix for navbar overflow issues */
@media (max-width: 768px) {
  nav.bg-background {
    overflow-x: hidden;
  }

  /* Ensure dropdowns don't overflow the screen */
  .profile-dropdown,
  .notification-dropdown,
  .search-dropdown {
    max-width: calc(100vw - 2rem);
    width: auto;
    min-width: 200px;
  }
}

/* Fix for mobile menu button positioning */
@media (max-width: 640px) {
  button[aria-label="Search"],
  button[aria-label="Notifications"],
  button[aria-label="Light Mode"],
  button[aria-label="Dark Mode"] {
    padding: 0.375rem;
  }
}

/* Ensure proper spacing in the mobile menu */
.mobile-menu .px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Fix for workspace switcher in mobile view */
.mobile-menu .px-4 button {
  width: 100%;
}
