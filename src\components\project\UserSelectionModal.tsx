import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { User } from '@/entities/User';
import { getProjectRoles } from '@/api/projectsApi';
import { Loader2, Settings } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useNavigate } from 'react-router-dom';
import { getFullImageUrl } from '@/utils/imageUtils';

interface UserSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (userId: number) => void;
  title: string;
  description: string;
  actionText: string;
  projectId: number;
  currentUserId: number;
}

interface UserWithRole extends User {
  role?: {
    id: number;
    name: string;
  };
}

const UserSelectionModal: React.FC<UserSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  title,
  description,
  actionText,
  projectId,
  currentUserId
}) => {
  const [users, setUsers] = useState<UserWithRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const { t } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen) {
      loadUsers();
    }
  }, [isOpen, projectId]);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const response = await getProjectRoles(projectId);

      // Filter out the current user and include all project members
      const filteredUsers = response.users
        .filter((user: UserWithRole) => user.id !== currentUserId)
        .map((user: UserWithRole) => ({
          ...user,
          role: response.roles.find((role: any) => role.id === user.pivot.role_id)
        }));

      setUsers(filteredUsers);
    } catch (error) {
      console.error('Error loading project users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelect = () => {
    if (selectedUserId) {
      onSelect(selectedUserId);
    }
  };

  const getInitials = (user: UserWithRole) => {
    return `${user.first_name?.charAt(0) || ''}${user.last_name?.charAt(0) || ''}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : users.length === 0 ? (
          <div className="py-6">
            <div className="bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-md p-4 mb-3">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">No Project Members Available</h3>
                  <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                    <p>{t('project.transfer.noUsers', 'No other users available to transfer ownership to.')}</p>
                    <p className="mt-2">To transfer ownership, you need to:</p>
                    <ol className="list-decimal pl-5 mt-1 space-y-1">
                      <li>Go to the project settings</li>
                      <li>Add users to the project</li>
                      <li>Return here to transfer ownership</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-4 flex justify-center">
              <Button
                variant="outline"
                onClick={() => {
                  onClose();
                  // Navigate to project settings with the members tab active
                  navigate(`/projects/${projectId}/edit?tab=members`);
                }}
              >
                <Settings className="mr-2 h-4 w-4" />
                Go to Project Settings
              </Button>
            </div>
          </div>
        ) : (
          <div className="py-4">
            <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
              {users.map((user) => (
                <div
                  key={user.id}
                  className={`flex items-center p-3 rounded-md cursor-pointer transition-colors ${
                    selectedUserId === user.id
                      ? 'bg-primary/10 border border-primary/30'
                      : 'hover:bg-muted border border-transparent'
                  }`}
                  onClick={() => setSelectedUserId(user.id)}
                >
                  <Avatar className="h-10 w-10 mr-3">
                    {user.profile_picture ? (
                      <AvatarImage src={getFullImageUrl(user.profile_picture)} alt={`${user.first_name} ${user.last_name}`} />
                    ) : (
                      <AvatarFallback className="bg-primary/10 text-primary dark:bg-white/90 dark:text-primary">{getInitials(user)}</AvatarFallback>
                    )}
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium">{`${user.first_name} ${user.last_name}`}</div>
                    <div className="text-sm text-muted-foreground">{user.email}</div>
                  </div>
                  {user.role && (
                    <div className="ml-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                        {user.role.name}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        <DialogFooter className="flex flex-col sm:flex-row sm:justify-between gap-2">
          <Button variant="outline" onClick={onClose}>
            {t('common.cancel', 'Cancel')}
          </Button>
          {users.length > 0 ? (
            <Button
              onClick={handleSelect}
              disabled={!selectedUserId || loading}
              className="sm:ml-2"
            >
              {actionText}
            </Button>
          ) : (
            <Button
              disabled={true}
              className="sm:ml-2"
            >
              {actionText}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserSelectionModal;
