import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ArrowDown,
  ArrowUp,
  Check,
  ChevronDown,
  Clock,
  Edit,
  Eye,
  Filter,
  MoreHorizontal,
  Search,
  Trash2
} from 'lucide-react';
import { Task } from '@/api/tasksApi';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { StatusBadge } from '@/components/ui/status-badge';
import { PriorityIndicator } from '@/components/ui/priority-indicator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { getFullImageUrl } from '@/utils/imageUtils';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';

interface TaskTableProps {
  tasks: Task[];
  project: Project;
  currentUser: User | null;
  onTaskClick: (task: Task) => void;
  onEditTask?: (task: Task) => void;
  onDeleteTask?: (task: Task) => void;
}

type SortField = 'title' | 'priority' | 'status' | 'due_date' | 'assignees';
type SortDirection = 'asc' | 'desc';

const TaskTable: React.FC<TaskTableProps> = ({
  tasks,
  project,
  currentUser,
  onTaskClick,
  onEditTask,
  onDeleteTask
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<SortField>('title');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  // Check permissions
  const canEditTask = hasProjectPermission(project, currentUser, ProjectPermission.EDIT_TASK);
  const canDeleteTask = hasProjectPermission(project, currentUser, ProjectPermission.DELETE_TASK);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort tasks
  const filteredAndSortedTasks = tasks
    .filter(task => {
      // Search filter
      const matchesSearch = searchQuery === '' ||
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      const matchesStatus = statusFilter === 'all' || task.status === statusFilter;

      // Priority filter
      const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;

      return matchesSearch && matchesStatus && matchesPriority;
    })
    .sort((a, b) => {
      // Sort by field
      if (sortField === 'title') {
        return sortDirection === 'asc'
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);
      }

      if (sortField === 'priority') {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
        const bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      if (sortField === 'status') {
        const statusOrder = { 'at-risk': 4, 'todo': 3, 'in-progress': 2, 'completed': 1 };
        const aValue = statusOrder[a.status as keyof typeof statusOrder] || 0;
        const bValue = statusOrder[b.status as keyof typeof statusOrder] || 0;
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      if (sortField === 'due_date') {
        const aDate = a.end_date ? new Date(a.end_date).getTime() : 0;
        const bDate = b.end_date ? new Date(b.end_date).getTime() : 0;
        return sortDirection === 'asc' ? aDate - bDate : bDate - aDate;
      }

      if (sortField === 'assignees') {
        const aCount = a.assignees?.length || 0;
        const bCount = b.assignees?.length || 0;
        return sortDirection === 'asc' ? aCount - bCount : bCount - aCount;
      }

      return 0;
    });

  // Render sort indicator
  const renderSortIndicator = (field: SortField) => {
    if (sortField !== field) return null;

    return sortDirection === 'asc'
      ? <ArrowUp className="ml-1 h-4 w-4" />
      : <ArrowDown className="ml-1 h-4 w-4" />;
  };

  return (
    <div className="space-y-4">
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
        <div className="flex flex-wrap gap-2 items-center">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={t('task.searchTasks', 'Search tasks...')}
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[130px]">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <SelectValue placeholder={t('task.status', 'Status')} />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('task.allStatuses', 'All Statuses')}</SelectItem>
              <SelectItem value="todo">{t('status.todo', 'To Do')}</SelectItem>
              <SelectItem value="in-progress">{t('status.inProgress', 'In Progress')}</SelectItem>
              <SelectItem value="completed">{t('status.completed', 'Completed')}</SelectItem>
              <SelectItem value="at-risk">{t('status.atRisk', 'At Risk')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-[130px]">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <SelectValue placeholder={t('task.priority', 'Priority')} />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('task.allPriorities', 'All Priorities')}</SelectItem>
              <SelectItem value="high">{t('priority.high', 'High')}</SelectItem>
              <SelectItem value="medium">{t('priority.medium', 'Medium')}</SelectItem>
              <SelectItem value="low">{t('priority.low', 'Low')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-muted-foreground">
          {filteredAndSortedTasks.length} {t('task.tasksFound', 'tasks found')}
        </div>
      </div>

      {/* Tasks Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50 hover:bg-muted">
              <TableHead
                className="cursor-pointer w-[40%]"
                onClick={() => handleSort('title')}
              >
                <div className="flex items-center">
                  {t('task.title', 'Title')}
                  {renderSortIndicator('title')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  {t('task.status', 'Status')}
                  {renderSortIndicator('status')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('priority')}
              >
                <div className="flex items-center">
                  {t('task.priority', 'Priority')}
                  {renderSortIndicator('priority')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('due_date')}
              >
                <div className="flex items-center">
                  {t('task.dueDate', 'Due Date')}
                  {renderSortIndicator('due_date')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('assignees')}
              >
                <div className="flex items-center">
                  {t('task.assignees', 'Assignees')}
                  {renderSortIndicator('assignees')}
                </div>
              </TableHead>
              <TableHead className="text-right">{t('task.actions', 'Actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedTasks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  {t('task.noTasksFound', 'No tasks found.')}
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedTasks.map((task) => (
                <TableRow
                  key={task.id}
                  className="cursor-pointer hover:bg-accent/10"
                  onClick={() => onTaskClick(task)}
                >
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <span className="line-clamp-1">{task.title}</span>
                      {task.description && (
                        <span className="text-xs text-muted-foreground line-clamp-1 mt-1">
                          {task.description}
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <StatusBadge
                      status={task.status as any}
                      size="sm"
                      variant="pill"
                    />
                  </TableCell>
                  <TableCell>
                    <PriorityIndicator
                      priority={task.priority as any}
                      size="sm"
                      showLabel
                    />
                  </TableCell>
                  <TableCell>
                    {task.end_date ? (
                      <div className="flex items-center text-sm">
                        <Clock className="mr-1 h-3.5 w-3.5 text-muted-foreground" />
                        {new Date(task.end_date).toLocaleDateString()}
                      </div>
                    ) : (
                      <span className="text-xs text-muted-foreground">
                        {t('task.noDueDate', 'No due date')}
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    {task.assignees && task.assignees.length > 0 ? (
                      <div className="flex -space-x-2">
                        {task.assignees.slice(0, 3).map((assignee) => (
                          <Avatar key={assignee.id} className="h-6 w-6 border-2 border-background">
                            <AvatarImage
                              src={assignee.profile_picture ? getFullImageUrl(assignee.profile_picture) : ''}
                              alt={`${assignee.first_name} ${assignee.last_name}`}
                            />
                            <AvatarFallback className="text-xs bg-primary/10 text-primary dark:bg-white/90 dark:text-primary">
                              {assignee.first_name?.[0]}{assignee.last_name?.[0]}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                        {task.assignees.length > 3 && (
                          <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs border-2 border-background">
                            +{task.assignees.length - 3}
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-xs text-muted-foreground">
                        {t('task.unassigned', 'Unassigned')}
                      </span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">{t('task.actions', 'Actions')}</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onTaskClick(task);
                        }}>
                          <Eye className="mr-2 h-4 w-4" />
                          {t('task.view', 'View Details')}
                        </DropdownMenuItem>

                        {canEditTask && onEditTask && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            onEditTask(task);
                          }}>
                            <Edit className="mr-2 h-4 w-4" />
                            {t('task.edit', 'Edit')}
                          </DropdownMenuItem>
                        )}

                        {canDeleteTask && onDeleteTask && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={(e) => {
                                e.stopPropagation();
                                onDeleteTask(task);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t('task.delete', 'Delete')}
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default TaskTable;
