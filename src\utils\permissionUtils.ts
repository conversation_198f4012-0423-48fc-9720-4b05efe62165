import { Project } from '@/entities/Project';
import { User } from '@/entities/User';

// Define all possible permissions
export enum ProjectPermission {
  // Project permissions
  VIEW_PROJECT = 'view_project',
  EDIT_PROJECT = 'edit_project',
  DELETE_PROJECT = 'delete_project',

  // Task permissions
  CREATE_TASK = 'create_task',
  VIEW_TASK = 'view_task',
  EDIT_TASK = 'edit_task',
  DELETE_TASK = 'delete_task',
  ASSIGN_TASK = 'assign_task',
  COMPLETE_TASK = 'complete_task',

  // Member permissions
  VIEW_MEMBERS = 'view_members',
  ADD_MEMBER = 'add_member',
  REMOVE_MEMBER = 'remove_member',
  CHANGE_MEMBER_ROLE = 'change_member_role',

  // Comment permissions
  ADD_COMMENT = 'add_comment',
  EDIT_COMMENT = 'edit_comment',
  DELETE_COMMENT = 'delete_comment',

  // Attachment permissions
  ADD_ATTACHMENT = 'add_attachment',
  DELETE_ATTACHMENT = 'delete_attachment',

  // Special permissions
  ALL = 'all'
}

// Role IDs
export enum RoleId {
  ADMIN = 1,
  EDITOR = 2,
  VIEWER = 3,
}

// Map role IDs to permissions
const rolePermissions: Record<RoleId, ProjectPermission[]> = {
  [RoleId.ADMIN]: [
    // Project permissions
    ProjectPermission.VIEW_PROJECT,
    ProjectPermission.EDIT_PROJECT,
    ProjectPermission.DELETE_PROJECT,

    // Task permissions
    ProjectPermission.CREATE_TASK,
    ProjectPermission.VIEW_TASK,
    ProjectPermission.EDIT_TASK,
    ProjectPermission.DELETE_TASK,
    ProjectPermission.ASSIGN_TASK,
    ProjectPermission.COMPLETE_TASK,

    // Member permissions
    ProjectPermission.ADD_MEMBER,
    ProjectPermission.REMOVE_MEMBER,
    ProjectPermission.CHANGE_MEMBER_ROLE,

    // Comment permissions
    ProjectPermission.ADD_COMMENT,
    ProjectPermission.EDIT_COMMENT,
    ProjectPermission.DELETE_COMMENT,

    // Attachment permissions
    ProjectPermission.ADD_ATTACHMENT,
    ProjectPermission.DELETE_ATTACHMENT,

    // Special permissions
    ProjectPermission.ALL
  ],
  [RoleId.EDITOR]: [
    // Project permissions
    ProjectPermission.VIEW_PROJECT,
    ProjectPermission.EDIT_PROJECT,

    // Task permissions
    ProjectPermission.CREATE_TASK,
    ProjectPermission.VIEW_TASK,
    ProjectPermission.EDIT_TASK,
    ProjectPermission.DELETE_TASK,
    ProjectPermission.ASSIGN_TASK,
    ProjectPermission.COMPLETE_TASK,

    // Member permissions
    ProjectPermission.VIEW_MEMBERS,
    ProjectPermission.ADD_MEMBER,

    // Comment permissions
    ProjectPermission.ADD_COMMENT,
    ProjectPermission.EDIT_COMMENT,

    // Attachment permissions
    ProjectPermission.ADD_ATTACHMENT,
  ],
  [RoleId.VIEWER]: [
    // Project permissions
    ProjectPermission.VIEW_PROJECT,

    // Task permissions
    ProjectPermission.VIEW_TASK,
    // Viewers should not be able to edit tasks
    // ProjectPermission.EDIT_TASK, // Limited to assigned tasks
    ProjectPermission.COMPLETE_TASK, // Limited to assigned tasks only

    // Comment permissions
    ProjectPermission.ADD_COMMENT,

    // Attachment permissions
    ProjectPermission.ADD_ATTACHMENT,
  ],
};

// Get the user's role ID in a project
export const getRoleId = (project: Project | undefined, user: User | null): RoleId | null => {
  if (!project || !user) return null;

  // Project creator is always an admin
  if (project.user_id === user.id) {
    return RoleId.ADMIN;
  }

  // If it's not a group project, only the owner has a role
  if (!project.is_group_project) {
    return null;
  }

  const projectUser = project.members?.find(member => member.id === user.id);
  if (!projectUser || !projectUser.pivot) return null;

  return projectUser.pivot.role_id as RoleId;
};

// Check if a user has a specific permission in a project
export const hasProjectPermission = (
  project: Project | undefined,
  user: User | null,
  permission: ProjectPermission
): boolean => {
  // For static site demo, always return true to allow access to all projects
  if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
    return true;
  }

  // Project creator is always an admin with all permissions
  if (project && user && project.user_id === user.id) {
    return true;
  }

  // If it's not a group project, only the owner has permissions
  if (project && !project.is_group_project && project.user_id !== user?.id) {
    return false;
  }

  const roleId = getRoleId(project, user);
  if (roleId && rolePermissions[roleId].includes(permission)) {
    return true;
  }

  return false;
};

// Get all permissions for a user in a project
export const getUserPermissions = (
  project: Project | undefined,
  user: User | null
): ProjectPermission[] => {
  // For static site demo, always return admin permissions
  if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
    return rolePermissions[RoleId.ADMIN];
  }

  // Project creator is always an admin with all permissions
  if (project && user && project.user_id === user.id) {
    return rolePermissions[RoleId.ADMIN];
  }

  // If it's not a group project, only the owner has permissions
  if (project && !project.is_group_project && project.user_id !== user?.id) {
    return [];
  }

  const roleId = getRoleId(project, user);
  if (roleId) {
    return rolePermissions[roleId];
  }

  return [];
};

/**
 * Check if a user has all of the specified permissions in a project
 */
export const hasAllProjectPermissions = (
  project: Project | undefined,
  user: User | null,
  permissions: ProjectPermission[]
): boolean => {
  // Project creator is always an admin with all permissions
  if (project && user && project.user_id === user.id) {
    return true;
  }

  const userPermissions = getUserPermissions(project, user);

  // If user has ALL permission, they have all permissions
  if (userPermissions.includes(ProjectPermission.ALL)) {
    return true;
  }

  // Check if user has all the specified permissions
  return permissions.every(permission => userPermissions.includes(permission));
};

/**
 * Check if a user has any of the specified permissions in a project
 */
export const hasAnyProjectPermission = (
  project: Project | undefined,
  user: User | null,
  permissions: ProjectPermission[]
): boolean => {
  // Project creator is always an admin with all permissions
  if (project && user && project.user_id === user.id) {
    return true;
  }

  const userPermissions = getUserPermissions(project, user);

  // If user has ALL permission, they have all permissions
  if (userPermissions.includes(ProjectPermission.ALL)) {
    return true;
  }

  // Check if user has any of the specified permissions
  return permissions.some(permission => userPermissions.includes(permission));
};

/**
 * Check if a user can edit a specific task
 * Viewers can only edit tasks assigned to them
 */
export const canEditTask = (
  project: Project | undefined,
  user: User | null,
  task: any
): boolean => {
  // For static site demo, always return true to allow editing all tasks
  if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
    return true;
  }

  // Project creator is always an admin with all permissions
  if (project && user && project.user_id === user.id) {
    return true;
  }

  // If it's not a group project, only the owner can edit tasks
  if (project && !project.is_group_project && project.user_id !== user?.id) {
    return false;
  }

  const roleId = getRoleId(project, user);
  if (!roleId) {
    return false;
  }

  // Admins and Editors can edit any task
  if (roleId === RoleId.ADMIN || roleId === RoleId.EDITOR) {
    return true;
  }

  // Viewers cannot edit tasks
  if (roleId === RoleId.VIEWER) {
    return false;
  }

  return false;
};

// Get role name from role ID
export const getRoleName = (roleId: RoleId | null): string => {
  if (!roleId) return 'Unknown';

  switch (roleId) {
    case RoleId.ADMIN:
      return 'Admin';
    case RoleId.EDITOR:
      return 'Editor';
    case RoleId.VIEWER:
      return 'Viewer';
    default:
      return 'Unknown';
  }
};
