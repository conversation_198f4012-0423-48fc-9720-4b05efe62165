import React, { useState, useEffect, useRef } from 'react';
import { Check, ChevronsUpDown, X, Search, User as UserIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User } from '@/entities/User';
import { ScrollArea } from '@/components/ui/scroll-area';

interface UserMultiSelectProps {
  users: User[];
  selectedUserIds: number[];
  onChange: (selectedIds: number[]) => void;
  placeholder?: string;
  disabled?: boolean;
}

const UserMultiSelect: React.FC<UserMultiSelectProps> = ({
  users,
  selectedUserIds,
  onChange,
  placeholder = 'Select users',
  disabled = false,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Update selected users when selectedUserIds or users changes
  useEffect(() => {
    if (!users || !Array.isArray(users)) {
      setSelectedUsers([]);
      return;
    }

    // Make sure selectedUserIds is an array
    const validSelectedIds = Array.isArray(selectedUserIds) ? selectedUserIds : [];

    const selected = users.filter(user => validSelectedIds.includes(user.id));
    setSelectedUsers(selected);
  }, [selectedUserIds, users]);

  // Focus search input when popover opens
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  const handleSelect = (userId: number) => {
    // Make sure selectedUserIds is an array
    const validSelectedIds = Array.isArray(selectedUserIds) ? selectedUserIds : [];
    let newSelectedIds: number[];

    if (validSelectedIds.includes(userId)) {
      // Remove user if already selected
      newSelectedIds = validSelectedIds.filter(id => id !== userId);
    } else {
      // Add user if not selected
      newSelectedIds = [...validSelectedIds, userId];
    }

    onChange(newSelectedIds);
  };

  const removeUser = (userId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    // Make sure selectedUserIds is an array
    const validSelectedIds = Array.isArray(selectedUserIds) ? selectedUserIds : [];
    const newSelectedIds = validSelectedIds.filter(id => id !== userId);
    onChange(newSelectedIds);
  };

  const getUserInitials = (user: User) => {
    return `${user.first_name?.[0] || ''}${user.last_name?.[0] || ''}`;
  };

  // Filter users based on search query
  const filteredUsers = Array.isArray(users)
    ? users.filter(user => {
        const fullName = `${user.first_name} ${user.last_name}`.toLowerCase();
        return fullName.includes(searchQuery.toLowerCase());
      })
    : [];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            selectedUsers.length > 0 ? "h-auto min-h-10 py-2" : "h-10"
          )}
          disabled={disabled}
        >
          <div className="flex flex-wrap gap-1 mr-2">
            {selectedUsers.length > 0 ? (
              selectedUsers.map(user => (
                <Badge
                  key={user.id}
                  variant="secondary"
                  className="flex items-center gap-1 px-1 py-0.5"
                >
                  <Avatar className="h-5 w-5">
                    <AvatarImage src={user.profile_picture} alt={`${user.first_name} ${user.last_name}`} />
                    <AvatarFallback className="text-xs bg-primary/10 text-primary dark:bg-white/90 dark:text-primary">{getUserInitials(user)}</AvatarFallback>
                  </Avatar>
                  <span className="text-xs">
                    {user.first_name} {user.last_name}
                  </span>
                  <span
                    className="ml-1 rounded-full hover:bg-muted p-0.5 cursor-pointer"
                    onClick={(e) => removeUser(user.id, e)}
                  >
                    <X className="h-3 w-3" />
                  </span>
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <div className="flex items-center border-b px-3 py-2">
          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
          <Input
            ref={searchInputRef}
            placeholder="Search users..."
            className="flex h-8 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <ScrollArea className="max-h-64 overflow-y-auto">
          {filteredUsers.length === 0 ? (
            <div className="py-6 text-center text-sm text-muted-foreground">
              No users found.
            </div>
          ) : (
            <div className="p-1">
              {filteredUsers.map(user => (
                <div
                  key={user.id}
                  onClick={() => handleSelect(user.id)}
                  className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                >
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src={user.profile_picture} alt={`${user.first_name} ${user.last_name}`} />
                    <AvatarFallback className="text-xs bg-primary/10 text-primary dark:bg-white/90 dark:text-primary">{getUserInitials(user)}</AvatarFallback>
                  </Avatar>
                  <span>{user.first_name} {user.last_name}</span>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      Array.isArray(selectedUserIds) && selectedUserIds.includes(user.id) ? "opacity-100" : "opacity-0"
                    )}
                  />
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};

export default UserMultiSelect;
