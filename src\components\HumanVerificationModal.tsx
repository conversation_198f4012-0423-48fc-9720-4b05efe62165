import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Shield, CheckCircle2 } from 'lucide-react';
import axios from 'axios';
import { API_BASE_URL } from '@/config';

interface HumanVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerificationSuccess: (token: string) => void;
}

const HumanVerificationModal: React.FC<HumanVerificationModalProps> = ({
  isOpen,
  onClose,
  onVerificationSuccess
}) => {
  const [question, setQuestion] = useState<string>('');
  const [answer, setAnswer] = useState<string>('');
  const [verificationToken, setVerificationToken] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch verification question when modal opens
  React.useEffect(() => {
    if (isOpen) {
      fetchQuestion();
    }
  }, [isOpen]);

  const fetchQuestion = async () => {
    setIsLoading(true);
    setError(null);

    // Generate a simple fallback question in case the API fails
    const generateFallbackQuestion = () => {
      const a = Math.floor(Math.random() * 10) + 1;
      const b = Math.floor(Math.random() * 10) + 1;
      const operators = ['+', '-', '*'];
      const op = operators[Math.floor(Math.random() * operators.length)];

      let answer;
      switch(op) {
        case '+': answer = a + b; break;
        case '-': answer = a - b; break;
        case '*': answer = a * b; break;
        default: answer = a + b;
      }

      return {
        question: `What is ${a} ${op} ${b}?`,
        answer: answer,
        token: 'fallback-' + Date.now()
      };
    };

    try {
      // Try the API endpoint with /api prefix first
      try {
        const response = await axios.get(`${API_BASE_URL}/human-verification`, {
          headers: {
            'Accept': 'application/json'
          }
        });

        setQuestion(response.data.question);
        setVerificationToken(response.data.verification_token);
      } catch (apiError) {
        // If that fails, try without the /api prefix
        try {
          const response = await axios.get(`http://127.0.0.1:8000/human-verification`, {
            headers: {
              'Accept': 'application/json'
            }
          });

          setQuestion(response.data.question);
          setVerificationToken(response.data.verification_token);
        } catch (webError) {
          // If both fail, use the fallback
          throw new Error('Both API and web routes failed');
        }
      }
    } catch (err) {
      console.error('Error fetching verification question:', err);

      // Use fallback question
      const fallback = generateFallbackQuestion();
      setQuestion(fallback.question);
      setVerificationToken(fallback.token);

      // Store the answer in localStorage for verification
      localStorage.setItem(`fallback_answer:${fallback.token}`, fallback.answer.toString());

      setError('Using local verification due to server issues. Please solve the math problem.');
      toast({
        title: 'Server Connection Issue',
        description: 'Using local verification instead',
        variant: 'default'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // Check if this is a fallback verification
    if (verificationToken && verificationToken.startsWith('fallback-')) {
      const storedAnswer = localStorage.getItem(`fallback_answer:${verificationToken}`);

      if (storedAnswer && parseInt(answer) === parseInt(storedAnswer)) {
        // Successful fallback verification
        setIsSuccess(true);

        // Generate a local token
        const localToken = 'local-verified-' + Date.now();
        localStorage.setItem('human_verification_token', localToken);

        // Pass the token to parent
        onVerificationSuccess(localToken);

        // Auto-close after success
        setTimeout(() => {
          onClose();
          setIsSuccess(false);
          setAnswer('');
        }, 3000);

        setIsLoading(false);
        return;
      } else {
        // Failed fallback verification
        setError('Incorrect answer. Please try again.');
        toast({
          title: 'Verification Failed',
          description: 'Please try again with the correct answer',
          variant: 'destructive'
        });
        setIsLoading(false);
        return;
      }
    }

    // Regular API verification
    try {
      // Try the API endpoint with /api prefix first
      try {
        const response = await axios.post(
          `${API_BASE_URL}/human-verification`,
          {
            answer: answer,
            verification_token: verificationToken
          },
          {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.data.success) {
          handleVerificationSuccess(response.data.verification_token);
        }
      } catch (apiError) {
        // If that fails, try without the /api prefix
        try {
          const response = await axios.post(
            `http://127.0.0.1:8000/human-verification`,
            {
              answer: answer,
              verification_token: verificationToken
            },
            {
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            }
          );

          if (response.data.success) {
            handleVerificationSuccess(response.data.verification_token);
          }
        } catch (webError) {
          // If both fail, throw an error
          throw new Error('Both API and web routes failed');
        }
      }
    } catch (err: any) {
      console.error('Verification error:', err);
      setError(err.response?.data?.error || 'Verification failed. Please try again.');
      toast({
        title: 'Verification Failed',
        description: err.response?.data?.error || 'Please try again with the correct answer',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationSuccess = (token: string) => {
    setIsSuccess(true);

    // If we received a verification token, pass it to the parent
    if (token) {
      onVerificationSuccess(token);
    }

    // Auto-close after success
    setTimeout(() => {
      onClose();
      setIsSuccess(false);
      setAnswer('');
    }, 3000);
  };

  const handleRetry = () => {
    setAnswer('');
    fetchQuestion();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isSuccess ? (
              <>
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                Verification Successful
              </>
            ) : (
              <>
                <Shield className="h-5 w-5 text-primary" />
                Human Verification Required
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {isSuccess
              ? 'You have been verified as a human. You can now continue using the application.'
              : 'Our system detected an unusual number of requests. Please solve this simple problem to continue.'}
          </DialogDescription>
        </DialogHeader>

        {isSuccess ? (
          <div className="flex flex-col items-center justify-center py-4">
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
            <p className="text-center text-muted-foreground">
              Verification successful! The page will automatically refresh in a moment.
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              {error && (
                <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md">
                  {error}
                </div>
              )}

              <div className="flex flex-col gap-2">
                <label htmlFor="question" className="text-sm font-medium">
                  Challenge Question:
                </label>
                <div className="bg-muted p-3 rounded-md text-center font-medium">
                  {isLoading ? 'Loading question...' : question}
                </div>
              </div>

              <div className="flex flex-col gap-2">
                <label htmlFor="answer" className="text-sm font-medium">
                  Your Answer:
                </label>
                <Input
                  id="answer"
                  type="number"
                  value={answer}
                  onChange={(e) => setAnswer(e.target.value)}
                  placeholder="Enter your answer"
                  disabled={isLoading || isSuccess}
                  required
                />
              </div>
            </div>

            <DialogFooter className="flex flex-col sm:flex-row sm:justify-between gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleRetry}
                disabled={isLoading || isSuccess}
              >
                Try Different Question
              </Button>
              <Button
                type="submit"
                disabled={isLoading || isSuccess || !answer}
              >
                {isLoading ? 'Verifying...' : 'Verify'}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default HumanVerificationModal;
