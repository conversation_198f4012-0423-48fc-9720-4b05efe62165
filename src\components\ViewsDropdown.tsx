import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from '@/components/ui/button';
import { ArrowLeftRight, Kanban, List, Calendar, BarChart, Clock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import { useQuery } from '@tanstack/react-query';
import { getProject } from '@/api/projectsApi';
import { useTranslation } from 'react-i18next';

interface ViewsDropdownProps {
  projectId: string | undefined;
}

const ViewsDropdown: React.FC<ViewsDropdownProps> = ({ projectId }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useTranslation();

  // Fetch project to check permissions
  const { data: project } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => getProject(Number(projectId)),
    enabled: !!projectId
  });

  if (!projectId) return null;

  const canViewProject = hasProjectPermission(project, user, ProjectPermission.VIEW_PROJECT);

  if (!canViewProject) return null;

  const handleViewChange = (view: string) => {
    navigate(`/projects/${projectId}/${view}`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="secondary"
          size="sm"
          className="flex items-center"
        >
          <ArrowLeftRight size={16} className="mr-1.5" />
          {t('project.views')}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t('project.selectView')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => handleViewChange('board')}>
          <Kanban size={16} className="mr-2" />
          {t('project.kanban')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleViewChange('list')}>
          <List size={16} className="mr-2" />
          {t('project.list')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleViewChange('calendar')}>
          <Calendar size={16} className="mr-2" />
          {t('project.calendar')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleViewChange('timeline')}>
          <Clock size={16} className="mr-2" />
          {t('project.timeline')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleViewChange('reports')}>
          <BarChart size={16} className="mr-2" />
          {t('project.reports')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ViewsDropdown;
