
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import Navbar from '../components/Navbar';
import ProfilePhoto from '@/components/profile/ProfilePhoto';
import ProfileForm from '@/components/profile/ProfileForm';
import api from '@/api/api';
import { useToast } from '@/components/ui/use-toast';
import { Loader } from 'lucide-react';
import { User } from '@/entities/User';

const ProfilePage: React.FC = () => {
  const { user, updateProfile, setUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState<User | null>(user);
  const { toast } = useToast();

  // Fetch user data if not available
  useEffect(() => {
    if (!user) {
      setIsLoading(true);
      api.get('/user')
        .then(response => {
          const fetchedUser = response.data;
          if (fetchedUser && fetchedUser.id) {
            setUser(fetchedUser);
            setUserData(fetchedUser);
            // Also update localStorage to ensure consistency
            localStorage.setItem('user', JSON.stringify(fetchedUser));
          } else {
            throw new Error('Invalid user data received from server');
          }
        })
        .catch(error => {
          console.error('Failed to fetch user data:', error);

          // Check if this is an authentication error
          if (error.response && error.response.status === 401) {
            toast({
              variant: "destructive",
              title: "Authentication Error",
              description: "Your session has expired. Please log in again.",
            });
            // Redirect to login after a short delay
            setTimeout(() => {
              window.location.href = '/login';
            }, 2000);
          } else {
            toast({
              variant: "destructive",
              title: "Error",
              description: "Failed to load profile data. Please try again.",
            });
          }
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      setUserData(user);
    }
  }, [user, setUser, toast]);

  // Update local state when user data changes
  useEffect(() => {
    if (user) {
      setUserData(user);
    }
  }, [user]);

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-foreground">Profile</h1>
          <p className="text-muted-foreground mt-1">
            Manage your personal information
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Loading profile data...</span>
          </div>
        ) : !userData ? (
          <div className="flex flex-col justify-center items-center h-64">
            <p className="text-muted-foreground">Could not load profile data.</p>
            <button
              className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
              onClick={() => window.location.reload()}
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Profile Photo Card */}
            <ProfilePhoto
              firstName={userData.first_name}
              lastName={userData.last_name}
              profilePicture={userData.profile_picture}
            />

            {/* Profile Information Card */}
            <ProfileForm
              userData={{
                first_name: userData.first_name || '',
                last_name: userData.last_name || '',
                email: userData.email || '',
                phone: userData.phone || '',
                job_title: userData.job_title || '',
                bio: userData.bio || '',
                birth_date: userData.birth_date
              }}
              onSubmit={async (data) => {
                try {
                  const updatedUser = await updateProfile(data);
                  setUserData(updatedUser);
                  toast({
                    title: "Profile updated",
                    description: "Your profile has been updated successfully.",
                  });
                } catch (error) {
                  console.error('Failed to update profile:', error);
                  toast({
                    variant: "destructive",
                    title: "Update failed",
                    description: "Could not update your profile. Please try again.",
                  });
                }
              }}
            />
          </div>
        )}
      </main>
    </div>
  );
};

export default ProfilePage;
