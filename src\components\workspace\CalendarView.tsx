import React, { useState } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Project } from '@/entities/Project';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { DateHelper } from '@/utils/dateUtils';

interface CalendarViewProps {
  isOpen: boolean;
  onClose: () => void;
  projects: Project[];
}

const CalendarView: React.FC<CalendarViewProps> = ({
  isOpen,
  onClose,
  projects
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  const daysInMonth = getDaysInMonth(currentYear, currentMonth);
  const firstDayOfMonth = getFirstDayOfMonth(currentYear, currentMonth);

  // Adjust for Sunday as first day (0) to Monday as first day (1)
  const adjustedFirstDay = firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1;

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  const prevMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
  };

  const nextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
  };

  // Get projects with deadlines in the current month
  const projectsInMonth = projects.filter(project => {
    const endDate = new Date(project.end_date);
    return endDate.getMonth() === currentMonth && endDate.getFullYear() === currentYear;
  });

  // Create a map of day -> projects for quick lookup
  const projectsByDay: Record<number, Project[]> = {};
  projectsInMonth.forEach(project => {
    const day = new Date(project.end_date).getDate();
    if (!projectsByDay[day]) {
      projectsByDay[day] = [];
    }
    projectsByDay[day].push(project);
  });

  // Generate calendar days
  const calendarDays = [];

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < adjustedFirstDay; i++) {
    calendarDays.push(<div key={`empty-${i}`} className="h-24 border border-border bg-muted/20"></div>);
  }

  // Add cells for each day of the month
  for (let day = 1; day <= daysInMonth; day++) {
    const hasProjects = projectsByDay[day] && projectsByDay[day].length > 0;

    calendarDays.push(
      <div
        key={`day-${day}`}
        className={`h-24 border border-border p-1 relative ${hasProjects ? 'bg-primary/5' : ''}`}
      >
        <div className="text-sm font-medium mb-1">{day}</div>
        {hasProjects && (
          <div className="overflow-y-auto max-h-[calc(100%-20px)]">
            {projectsByDay[day].map((project, index) => (
              <div
                key={`project-${day}-${index}`}
                className="text-xs p-1 mb-1 rounded bg-primary/10 truncate"
                title={project.name}
              >
                {project.name}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Project Calendar</DialogTitle>
        </DialogHeader>

        <div className="flex justify-between items-center mb-4">
          <Button variant="outline" size="sm" onClick={prevMonth}>
            <ChevronLeft size={16} />
          </Button>
          <h2 className="text-lg font-medium">
            {monthNames[currentMonth]} {currentYear}
          </h2>
          <Button variant="outline" size="sm" onClick={nextMonth}>
            <ChevronRight size={16} />
          </Button>
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Day names */}
          {dayNames.map(day => (
            <div key={day} className="text-center font-medium text-sm py-2 bg-muted/30">
              {day}
            </div>
          ))}

          {/* Calendar days */}
          {calendarDays}
        </div>

        {/* Projects with deadlines this month */}
        {projectsInMonth.length > 0 && (
          <div className="mt-4">
            <h3 className="text-md font-medium mb-2">Deadlines This Month</h3>
            <div className="space-y-2">
              {projectsInMonth.map(project => (
                <div
                  key={project.id}
                  className="p-2 border border-border rounded-lg flex justify-between items-center"
                >
                  <div>
                    <h4 className="font-medium">{project.name}</h4>
                    <p className="text-xs text-muted-foreground">{project.description}</p>
                  </div>
                  <div className="text-sm font-medium text-primary">
                    {DateHelper.formatDateTime(project.end_date)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CalendarView;
