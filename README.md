
# Project Management Suite

A comprehensive project management platform that helps teams collaborate, plan, and deliver their best work.

## Overview

This project management suite provides tools for teams to manage projects efficiently with features such as:

- Kanban board for task management
- Timeline view for project scheduling
- Team collaboration tools
- Project progress tracking
- Dashboard with analytics
- User profiles and authentication

## Tech Stack

### Frontend
- React with TypeScript
- Vite for fast development and building
- Tailwind CSS for styling
- shadcn/ui component library
- React Router for navigation
- TanStack React Query for data fetching
- Recharts for data visualization

### Backend
- Laravel PHP framework
- MySQL/PostgreSQL database
- Laravel Sanctum for authentication
- RESTful API architecture

## Project Structure

### Key Frontend Directories and Files
- `src/components/` - Reusable UI components
- `src/pages/` - Page components for different routes
- `src/context/` - React context providers for state management
- `src/api/` - API service modules for backend communication
- `src/hooks/` - Custom React hooks
- `src/lib/` - Utility functions and shared code

### Key Backend Directories and Files
- `backend/app/Http/Controllers/` - API controllers
- `backend/app/Models/` - Eloquent data models
- `backend/database/migrations/` - Database schema definitions
- `backend/routes/api.php` - API route definitions

## Getting Started

### Prerequisites
- Node.js (v18+) and npm/yarn
- PHP 8.1+
- Composer
- MySQL/PostgreSQL database

### Frontend Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd project-management-suite
```

2. Install frontend dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend will be available at http://localhost:8080

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Install PHP dependencies:
```bash
composer install
```

3. Copy the environment file and configure your database:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Run database migrations:
```bash
php artisan migrate
```

6. Start the development server:
```bash
php artisan serve
```

The backend API will be available at http://localhost:8000

## Key Features

### Project Management
- Create and manage projects
- Track project progress
- Assign team members
- Set priorities and deadlines

### Task Management
- Kanban board for visualizing workflow
- Create, edit, and delete tasks
- Move tasks between states (Not Started, In Progress, Completed, At Risk)
- Attach files and add comments to tasks

### Team Collaboration
- Team member profiles
- Task assignments
- Activity tracking
- Chat functionality

### Reporting and Analytics
- Project status overview
- Progress tracking
- Time tracking
- Performance metrics

## Development Guidelines

- Follow the established code structure when adding new components
- Use TypeScript types for all components and functions
- Follow the existing patterns for API calls using React Query
- Implement responsive designs for all UI components
- Add appropriate error handling for all API interactions

## License

This project is licensed under the MIT License - see the LICENSE file for details.
