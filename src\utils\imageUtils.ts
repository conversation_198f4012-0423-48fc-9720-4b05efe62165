/**
 * Utility functions for handling image URLs and optimization
 */
import { API_BASE_URL, FEATURES } from '@/config';

// Extract the base URL without the /api part
const getBackendBaseUrl = () => {
  // Remove '/api' from the end if it exists
  const apiBaseUrl = API_BASE_URL;
  return apiBaseUrl.endsWith('/api')
    ? apiBaseUrl.substring(0, apiBaseUrl.length - 4)
    : apiBaseUrl;
};

// Image size constants
export enum ImageSize {
  THUMBNAIL = 'thumbnail', // 150x150
  SMALL = 'small',         // 300x300
  MEDIUM = 'medium',       // 600x600
  LARGE = 'large',         // 1200x1200
  ORIGINAL = 'original'    // Original size
}

/**
 * Converts a relative storage path to a full URL
 * @param path The relative path (e.g., /storage/profile_pictures/image.jpg)
 * @returns The full URL including the backend base URL
 */
export const getFullImageUrl = (path: string | undefined): string | undefined => {
  if (!path) return undefined;

  // If the path already includes http:// or https://, it's already a full URL
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }

  // If using mock data, handle paths differently
  if (FEATURES.USE_MOCK_DATA) {
    // If the path starts with /storage, convert to local assets path
    if (path.startsWith('/storage/profile_pictures/')) {
      // Extract the filename from the path
      const filename = path.split('/').pop();
      return `/assets/profile-pictures/${filename}`;
    }

    // If the path starts with /assets, it's already a local path
    if (path.startsWith('/assets')) {
      return path;
    }
  } else {
    // If the path starts with /storage, prepend the backend URL
    if (path.startsWith('/storage')) {
      return `${getBackendBaseUrl()}${path}`;
    }
  }

  // Otherwise, return the path as is
  return path;
};

/**
 * Gets an optimized image URL with the specified size
 * @param path The image path
 * @param size The desired image size
 * @returns Optimized image URL
 */
export const getOptimizedImageUrl = (
  path: string | undefined,
  size: ImageSize = ImageSize.MEDIUM
): string | undefined => {
  const fullUrl = getFullImageUrl(path);
  if (!fullUrl) return undefined;

  // If it's an external URL (not from our backend), return as is
  if (!fullUrl.includes(getBackendBaseUrl())) {
    return fullUrl;
  }

  // Add size parameter to URL
  const separator = fullUrl.includes('?') ? '&' : '?';
  return `${fullUrl}${separator}size=${size}`;
};

/**
 * Preloads an image to improve perceived performance
 * @param src Image source URL
 */
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

/**
 * Compresses an image file before upload
 * @param file The image file to compress
 * @param maxSizeKB Maximum size in KB
 * @param quality Compression quality (0-1)
 * @returns Compressed file as Blob
 */
export const compressImage = async (
  file: File,
  maxSizeKB: number = 1024,
  quality: number = 0.7
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;

        // Calculate new dimensions while maintaining aspect ratio
        if (width > height) {
          if (width > 1200) {
            height = Math.round((height * 1200) / width);
            width = 1200;
          }
        } else {
          if (height > 1200) {
            width = Math.round((width * 1200) / height);
            height = 1200;
          }
        }

        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0, width, height);

        // Convert to blob with quality setting
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Canvas to Blob conversion failed'));
              return;
            }

            // Check if we need further compression
            if (blob.size > maxSizeKB * 1024 && quality > 0.3) {
              // Try again with lower quality
              compressImage(file, maxSizeKB, quality - 0.1)
                .then(resolve)
                .catch(reject);
            } else {
              resolve(blob);
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('Error loading image for compression'));
      };
    };

    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
  });
};
