[{"id": 1, "project_id": 1, "title": "To Do", "description": "Tasks that need to be started", "position": 1, "created_at": "2023-04-15T09:00:00Z", "updated_at": "2023-04-15T09:00:00Z"}, {"id": 2, "project_id": 1, "title": "In Progress", "description": "Tasks currently being worked on", "position": 2, "created_at": "2023-04-15T09:05:00Z", "updated_at": "2023-04-15T09:05:00Z"}, {"id": 3, "project_id": 1, "title": "Completed", "description": "Tasks that have been finished", "position": 3, "created_at": "2023-04-15T09:10:00Z", "updated_at": "2023-04-15T09:10:00Z"}, {"id": 4, "project_id": 2, "title": "Backlog", "description": "Tasks planned for future sprints", "position": 1, "created_at": "2023-05-01T10:00:00Z", "updated_at": "2023-05-01T10:00:00Z"}, {"id": 5, "project_id": 2, "title": "To Do", "description": "Tasks for the current sprint", "position": 2, "created_at": "2023-05-01T10:05:00Z", "updated_at": "2023-05-01T10:05:00Z"}, {"id": 6, "project_id": 2, "title": "In Progress", "description": "Tasks being actively worked on", "position": 3, "created_at": "2023-05-01T10:10:00Z", "updated_at": "2023-05-01T10:10:00Z"}, {"id": 7, "project_id": 2, "title": "Review", "description": "Tasks awaiting review or testing", "position": 4, "created_at": "2023-05-01T10:15:00Z", "updated_at": "2023-05-01T10:15:00Z"}, {"id": 8, "project_id": 2, "title": "Done", "description": "Completed tasks", "position": 5, "created_at": "2023-05-01T10:20:00Z", "updated_at": "2023-05-01T10:20:00Z"}, {"id": 9, "project_id": 3, "title": "Planning", "description": "Campaign planning tasks", "position": 1, "created_at": "2023-06-10T11:00:00Z", "updated_at": "2023-06-10T11:00:00Z"}, {"id": 10, "project_id": 3, "title": "Content Creation", "description": "Creating marketing content", "position": 2, "created_at": "2023-06-10T11:05:00Z", "updated_at": "2023-06-10T11:05:00Z"}, {"id": 11, "project_id": 3, "title": "Review", "description": "Content review and approval", "position": 3, "created_at": "2023-06-10T11:10:00Z", "updated_at": "2023-06-10T11:10:00Z"}, {"id": 12, "project_id": 3, "title": "Scheduled", "description": "Content scheduled for publication", "position": 4, "created_at": "2023-06-10T11:15:00Z", "updated_at": "2023-06-10T11:15:00Z"}, {"id": 13, "project_id": 3, "title": "Published", "description": "Content that has been published", "position": 5, "created_at": "2023-06-10T11:20:00Z", "updated_at": "2023-06-10T11:20:00Z"}, {"id": 14, "project_id": 4, "title": "Planning", "description": "Database migration planning", "position": 1, "created_at": "2023-07-01T14:00:00Z", "updated_at": "2023-07-01T14:00:00Z"}, {"id": 15, "project_id": 4, "title": "Development", "description": "Migration script development", "position": 2, "created_at": "2023-07-01T14:05:00Z", "updated_at": "2023-07-01T14:05:00Z"}, {"id": 16, "project_id": 4, "title": "Testing", "description": "Testing migration scripts", "position": 3, "created_at": "2023-07-01T14:10:00Z", "updated_at": "2023-07-01T14:10:00Z"}, {"id": 17, "project_id": 4, "title": "Deployment", "description": "Deploying migration to production", "position": 4, "created_at": "2023-07-01T14:15:00Z", "updated_at": "2023-07-01T14:15:00Z"}, {"id": 18, "project_id": 5, "title": "Requirements", "description": "Gathering requirements", "position": 1, "created_at": "2023-08-01T10:30:00Z", "updated_at": "2023-08-01T10:30:00Z"}, {"id": 19, "project_id": 5, "title": "Design", "description": "UI/UX design tasks", "position": 2, "created_at": "2023-08-01T10:35:00Z", "updated_at": "2023-08-01T10:35:00Z"}, {"id": 20, "project_id": 5, "title": "Development", "description": "Implementation tasks", "position": 3, "created_at": "2023-08-01T10:40:00Z", "updated_at": "2023-08-01T10:40:00Z"}, {"id": 21, "project_id": 5, "title": "Testing", "description": "QA and testing tasks", "position": 4, "created_at": "2023-08-01T10:45:00Z", "updated_at": "2023-08-01T10:45:00Z"}, {"id": 22, "project_id": 5, "title": "Deployment", "description": "Launch preparation tasks", "position": 5, "created_at": "2023-08-01T10:50:00Z", "updated_at": "2023-08-01T10:50:00Z"}, {"id": 23, "project_id": 6, "title": "Planning", "description": "CMS planning tasks", "position": 1, "created_at": "2023-07-15T10:00:00Z", "updated_at": "2023-07-15T10:00:00Z"}, {"id": 24, "project_id": 6, "title": "Design", "description": "UI/UX design for CMS", "position": 2, "created_at": "2023-07-15T10:05:00Z", "updated_at": "2023-07-15T10:05:00Z"}, {"id": 25, "project_id": 6, "title": "Development", "description": "CMS development tasks", "position": 3, "created_at": "2023-07-15T10:10:00Z", "updated_at": "2023-07-15T10:10:00Z"}, {"id": 26, "project_id": 6, "title": "Testing", "description": "CMS testing tasks", "position": 4, "created_at": "2023-07-15T10:15:00Z", "updated_at": "2023-07-15T10:15:00Z"}, {"id": 27, "project_id": 7, "title": "Research", "description": "AI research tasks", "position": 1, "created_at": "2023-06-01T12:00:00Z", "updated_at": "2023-06-01T12:00:00Z"}, {"id": 28, "project_id": 7, "title": "Data Collection", "description": "Gathering and preparing data", "position": 2, "created_at": "2023-06-01T12:05:00Z", "updated_at": "2023-06-01T12:05:00Z"}, {"id": 29, "project_id": 7, "title": "Model Development", "description": "Developing AI models", "position": 3, "created_at": "2023-06-01T12:10:00Z", "updated_at": "2023-06-01T12:10:00Z"}, {"id": 30, "project_id": 7, "title": "Evaluation", "description": "Testing and evaluating models", "position": 4, "created_at": "2023-06-01T12:15:00Z", "updated_at": "2023-06-01T12:15:00Z"}, {"id": 31, "project_id": 8, "title": "Concept", "description": "Game concept and planning", "position": 1, "created_at": "2023-05-15T14:00:00Z", "updated_at": "2023-05-15T14:00:00Z"}, {"id": 32, "project_id": 8, "title": "Design", "description": "Game design and art", "position": 2, "created_at": "2023-05-15T14:05:00Z", "updated_at": "2023-05-15T14:05:00Z"}, {"id": 33, "project_id": 8, "title": "Development", "description": "Game development tasks", "position": 3, "created_at": "2023-05-15T14:10:00Z", "updated_at": "2023-05-15T14:10:00Z"}, {"id": 34, "project_id": 8, "title": "Testing", "description": "Game testing and QA", "position": 4, "created_at": "2023-05-15T14:15:00Z", "updated_at": "2023-05-15T14:15:00Z"}, {"id": 35, "project_id": 8, "title": "Release", "description": "Game release preparation", "position": 5, "created_at": "2023-05-15T14:20:00Z", "updated_at": "2023-05-15T14:20:00Z"}]