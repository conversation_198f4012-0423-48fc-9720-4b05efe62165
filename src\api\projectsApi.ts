import { normalizeProjects, Project } from '@/entities/Project';
import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';
import { queryClient } from '@/lib/react-query';
import { toast } from "@/hooks/use-toast";



// Get all projects with optional workspace filter
export const getProjects = async (workspaceId?: number) => {
  try {
    // If workspaceId is undefined, use the default workspace or get all projects
    if (!workspaceId) {
      console.log('No workspace ID provided, fetching all projects');
      return getAllProjects();
    }

    console.log(`Fetching projects for workspace ID: ${workspaceId}`);
    const response = await api.get(`/workspace/projects/${workspaceId}`);
    return normalizeProjects(response.data);
  } catch (error) {
    console.error('Error fetching projects:', error);
    return handleApiError(
      error,
      "Failed to fetch projects",
      "Could not load project data"
    );
  }
};

// Get all projects for the user, regardless of workspace
export const getAllProjects = async () => {
  try {
    const response = await api.get('/projects/all');
    return normalizeProjects(response.data);
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch all projects",
      "Could not load all project data"
    );
  }
};

// Get a specific project
export const getProject = async (id: string | number) => {
  try {
    console.log(`Fetching project with ID: ${id}`);
    const response = await api.get(`/projects/${id}`);
    console.log(`Project data received:`, response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching project ${id}:`, error);

    // Check if it's a 404 error
    if (error.response && error.response.status === 404) {
      console.error('Project not found or no access rights');
      // Throw a specific error that can be handled by the component
      throw new Error('Project not found or you do not have permission to view it');
    }

    return handleApiError(
      error,
      "Failed to fetch project",
      "Could not load project details"
    );
  }
};



// Create a new project
export const createProject = async (projectData: Partial<Project>) => {
  try {
    console.log(`Creating project in workspace ID: ${projectData.workspace_id}`);
    const response = await api.post('/projects', projectData);
    console.log('Project created successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error("Error creating project:", error);

    // Log more detailed error information
    if (error.response) {
      console.error(`Error status: ${error.response.status}`);
      console.error('Error data:', error.response.data);

      // Add error_code to the error object for more specific handling in the UI
      if (error.response.data && error.response.data.error_code) {
        error.error_code = error.response.data.error_code;
        console.error(`Error code: ${error.error_code}`);

        // If the server provided available workspaces, add them to the error
        if (error.response.data.available_workspaces) {
          error.available_workspaces = error.response.data.available_workspaces;
          console.log('Available workspaces:', error.available_workspaces);
        }
      }

      if (error.response.status === 404) {
        console.error(`Resource not found: ${error.response.config.url}`, 'Response:', error.response.data);
      }
    }

    throw error; // Re-throw the error to be handled by the component
  }
};

// Update a project
export const updateProject = async (id: number, projectData: Partial<Project>) => {
  try {
    const response = await api.put(`/projects/${id}`, projectData);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      `Failed to update project`,
      "Could not update the project details"
    );
  }
};



// Delete a project
export const deleteProject = async (id: number) => {
  try {
    const response = await api.delete(`/projects/${id}`);
    return response.data;
  } catch (error: any) {
    // Check if this is a permission error with additional info
    if (error.response?.data?.action === 'leave') {
      throw new Error('You do not have permission to delete this project. You may leave the project instead.');
    }

    return handleApiError(
      error,
      `Failed to delete project`,
      "Could not delete the project"
    );
  }
};

// Add a user to a project
export const addProjectUser = async (projectId: number, userData: { user_id: number, role_id: number }) => {
  try {
    const response = await api.post(`/projects/${projectId}/members`, userData);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to add user",
      "Could not add user to the project"
    );
  }
};

// Remove a user from a project
export const removeProjectUser = async (projectId: number, userId: number) => {
  try {
    const response = await api.delete(`/projects/${projectId}/members`, {
      data: { user_id: userId }
    });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to remove user",
      "Could not remove user from the project"
    );
  }
};

// Leave a project (current user removes themselves)
export const leaveProject = async (projectId: number) => {
  try {
    // Get the current user's ID from the auth token
    const token = localStorage.getItem('token');

    if (!token) {
      throw new Error("User not authenticated");
    }

    // The backend will use the authenticated user from the token
    const response = await api.delete(`/projects/${projectId}/leave`);

    // Invalidate any cached data related to projects and workspaces
    // This ensures that when the user navigates to the dashboard or workspace,
    // the data will be refreshed from the server
    queryClient.invalidateQueries({ queryKey: ['projects'] });
    queryClient.invalidateQueries({ queryKey: ['workspaces'] });
    queryClient.invalidateQueries({ queryKey: ['project', projectId] });

    return response.data;
  } catch (error: any) {
    // Check if this is the "project owner cannot leave" error
    if (error.response && error.response.status === 400 &&
        error.response.data && error.response.data.message) {

      // Display the exact error message from the backend
      toast({
        title: "Error",
        description: error.response.data.message,
        variant: "destructive"
      });

      throw error;
    }

    return handleApiError(
      error,
      "Failed to leave project",
      "Could not leave the project"
    );
  }
};

// Star a project
export const starProject = async (projectId: number) => {
  try {
    const response = await api.post(`/projects/${projectId}/star`);

    // Immediately invalidate relevant queries
    queryClient.invalidateQueries({ queryKey: ['starredProjects'] });
    queryClient.invalidateQueries({ queryKey: ['projects'] });
    queryClient.invalidateQueries({ queryKey: ['project', projectId] });

    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to star project",
      "Could not star the project"
    );
  }
};

// Unstar a project
export const unstarProject = async (projectId: number) => {
  try {
    const response = await api.delete(`/projects/${projectId}/star`);

    // Immediately invalidate and refetch relevant queries
    await queryClient.invalidateQueries({ queryKey: ['starredProjects'] });
    await queryClient.refetchQueries({ queryKey: ['starredProjects'] });

    // Also invalidate and refetch the projects query to ensure consistency
    await queryClient.invalidateQueries({ queryKey: ['projects'] });
    await queryClient.refetchQueries({ queryKey: ['projects'] });

    // Invalidate the specific project query
    queryClient.invalidateQueries({ queryKey: ['project', projectId] });

    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to unstar project",
      "Could not unstar the project"
    );
  }
};

// Get all starred projects
export const getStarredProjects = async () => {
  try {
    const response = await api.get('/starred-projects');
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch starred projects",
      "Could not load starred projects"
    );
  }
};

// Get all project templates
export const getProjectTemplates = async () => {
  try {
    const response = await api.get('/project-templates');
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch templates",
      "Could not load project templates"
    );
  }
};

// Get a specific project template
export const getProjectTemplate = async (id: number) => {
  try {
    const response = await api.get(`/project-templates/${id}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch template",
      "Could not load project template details"
    );
  }
};

// Convert a personal project to a group project
export const convertToGroupProject = async (projectId: number) => {
  try {
    const response = await api.post(`/projects/${projectId}/convert-to-group`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to convert project",
      "Could not convert the project to a group project"
    );
  }
};

// Convert a group project to a personal project
export const convertToPersonalProject = async (projectId: number) => {
  try {
    const response = await api.post(`/projects/${projectId}/convert-to-personal`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to convert project",
      "Could not convert the project to a personal project"
    );
  }
};

// Get project type information
export const getProjectTypeInfo = async (projectId: number) => {
  try {
    const response = await api.get(`/projects/${projectId}/type`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to get project type information",
      "Could not retrieve project type information"
    );
  }
};

// Get all available roles
export const getRoles = async () => {
  try {
    const response = await api.get('/roles');
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to get roles",
      "Could not retrieve role information"
    );
  }
};

// Get the roles of all users in a project
export const getProjectRoles = async (projectId: number) => {
  try {
    console.log(`Fetching roles for project ${projectId}`);
    const response = await api.get(`/projects/${projectId}/roles`);

    console.log('Project roles response:', response.data);

    // Ensure the response data is an array
    if (Array.isArray(response.data)) {
      // Ensure each member has a user_id property
      const members = response.data.map(member => {
        if (!member.user_id && member.id) {
          console.log('Converting id to user_id for member:', member);
          return { ...member, user_id: member.id };
        }
        return member;
      });

      console.log('Processed members:', members);
      return members;
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching project roles:', error);

    if (error.response) {
      console.error(`Error status: ${error.response.status}`);
      console.error('Error data:', error.response.data);
    }

    throw error; // Throw the error to be handled by the component
  }
};

// Update a user's role in a project (original method - may have backend issues)
export const updateUserRole = async (projectId: number, userId: number, roleId: number) => {
  try {
    // Log the SQL error from the server for debugging
    console.log(`Updating role for user ${userId} in project ${projectId} to role ${roleId}`);
    console.log('Note: The server might be expecting "id" instead of "user_id"');

    // Try sending both id and user_id to handle different backend expectations
    const response = await api.put(`/projects/${projectId}/roles`, {
      id: userId,         // Try using 'id' as the parameter name
      user_id: userId,    // Also include 'user_id' for backward compatibility
      role_id: roleId
    });

    // Log the response for debugging
    console.log('Update role response:', response.data);

    return response.data;
  } catch (error) {
    // Log detailed error information
    console.error("Error updating user role:", error);

    if (error.response) {
      console.error(`Error status: ${error.response.status}`);
      console.error('Error data:', error.response.data);
    }

    throw error; // Throw the error to be handled by the component
  }
};

// Get the current user's permissions in a project
export const getUserPermissions = async (projectId: number) => {
  try {
    const response = await api.get(`/projects/${projectId}/permissions`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to get user permissions",
      "Could not retrieve your permissions for this project"
    );
  }
};

// Add a user to a project
export const addUserToProject = async (projectId: number, userId: number, roleId: number) => {
  try {
    const response = await api.post(`/projects/${projectId}/members`, { user_id: userId, role_id: roleId });
    return response.data;
  } catch (error) {
    // Check if the error is a 409 Conflict (user already exists in project)
    if (error.response && error.response.status === 409) {
      // If it's a conflict, we can consider this a success since the user is already in the project
      console.log("User already exists in project, considering this a success");
      return { message: "User already exists in project" };
    }

    // For other errors, handle normally
    return handleApiError(
      error,
      "Failed to add user to project",
      "Could not add the user to the project"
    );
  }
};

// Remove a user from a project
export const removeUserFromProject = async (projectId: number, userId: number) => {
  try {
    console.log(`Removing user ${userId} from project ${projectId}`);

    // The backend expects the user_id parameter in the request body, not in the URL
    const response = await api.delete(`/projects/${projectId}/members`, {
      data: { user_id: userId }
    });

    console.log('Remove user response:', response.data);

    // Invalidate any cached data related to projects and workspaces
    // This ensures that when the user navigates to the dashboard or workspace,
    // the data will be refreshed from the server
    queryClient.invalidateQueries({ queryKey: ['projects'] });
    queryClient.invalidateQueries({ queryKey: ['workspaces'] });
    queryClient.invalidateQueries({ queryKey: ['project', projectId] });

    return response.data;
  } catch (error) {
    // Log detailed error information
    console.error("Error removing user from project:", error);

    if (error.response) {
      console.error(`Error status: ${error.response.status}`);
      console.error('Error data:', error.response.data);
    }

    throw error; // Throw the error to be handled by the component
  }
};

// Send a project invitation
export const sendProjectInvitation = async (projectId: number, email: string, roleId: number) => {
  try {
    console.log(`Sending project invitation with projectId: ${projectId}, email: ${email}, roleId: ${roleId} (type: ${typeof roleId})`);

    // Validate roleId is a number and one of the valid role IDs
    if (typeof roleId !== 'number' || isNaN(roleId) || ![1, 2, 3].includes(roleId)) {
      console.error(`Invalid role ID: ${roleId}`);
      throw new Error('The selected role id is invalid.');
    }

    // Ensure roleId is a number and convert it to a string for the API
    // Some backends might expect a string for role_id
    const roleIdStr = String(roleId);
    console.log(`Converted roleId to string: ${roleIdStr}`);

    // Try sending both formats to handle different backend expectations
    const response = await api.post(`/projects/${projectId}/invitations`, {
      email,
      role_id: roleId,
      role_id_str: roleIdStr
    });

    console.log('Project invitation response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error sending project invitation:', error);

    // Check for specific error messages
    if (error.response?.data?.message) {
      const errorMessage = error.response.data.message;
      console.log('Error message from server:', errorMessage);

      // Handle specific error cases
      if (errorMessage.includes('cannot invite yourself') ||
          errorMessage.includes('You cannot invite yourself')) {
        return handleApiError(
          error,
          "Cannot invite yourself",
          "You cannot invite yourself to a project",
          false // Don't throw the error, just show the toast
        );
      } else if (errorMessage.includes('already a member')) {
        return handleApiError(
          error,
          "User already in project",
          "This user is already a member of the project",
          false // Don't throw the error, just show the toast
        );
      } else if (errorMessage.includes('role id is invalid')) {
        return handleApiError(
          error,
          "Invalid role",
          "The selected role id is invalid.",
          false // Don't throw the error, just show the toast
        );
      }
    }

    // Default error handling
    return handleApiError(
      error,
      "Failed to send invitation",
      error.response?.data?.message || "Could not send the project invitation",
      false // Don't throw the error, just show the toast
    );
  }
};

// Get all pending invitations for a project
export const getProjectInvitations = async (projectId: number) => {
  try {
    const response = await api.get(`/projects/${projectId}/invitations`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch invitations",
      "Could not load project invitations"
    );
  }
};

// Cancel a project invitation
export const cancelProjectInvitation = async (projectId: number, invitationId: number) => {
  try {
    const response = await api.delete(`/projects/${projectId}/invitations/${invitationId}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to cancel invitation",
      "Could not cancel the project invitation"
    );
  }
};

// Transfer a project to a different workspace
export const transferProjectToWorkspace = async (projectId: number, workspaceId: number) => {
  try {
    const response = await api.post(`/projects/${projectId}/transfer-to-workspace`, { workspace_id: workspaceId });

    // Invalidate queries to ensure data is refreshed
    queryClient.invalidateQueries({ queryKey: ['projects'] });
    queryClient.invalidateQueries({ queryKey: ['workspaces'] });
    queryClient.invalidateQueries({ queryKey: ['project', projectId] });

    return response.data;
  } catch (error: any) {
    // Instead of using handleApiError which shows a toast AND throws an error,
    // we'll just throw the error and let the component handle it
    console.error("Error transferring project:", error);
    throw error;
  }
};

// Copy a project to a different workspace
export const copyProjectToWorkspace = async (projectId: number, workspaceId: number) => {
  try {
    const response = await api.post(`/projects/${projectId}/copy-to-workspace`, { workspace_id: workspaceId });

    // Invalidate queries to ensure data is refreshed
    queryClient.invalidateQueries({ queryKey: ['projects'] });
    queryClient.invalidateQueries({ queryKey: ['workspaces'] });

    return response.data;
  } catch (error: any) {
    // Instead of using handleApiError which shows a toast AND throws an error,
    // we'll just throw the error and let the component handle it
    console.error("Error copying project:", error);
    throw error;
  }
};

// Transfer project ownership to another user
export const transferProjectOwnership = async (projectId: number, newOwnerId: number) => {
  try {
    const response = await api.post(`/projects/${projectId}/transfer-ownership`, { new_owner_id: newOwnerId });

    // Invalidate queries to ensure data is refreshed
    queryClient.invalidateQueries({ queryKey: ['projects'] });
    queryClient.invalidateQueries({ queryKey: ['project', projectId] });
    queryClient.invalidateQueries({ queryKey: ['workspaces'] });

    return response.data;
  } catch (error: any) {
    // Log detailed error information
    console.error("Error transferring project ownership:", error);

    if (error.response) {
      console.error(`Error status: ${error.response.status}`);
      console.error('Error data:', error.response.data);

      // If the error is that the user is not a member of the project
      if (error.response.status === 400 &&
          error.response.data.message &&
          error.response.data.message.includes('must be a member of the project')) {
        throw new Error('The selected user must be a member of the project. Please add them to the project first.');
      }

      // If the error is that only the owner can transfer ownership
      if (error.response.status === 403 &&
          error.response.data.message &&
          error.response.data.message.includes('Only the project owner can transfer ownership')) {
        throw new Error('Only the project owner can transfer ownership of this project.');
      }
    }

    // For other errors, throw a generic error
    throw error;
  }
};
