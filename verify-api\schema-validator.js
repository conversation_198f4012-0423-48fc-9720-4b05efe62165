
const { generateSchema } = require('typescript-json-schema');
const Ajv = require('ajv');

// Initialize JSON schema validator
const ajv = new Ajv({ allErrors: true });

/**
 * Generate JSON schemas from TypeScript interfaces
 * @param {string[]} typeFiles Array of TypeScript files with interfaces
 * @returns {Object} Map of interface names to schemas
 */
function generateJsonSchemas(typeFiles) {
  console.log('Generating JSON schemas from TypeScript interfaces...');
  
  // Use typescript-json-schema to generate schemas
  const program = generateSchema.getProgramFromFiles(
    typeFiles,
    {
      required: true,
      noExtraProps: true,
      ignoreErrors: true
    }
  );
  
  const generator = generateSchema.buildGenerator(program, {
    required: true,
    noExtraProps: true
  });
  
  if (!generator) {
    console.log('Warning: Could not generate JSON schemas');
    return {};
  }
  
  const schemas = {};
  const typeSymbols = generator.getMainTypeSymbols(program);
  
  for (const symbol of typeSymbols) {
    const name = symbol.name;
    try {
      const schema = generator.getSchemaForSymbol(name);
      if (schema) {
        schemas[name] = schema;
      }
    } catch (error) {
      console.log(`Warning: Could not generate schema for ${name}`);
    }
  }
  
  return schemas;
}

/**
 * Validate response data against a JSON schema
 * @param {Object} data Response data
 * @param {Object} schema JSON schema
 * @returns {Object} Validation result
 */
function validateSchema(data, schema) {
  if (!schema) return { valid: false, reason: 'No schema available' };
  
  try {
    const validate = ajv.compile(schema);
    const valid = validate(data);
    
    return {
      valid,
      errors: validate.errors
    };
  } catch (error) {
    return {
      valid: false,
      reason: error.message
    };
  }
}

module.exports = {
  generateJsonSchemas,
  validateSchema
};
