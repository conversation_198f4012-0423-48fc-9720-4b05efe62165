import React, { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Users } from 'lucide-react';
import { convertToGroupProject } from '@/api/projectsApi';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';

interface ProjectSettingsProps {
  project: Project;
  user: User | null;
}

const ProjectSettings: React.FC<ProjectSettingsProps> = ({ project, user }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Only the project owner can convert it to a group project
  const canConvertToGroup = user && project.user_id === user.id && !project.is_group_project;

  const handleConvertToGroup = async () => {
    if (!canConvertToGroup) return;

    try {
      setIsLoading(true);
      await convertToGroupProject(project.id);

      toast({
        title: "Project converted",
        description: "This project is now a group project. You can add members to it.",
      });

      // Invalidate the project query to refresh the data
      queryClient.invalidateQueries({ queryKey: ['project', project.id] });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to convert the project. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Project Settings</CardTitle>
          <CardDescription>
            Manage your project settings and permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Project Type Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Project Type</h3>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <div className="font-medium">Group Project</div>
                <div className="text-sm text-muted-foreground">
                  {project.is_group_project
                    ? "This is a group project that can be shared with team members"
                    : "This is a personal project only visible to you"}
                </div>
              </div>
              {project.is_group_project ? (
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-primary" />
                  <span className="text-sm font-medium">Group Project</span>
                </div>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleConvertToGroup}
                  disabled={!canConvertToGroup || isLoading}
                >
                  <Users size={16} className="mr-2" />
                  {isLoading ? "Converting..." : "Convert to Group Project"}
                </Button>
              )}
            </div>
          </div>

          <Separator />

          {/* Project Visibility */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Project Visibility</h3>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <div className="font-medium">Public in Workspace</div>
                <div className="text-sm text-muted-foreground">
                  Make this project visible to all workspace members
                </div>
              </div>
              <Switch
                disabled={!project.is_group_project}
                checked={false}
                onCheckedChange={() => {
                  toast({
                    title: "Feature not implemented",
                    description: "This feature will be available in a future update.",
                  });
                }}
              />
            </div>
          </div>

          <Separator />

          {/* Danger Zone */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-destructive">Danger Zone</h3>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                {project && user && project.user_id === user.id && hasProjectPermission(project, user, ProjectPermission.DELETE_PROJECT) ? (
                  <>
                    <div className="font-medium">Delete Project</div>
                    <div className="text-sm text-muted-foreground">
                      This action cannot be undone. All data will be permanently deleted.
                    </div>
                  </>
                ) : (
                  <>
                    <div className="font-medium">Leave Project</div>
                    <div className="text-sm text-muted-foreground">
                      You will be removed from this project and lose access to it.
                    </div>
                  </>
                )}
              </div>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => {
                  toast({
                    title: "Feature not implemented",
                    description: "Please use the project card menu to manage project membership.",
                  });
                }}
              >
                {project && user && project.user_id === user.id && hasProjectPermission(project, user, ProjectPermission.DELETE_PROJECT) ?
                  'Delete Project' : 'Leave Project'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectSettings;
