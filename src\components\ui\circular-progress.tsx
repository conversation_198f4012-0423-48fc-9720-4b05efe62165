import React from 'react';
import { cn } from '@/lib/utils';

interface CircularProgressProps {
  value: number;
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  strokeWidth?: number;
  color?: string;
  className?: string;
  trackColor?: string;
}

/**
 * A circular progress bar component that displays progress as a percentage
 * in a circular format, similar to the one shown in the reference image.
 */
const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  size = 'md',
  showPercentage = true,
  strokeWidth = 4,
  color,
  className,
  trackColor,
}) => {
  // Ensure value is between 0 and 100
  const percentage = Math.min(100, Math.max(0, value));

  // Calculate dimensions based on size
  const dimensions = {
    sm: { size: 44, fontSize: '0.8rem', strokeWidth: strokeWidth || 3 },
    md: { size: 64, fontSize: '1rem', strokeWidth: strokeWidth || 4 },
    lg: { size: 84, fontSize: '1.25rem', strokeWidth: strokeWidth || 5 },
  }[size];

  // Calculate SVG parameters
  const radius = (dimensions.size - dimensions.strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;
  const center = dimensions.size / 2;

  // Determine color based on percentage
  const getDefaultColor = () => {
    if (percentage >= 100) return '#10b981'; // Green for 100%
    if (percentage >= 75) return '#3b82f6';
    if (percentage >= 50) return '#6366f1';
    if (percentage >= 25) return '#f59e0b';
    return '#ef4444';
  };

  // Use the provided color or get the default color based on percentage
  const progressColor = color || getDefaultColor();

  // Default track color with dark mode support
  const defaultTrackColor = trackColor || 'rgba(0, 0, 0, 0.1)';

  // Create a unique ID for this progress circle
  const uniqueId = React.useId();
  const progressId = `progress-${uniqueId}`;

  return (
    <div
      className={cn(
        "relative inline-flex items-center justify-center",
        className
      )}
      style={{
        width: dimensions.size,
        height: dimensions.size
      }}
      id={progressId}
    >
      {/* Render SVG with explicit dimensions and positioning */}
      <svg
        width={dimensions.size}
        height={dimensions.size}
        viewBox={`0 0 ${dimensions.size} ${dimensions.size}`}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          transform: 'rotate(-90deg)',
          overflow: 'visible'
        }}
      >
        {/* Background track */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={defaultTrackColor}
          strokeWidth={dimensions.strokeWidth}
          style={{ opacity: 0.2 }}
        />

        {/* Progress arc */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={percentage >= 100 ? "#10b981" : progressColor}
          strokeWidth={dimensions.strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          style={{ transition: 'all 0.5s ease-in-out' }}
        />
      </svg>

      {/* Percentage text */}
      {showPercentage && (
        <div
          className="absolute inset-0 flex items-center justify-center font-semibold"
          style={{
            fontSize: dimensions.fontSize,
            color: percentage >= 100 ? "#10b981" : "currentColor",
            zIndex: 10,
            pointerEvents: "none"
          }}
        >
          <span className="relative z-10">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  );
};

export default CircularProgress;
