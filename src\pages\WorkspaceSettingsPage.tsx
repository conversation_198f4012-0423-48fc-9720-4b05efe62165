import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import { useWorkspace } from '@/context/WorkspaceContext';
import { useAuth } from '@/context/AuthContext';
import WorkspaceSettings from '@/components/workspace/WorkspaceSettings';
import Loader from '@/components/Loader';
import { AlertTriangle } from 'lucide-react';
import { Workspace } from '@/entities/Workspace';
import { useToast } from '@/components/ui/use-toast';

const WorkspaceSettingsPage: React.FC = () => {
  const { workspaceId } = useParams<{ workspaceId: string }>();
  const { currentWorkspace, members, isLoading, fetchWorkspaceById, error } = useWorkspace();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loadingError, setLoadingError] = useState<string | null>(null);

  useEffect(() => {
    if (!workspaceId) {
      navigate('/dashboard');
      return;
    }

    const loadWorkspace = async () => {
      try {
        await fetchWorkspaceById(workspaceId);
        setLoadingError(null);
      } catch (err) {
        setLoadingError('Failed to load workspace. Please try again.');
        console.error('Error loading workspace:', err);
      }
    };

    loadWorkspace();
  }, [workspaceId, fetchWorkspaceById, navigate]);

  // Update document title when workspace changes
  useEffect(() => {
    if (currentWorkspace) {
      document.title = `${currentWorkspace.name} Settings | Suite`;
    } else {
      document.title = 'Workspace Settings | Suite';
    }
  }, [currentWorkspace]);

  // Check if user is the workspace owner
  const isOwner = currentWorkspace && user && currentWorkspace.owner.id === user.id;

  // Handle workspace update
  const handleWorkspaceUpdated = async (updatedWorkspace: Workspace) => {
    try {
      // Refresh workspace data
      await fetchWorkspaceById(workspaceId!);

      toast({
        title: "Workspace updated",
        description: "Workspace settings have been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh workspace data.",
        variant: "destructive",
      });
    }
  };

  // Add a timeout to prevent infinite loading
  const [loadingTimeout, setLoadingTimeout] = useState(false);

  useEffect(() => {
    // If loading takes more than 10 seconds, show a timeout message
    let timeoutId: NodeJS.Timeout;

    if (isLoading && !loadingTimeout) {
      timeoutId = setTimeout(() => {
        setLoadingTimeout(true);
      }, 10000);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isLoading, loadingTimeout]);

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      {isLoading && !loadingTimeout && <Loader />}

      {(loadingError || loadingTimeout) && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center mb-6">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span>
              {loadingError || "Loading is taking longer than expected. There might be an issue with the connection."}
            </span>
            <button
              onClick={() => {
                setLoadingTimeout(false);
                fetchWorkspaceById(workspaceId!);
              }}
              className="ml-auto bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-md text-sm"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-foreground">Workspace Settings</h1>
          <p className="text-muted-foreground mt-1">
            Manage your workspace settings and members
          </p>
        </div>

        {(!isLoading || loadingTimeout) && !loadingError && currentWorkspace ? (
          <>
            {isOwner ? (
              <WorkspaceSettings
                workspace={currentWorkspace}
                members={members}
                onWorkspaceUpdated={handleWorkspaceUpdated}
              />
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
                <p>Only the workspace owner can modify workspace settings.</p>
              </div>
            )}
          </>
        ) : null}
      </main>
    </div>
  );
};

export default WorkspaceSettingsPage;
