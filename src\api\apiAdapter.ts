import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_BASE_URL, FEATURES } from '@/config';
import mockApi from './mockApi';
import { getLocalStorageData } from '@/utils/sessionLocalStorageUtil';

// Create a real API instance
const createRealApi = (): AxiosInstance => {
  const api = axios.create({
    baseURL: API_BASE_URL,
    withCredentials: true,
  });

  // Add request interceptor
  api.interceptors.request.use(
    async (config) => {
      const token = getLocalStorageData('token');

      // Check if this is an authentication-related endpoint
      const isAuthEndpoint = config.url?.includes('/login') ||
                            config.url?.includes('/request-for-account') ||
                            config.url?.includes('/broadcasting/auth');

      if (token) {
        try {
          config.headers.Authorization = `Bearer ${token.toString()}`;
        } catch (e) {
          console.error('Error setting Authorization header:', e);
          // Clear invalid token
          localStorage.removeItem('token');
          if (!isAuthEndpoint) {
            window.location.href = '/login';
            return Promise.reject(new Error('Invalid authentication token'));
          }
        }
      }

      return config;
    },
    (error) => {
      console.error('Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Add response interceptor
  api.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.response) {
        const { status } = error.response;

        if (status === 401) {
          // Handle unauthorized
          if (!window.location.pathname.includes('/login')) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/login';
          }
        }
      }

      return Promise.reject(error);
    }
  );

  return api;
};

// Create a mock API adapter
const createMockApiAdapter = () => {
  // Create a mock axios instance that intercepts requests
  const mockAdapter: AxiosInstance = axios.create({
    baseURL: API_BASE_URL,
  });

  // Override the request method to use our mock implementations
  mockAdapter.request = async <T = any>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    const url = config.url || '';
    const method = (config.method || 'get').toLowerCase();
    const data = config.data;

    console.log(`[Mock API] ${method.toUpperCase()} ${url}`, data);

    try {
      // Handle different API endpoints
      if (url.includes('/login') && method === 'post') {
        const { email, password } = data;
        return mockApi.login(email, password) as Promise<AxiosResponse<T>>;
      }

      if (url.includes('/logout') && method === 'post') {
        return mockApi.logout() as Promise<AxiosResponse<T>>;
      }

      if (url === '/user' && method === 'get') {
        return mockApi.getUser() as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/update-user\/\d+/) && method === 'put') {
        const userId = parseInt(url.split('/').pop() || '0');
        return mockApi.updateUser(userId, data) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/update-profile-picture\/\d+/) && method === 'post') {
        const userId = parseInt(url.split('/').pop() || '0');
        const file = data.get('profile_picture');
        return mockApi.uploadProfilePicture(userId, file) as Promise<AxiosResponse<T>>;
      }

      if (url === '/workspaces' && method === 'get') {
        return mockApi.getWorkspaces() as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/workspace\/\d+/) && method === 'get') {
        const workspaceId = parseInt(url.split('/').pop() || '0');
        return mockApi.getWorkspace(workspaceId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/workspace\/\d+\/projects/) && method === 'get') {
        const workspaceId = parseInt(url.split('/')[2] || '0');
        return mockApi.getProjects(workspaceId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/workspace\/projects\/\d+/) && method === 'get') {
        const workspaceId = parseInt(url.split('/').pop() || '0');
        return mockApi.getProjects(workspaceId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/projects\/\d+/) && method === 'get' && !url.includes('/users') && !url.includes('/lists') && !url.includes('/tasks') && !url.includes('/reorder-lists')) {
        // Extract the project ID from the URL
        const matches = url.match(/\/projects\/(\d+)/);
        const projectId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Getting project ${projectId}`);
        return mockApi.getProject(projectId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/projects\/\d+\/users/) && method === 'get') {
        // Extract the project ID from the URL
        const matches = url.match(/\/projects\/(\d+)\/users/);
        const projectId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Getting users for project ${projectId}`);
        return mockApi.getProjectUsers(projectId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/projects\/\d+\/tasks/) && method === 'get') {
        // Extract the project ID from the URL
        const matches = url.match(/\/projects\/(\d+)\/tasks/);
        const projectId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Getting tasks for project ${projectId}`);
        return mockApi.getProjectTasks(projectId) as Promise<AxiosResponse<T>>;
      }

      if (url === '/tasks' && method === 'get') {
        // Check if there's a project_id parameter
        if (config?.params?.project_id) {
          const projectId = parseInt(config.params.project_id);
          console.log(`[Mock API] Getting tasks for project ${projectId} via query param`);
          return mockApi.getProjectTasks(projectId) as Promise<AxiosResponse<T>>;
        }
        return mockApi.getAllTasks() as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/tasks\/\d+/) && method === 'get') {
        // Extract the task ID from the URL
        const matches = url.match(/\/tasks\/(\d+)/);
        const taskId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Getting task ${taskId}`);
        return mockApi.getTask(taskId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/projects\/\d+\/lists/) && method === 'get') {
        // Extract the project ID from the URL
        const matches = url.match(/\/projects\/(\d+)\/lists/);
        const projectId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Getting lists for project ${projectId}`);
        return mockApi.getProjectLists(projectId) as Promise<AxiosResponse<T>>;
      }

      if (url === '/lists' && method === 'post') {
        return mockApi.createList(data) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/lists\/\d+/) && method === 'put') {
        // Extract the list ID from the URL
        const matches = url.match(/\/lists\/(\d+)/);
        const listId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Updating list ${listId}`);
        return mockApi.updateList(listId, data) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/lists\/\d+/) && method === 'delete') {
        // Extract the list ID from the URL
        const matches = url.match(/\/lists\/(\d+)/);
        const listId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Deleting list ${listId}`);
        return mockApi.deleteList(listId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/projects\/\d+\/reorder-lists/) && method === 'post') {
        // Extract the project ID from the URL
        const matches = url.match(/\/projects\/(\d+)\/reorder-lists/);
        const projectId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Reordering lists for project ${projectId}`);
        return mockApi.reorderLists(projectId, data.list_ids) as Promise<AxiosResponse<T>>;
      }

      // Timeline endpoints
      if (url.match(/\/timeline\/workspace\/\d+/) && method === 'get') {
        // Extract the workspace ID from the URL
        const matches = url.match(/\/timeline\/workspace\/(\d+)/);
        const workspaceId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Getting timeline for workspace ${workspaceId}`);
        return mockApi.getWorkspaceTimeline(
          workspaceId,
          config?.params?.start_date,
          config?.params?.end_date,
          config?.params?.status
        ) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/timeline\/project\/\d+/) && method === 'get') {
        // Extract the project ID from the URL
        const matches = url.match(/\/timeline\/project\/(\d+)/);
        const projectId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Getting timeline for project ${projectId}`);
        return mockApi.getProjectTimeline(
          projectId,
          config?.params?.start_date,
          config?.params?.end_date,
          config?.params?.status,
          config?.params?.assignee_id ? parseInt(config.params.assignee_id) : undefined
        ) as Promise<AxiosResponse<T>>;
      }

      if (url === '/timeline/item' && method === 'put') {
        return mockApi.updateTimelineItem(
          data.item_id,
          data.start_date,
          data.end_date
        ) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/timeline\/project\/\d+\/milestone/) && method === 'post') {
        // Extract the project ID from the URL
        const matches = url.match(/\/timeline\/project\/(\d+)\/milestone/);
        const projectId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Creating milestone for project ${projectId}`);
        return mockApi.createMilestone(projectId, data) as Promise<AxiosResponse<T>>;
      }

      // Calendar endpoints
      if (url === '/calendar-events' && method === 'get') {
        console.log(`[Mock API] Getting calendar events with params:`, config?.params);
        return mockApi.getAllEvents(config?.params) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/calendar-events\/[^\/]+$/) && method === 'get') {
        // Extract the event ID from the URL
        const eventId = url.split('/').pop();
        console.log(`[Mock API] Getting calendar event ${eventId}`);
        return mockApi.getEvent(eventId) as Promise<AxiosResponse<T>>;
      }

      if (url === '/calendar-events' && method === 'post') {
        console.log(`[Mock API] Creating calendar event`);
        return mockApi.createEvent(data) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/calendar-events\/[^\/]+$/) && method === 'put') {
        // Extract the event ID from the URL
        const eventId = url.split('/').pop();
        console.log(`[Mock API] Updating calendar event ${eventId}`);
        return mockApi.updateEvent(eventId, data) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/calendar-events\/[^\/]+$/) && method === 'delete') {
        // Extract the event ID from the URL
        const eventId = url.split('/').pop();
        console.log(`[Mock API] Deleting calendar event ${eventId}`);
        return mockApi.deleteEvent(eventId) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/calendar-events\/[^\/]+\/reschedule$/) && method === 'put') {
        // Extract the event ID from the URL
        const eventId = url.split('/')[2];
        console.log(`[Mock API] Rescheduling calendar event ${eventId}`);
        return mockApi.rescheduleEvent(eventId, data.start_date, data.end_date) as Promise<AxiosResponse<T>>;
      }

      // Chat endpoints
      if (url === '/chats' && method === 'get') {
        // Check if there's a task_id parameter
        if (config?.params?.task_id) {
          const taskId = parseInt(config.params.task_id);
          console.log(`[Mock API] Getting chats for task ${taskId}`);
          return mockApi.getTaskChats(taskId) as Promise<AxiosResponse<T>>;
        }
      }

      if (url === '/chats' && method === 'post') {
        console.log(`[Mock API] Creating chat message`);
        return mockApi.createChat(data) as Promise<AxiosResponse<T>>;
      }

      if (url.match(/\/chats\/\d+/) && method === 'delete') {
        // Extract the chat ID from the URL
        const matches = url.match(/\/chats\/(\d+)/);
        const chatId = matches ? parseInt(matches[1]) : 0;
        console.log(`[Mock API] Deleting chat ${chatId}`);
        return mockApi.deleteChat(chatId) as Promise<AxiosResponse<T>>;
      }

      // Activities endpoint
      if (url === '/activities' && method === 'get') {
        console.log(`[Mock API] Getting activities with params:`, config?.params);
        return mockApi.getRecentActivities(config?.params) as Promise<AxiosResponse<T>>;
      }

      // Team members endpoint
      if (url === '/team-members' && method === 'get') {
        console.log(`[Mock API] Getting team members`);
        return mockApi.getTeamMembers() as Promise<AxiosResponse<T>>;
      }

      // Default response for unhandled endpoints
      console.warn(`[Mock API] Unhandled endpoint: ${method.toUpperCase()} ${url}`);
      return Promise.resolve({
        data: {} as T,
        status: 200,
        statusText: 'OK',
        headers: {},
        config
      });
    } catch (error) {
      console.error(`[Mock API] Error handling ${method.toUpperCase()} ${url}:`, error);
      return Promise.reject(error);
    }
  };

  // Override other methods to use request
  mockAdapter.get = <T = any>(url: string, config?: AxiosRequestConfig) =>
    mockAdapter.request<T>({ ...config, method: 'get', url });

  mockAdapter.post = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    mockAdapter.request<T>({ ...config, method: 'post', url, data });

  mockAdapter.put = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    mockAdapter.request<T>({ ...config, method: 'put', url, data });

  mockAdapter.delete = <T = any>(url: string, config?: AxiosRequestConfig) =>
    mockAdapter.request<T>({ ...config, method: 'delete', url });

  return mockAdapter;
};

// Export the appropriate API based on the feature flag
const api = FEATURES.USE_MOCK_DATA ? createMockApiAdapter() : createRealApi();

export default api;
