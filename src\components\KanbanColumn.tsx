
import React, { memo, useMemo } from 'react';
import { List } from '@/api/listsApi';
import { Task } from '@/api/tasksApi';
import TaskCard from './TaskCard';


import {
  MoreHorizontal,
  Plus,
  Trash2,
  Edit
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import { Project } from '@/entities/Project';
import { cn } from '@/lib/utils';

interface KanbanColumnProps {
  list: List;
  tasks: Task[];
  project: Project | undefined;
  onAddTask: (listId: number) => void;
  onEditList: (list: List) => void;
  onDeleteList: (listId: number) => void;
  onTaskClick: (task: Task) => void;
}

const KanbanColumn: React.FC<KanbanColumnProps> = memo(({
  list,
  tasks,
  project,
  onAddTask,
  onEditList,
  onDeleteList,
  onTaskClick
}) => {
  const { user } = useAuth();

  // Memoize permissions to prevent recalculation on every render
  const permissions = useMemo(() => ({
    canEditTask: hasProjectPermission(project, user, ProjectPermission.EDIT_TASK),
    canCreateTask: hasProjectPermission(project, user, ProjectPermission.CREATE_TASK),
    canDeleteTask: hasProjectPermission(project, user, ProjectPermission.DELETE_TASK),
    canEditProject: hasProjectPermission(project, user, ProjectPermission.EDIT_PROJECT)
  }), [project, user]);

  const { canEditTask, canCreateTask, canDeleteTask, canEditProject } = permissions;
  return (
    <div className="flex flex-col min-w-[300px] w-[300px] bg-card border border-border rounded-xl shadow-sm overflow-hidden dark:shadow-lg dark:shadow-primary/5">
      {/* Column Header */}
      <div className="p-3 border-b border-border flex items-center justify-between bg-primary/5 dark:bg-primary/20 sticky top-0 z-10">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-primary"></div>
          <h3 className="font-medium text-foreground truncate">{list.title}</h3>
        </div>

        {canEditProject && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full hover:bg-accent/50">
                <MoreHorizontal size={16} className="text-muted-foreground" />
                <span className="sr-only">Column actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
                List Actions
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onEditList(list)} className="cursor-pointer">
                <Edit size={14} className="mr-2" />
                Edit List
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {canDeleteTask && (
                <DropdownMenuItem
                  onClick={() => onDeleteList(list.id)}
                  className="text-destructive focus:text-destructive cursor-pointer"
                >
                  <Trash2 size={14} className="mr-2" />
                  Delete List
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      <div className="p-2 flex-1 overflow-y-auto max-h-[calc(100vh-260px)] scrollbar-thin">
        {tasks.length > 0 ? (
          <div className="space-y-2.5">
            {tasks.map((task, index) => (
              <TaskCard
                key={task.id}
                task={task}
                index={index}
                canEditProject={canEditProject}
                onTaskClick={onTaskClick}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-24 text-center p-4 m-2 bg-muted/20 dark:bg-accent/30 border border-dashed border-border dark:border-accent/50 rounded-lg">
            <p className="text-sm text-muted-foreground dark:text-muted-foreground/90">No tasks in this list</p>
          </div>
        )}
      </div>

      {/* Add Task Button */}
      {canCreateTask && canEditProject && (
        <button
          onClick={() => onAddTask(list.id)}
          className="m-2 py-2 px-3 text-sm text-primary-foreground bg-primary hover:bg-primary/90 rounded-md flex items-center justify-center transition-colors dark:shadow-md dark:shadow-primary/10 dark:hover:shadow-primary/20"
        >
          <Plus size={14} className="mr-1.5" />
          Add Task
        </button>
      )}
    </div>
  );
});

export default KanbanColumn;
