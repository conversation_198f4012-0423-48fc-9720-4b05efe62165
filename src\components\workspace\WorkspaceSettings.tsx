import React, { useState, useEffect } from 'react';
import { Workspace } from '@/entities/Workspace';
import { User } from '@/entities/User';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { updateWorkspace, deleteWorkspace, getWorkspaces } from '@/api/workspacesApi';
import { Loader2 } from 'lucide-react';
// AllMembersModal removed as workspaces are now personal
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useWorkspace } from '@/context/WorkspaceContext';
import { removeFromLocalStorage } from '@/utils/sessionLocalStorageUtil';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface WorkspaceSettingsProps {
  workspace: Workspace;
  members: User[];
  onWorkspaceUpdated?: (updatedWorkspace: Workspace) => void;
}

const WorkspaceSettings: React.FC<WorkspaceSettingsProps> = ({
  workspace,
  members,
  onWorkspaceUpdated
}) => {
  const [activeTab, setActiveTab] = useState('general');
  const [name, setName] = useState(workspace.name);
  const [description, setDescription] = useState(workspace.description || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ name?: string }>({});
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { fetchWorkspaces, setCurrentWorkspace } = useWorkspace();



  // Update form when workspace prop changes
  useEffect(() => {
    setName(workspace.name);
    setDescription(workspace.description || '');
  }, [workspace]);



  // Validate form inputs
  const validateForm = (): boolean => {
    const newErrors: { name?: string } = {};

    if (!name.trim()) {
      newErrors.name = "Workspace name is required";
    } else if (name.length < 3) {
      newErrors.name = "Workspace name must be at least 3 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle workspace update
  const handleUpdateWorkspace = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const updatedWorkspace = await updateWorkspace(workspace.id, {
        name,
        description,
        background: workspace.background,
      });

      toast({
        title: "Workspace updated",
        description: "Your workspace settings have been updated successfully.",
        variant: "default",
      });

      if (onWorkspaceUpdated) {
        onWorkspaceUpdated(updatedWorkspace);
      }
    } catch (error) {
      console.error("Error updating workspace:", error);
      toast({
        title: "Failed to update workspace",
        description: "There was an error updating your workspace. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };



  // handleMemberUpdated removed as workspaces are now personal

  // Handle workspace deletion
  const handleDeleteWorkspace = async () => {
    try {
      setIsDeleting(true);

      // Get all workspaces before deletion to check if this is the last one
      const allWorkspaces = await getWorkspaces();
      const otherWorkspaces = allWorkspaces.filter(ws => ws.id !== workspace.id);

      // Delete the workspace
      await deleteWorkspace(workspace.id);

      // Clean up localStorage to prevent stale data
      removeFromLocalStorage('workspace');
      if (user?.id) {
        removeFromLocalStorage(`workspace_${user.id}`);
      }

      // Refresh the workspaces list in the context
      await fetchWorkspaces();

      toast({
        title: "Workspace deleted",
        description: "Your workspace has been deleted successfully.",
      });

      // Update the current workspace in the context
      if (otherWorkspaces.length > 0) {
        // Set the first available workspace as the current one
        setCurrentWorkspace(otherWorkspaces[0]);
        navigate(`/workspace/${otherWorkspaces[0].id}`);
      } else {
        // If no workspaces left, clear the current workspace
        setCurrentWorkspace(null);
        navigate('/dashboard');
      }
    } catch (error) {
      console.error("Error deleting workspace:", error);
      toast({
        title: "Failed to delete workspace",
        description: "There was an error deleting your workspace. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Workspace Details</CardTitle>
              <CardDescription>
                Update your workspace information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="workspace-name">Workspace Name</Label>
                  <Input
                    id="workspace-name"
                    value={name}
                    onChange={(e) => {
                      setName(e.target.value);
                      if (errors.name) setErrors({...errors, name: undefined});
                    }}
                    placeholder="My Workspace"
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="workspace-description">Description</Label>
                  <Textarea
                    id="workspace-description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Describe your workspace (optional)"
                    className="resize-none"
                    rows={4}
                  />
                </div>

                <Button
                  onClick={handleUpdateWorkspace}
                  disabled={isSubmitting}
                  className="mt-2"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Members tab removed as workspaces are now personal */}



        <TabsContent value="advanced" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Danger Zone</CardTitle>
              <CardDescription>
                Destructive actions for your workspace
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border border-destructive/20 bg-destructive/5 rounded-md">
                  <h3 className="text-lg font-medium text-destructive mb-2">Delete Workspace</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Once you delete a workspace, there is no going back. This action cannot be undone.
                    All projects, tasks, and data associated with this workspace will be permanently deleted.
                  </p>
                  <Button
                    variant="destructive"
                    onClick={() => setIsDeleteDialogOpen(true)}
                  >
                    Delete Workspace
                  </Button>

                  <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Workspace: "{workspace.name}"</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete the workspace <strong>"{workspace.name}"</strong>? This action cannot be undone.
                          All projects, tasks, and data associated with this workspace will be permanently deleted.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteWorkspace}
                          disabled={isDeleting}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          {isDeleting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Deleting...
                            </>
                          ) : (
                            'Delete Workspace'
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Members Modal removed as workspaces are now personal */}
    </div>
  );
};

export default WorkspaceSettings;
