import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>xis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import { cn } from '@/lib/utils';

interface DataItem {
  [key: string]: any;
}

interface LineConfig {
  key: string;
  color: string;
  name?: string;
  strokeWidth?: number;
  type?: 'monotone' | 'linear' | 'step' | 'stepBefore' | 'stepAfter' | 'natural' | 'basis';
  dot?: boolean | object;
  activeDot?: boolean | object;
}

interface LineChartComponentProps {
  data: DataItem[];
  xAxisKey: string;
  lines: LineConfig[];
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  className?: string;
  height?: number;
  width?: string | number;
  referenceLine?: {
    y?: number;
    x?: number | string;
    label?: string;
    color?: string;
  };
  margin?: { top?: number; right?: number; bottom?: number; left?: number };
  areaFill?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border border-border p-2 rounded-md shadow-md">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`tooltip-${index}`} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: <span className="font-medium">{entry.value}</span>
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const LineChartComponent: React.FC<LineChartComponentProps> = ({
  data,
  xAxisKey,
  lines,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  className,
  height = 300,
  width = '100%',
  referenceLine,
  margin = { top: 20, right: 30, left: 20, bottom: 40 },
  areaFill = false,
}) => {
  return (
    <div className={cn("w-full", className)}>
      <ResponsiveContainer width={width} height={height}>
        <LineChart
          data={data}
          margin={margin}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" opacity={0.2} />}
          
          <XAxis 
            dataKey={xAxisKey} 
            tick={{ fontSize: 12 }} 
            tickLine={false}
            axisLine={{ stroke: 'var(--border)' }}
          />
          <YAxis 
            tick={{ fontSize: 12 }} 
            tickLine={false}
            axisLine={{ stroke: 'var(--border)' }}
          />
          
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          {showLegend && <Legend wrapperStyle={{ paddingTop: 10 }} />}
          
          {referenceLine && referenceLine.y !== undefined && (
            <ReferenceLine 
              y={referenceLine.y} 
              stroke={referenceLine.color || "#ff7300"} 
              strokeDasharray="3 3" 
              label={referenceLine.label} 
            />
          )}
          
          {referenceLine && referenceLine.x !== undefined && (
            <ReferenceLine 
              x={referenceLine.x} 
              stroke={referenceLine.color || "#ff7300"} 
              strokeDasharray="3 3" 
              label={referenceLine.label} 
            />
          )}
          
          {lines.map((line, index) => (
            <Line
              key={`line-${index}`}
              type={line.type || "monotone"}
              dataKey={line.key}
              name={line.name || line.key}
              stroke={line.color}
              strokeWidth={line.strokeWidth || 2}
              dot={line.dot !== undefined ? line.dot : { r: 3 }}
              activeDot={line.activeDot !== undefined ? line.activeDot : { r: 6 }}
              isAnimationActive={true}
              animationDuration={1500}
              animationEasing="ease-in-out"
              fill={areaFill ? `${line.color}20` : undefined}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default LineChartComponent;
