import React, { useState } from 'react';
import { Loader2, Plus } from 'lucide-react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { createWorkspace } from "@/api/workspacesApi";
import { useWorkspace } from '@/context/WorkspaceContext';
import { useNavigate } from 'react-router-dom';

interface CreateWorkspaceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [workspaceName, setWorkspaceName] = useState('');
  const [workspaceDescription, setWorkspaceDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ name?: string; description?: string }>({});
  const { toast } = useToast();
  const { fetchWorkspaces } = useWorkspace();
  const navigate = useNavigate();

  // Validate form inputs
  const validateForm = (): boolean => {
    const newErrors: { name?: string; description?: string } = {};

    if (!workspaceName.trim()) {
      newErrors.name = "Workspace name is required";
    } else if (workspaceName.length < 3) {
      newErrors.name = "Workspace name must be at least 3 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle workspace creation
  const handleSubmitWorkspace = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const response = await createWorkspace({
        name: workspaceName,
        description: workspaceDescription,
        background: '', // Default empty background
      });

      console.log("Workspace created:", response);

      toast({
        title: "Workspace created",
        description: "Your new workspace has been created successfully.",
        variant: "default",
      });

      // Reset form
      setWorkspaceName("");
      setWorkspaceDescription("");

      // Close the modal
      onClose();

      // Refresh workspaces list
      await fetchWorkspaces();

      // Navigate to the new workspace
      navigate(`/workspace/${response.id}`);
    } catch (error) {
      console.error("Error creating workspace:", error);
      toast({
        title: "Failed to create workspace",
        description: "There was an error creating your workspace. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!isSubmitting) {
        onClose();
        if (!open) {
          // Reset form when dialog is closed
          setWorkspaceName("");
          setWorkspaceDescription("");
          setErrors({});
        }
      }
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Personal Workspace</DialogTitle>
          <DialogDescription>Fill in the details below to create a new personal workspace for organizing your projects.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Workspace Name
            </Label>
            <div className="col-span-3 space-y-1">
              <Input
                id="name"
                value={workspaceName}
                onChange={(e) => {
                  setWorkspaceName(e.target.value);
                  if (errors.name) setErrors({...errors, name: undefined});
                }}
                placeholder="My Workspace"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <div className="col-span-3">
              <Textarea
                id="description"
                value={workspaceDescription}
                onChange={(e) => setWorkspaceDescription(e.target.value)}
                placeholder="Describe your workspace (optional)"
                className="resize-none"
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmitWorkspace}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Personal Workspace"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateWorkspaceModal;
