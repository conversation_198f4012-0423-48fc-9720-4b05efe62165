import React, { useEffect, useState } from 'react';
import { useSearchParams, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Calendar, CheckCircle, Clock, FileText, Loader2, Search, User as UserIcon, X } from 'lucide-react';
import { globalSearch, SearchResults } from '@/api/searchApi';
import Navbar from '@/components/Navbar';
import { useWorkspace } from '@/context/WorkspaceContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import ProjectCard from '@/components/ProjectCard';

const SearchResultsPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q') || '';
  const [results, setResults] = useState<SearchResults | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { currentWorkspace } = useWorkspace();

  // Default to 'all' tab, or use the tab from URL
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'all');

  // Handle back button click - always navigate to workspace or dashboard
  const handleBackClick = () => {
    // Always navigate to workspace or dashboard, ignoring tab navigation history
    if (currentWorkspace?.id) {
      navigate(`/workspace/${currentWorkspace.id}`);
    } else {
      navigate('/dashboard');
    }
  };

  useEffect(() => {
    const fetchResults = async () => {
      if (!query || query.length < 2) {
        setResults(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const data = await globalSearch(query);
        setResults(data);
      } catch (err) {
        console.error('Search error:', err);
        setError('Failed to load search results. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [query]);

  // Update URL when tab changes without adding to history
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const newParams = new URLSearchParams(searchParams);
    newParams.set('tab', value);

    // Use replaceState to update URL without adding to history
    const newUrl = `/search?${newParams.toString()}`;
    window.history.replaceState(null, '', newUrl);
  };

  // Count results by category
  const projectCount = results?.results?.projects?.length || 0;
  const taskCount = results?.results?.tasks?.length || 0;
  const userCount = results?.results?.users?.length || 0;
  const totalCount = projectCount + taskCount + userCount;

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
          <div>
            <Button
              variant="ghost"
              className="mb-2 p-0 hover:bg-transparent"
              onClick={handleBackClick}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <h1 className="text-2xl font-bold flex items-center">
              <Search className="mr-2 h-5 w-5 text-muted-foreground" />
              Search Results
            </h1>
            <p className="text-muted-foreground mt-1">
              {isLoading ? 'Searching...' :
                totalCount > 0 ?
                  `Found ${totalCount} results for "${query}"` :
                  query ? `No results found for "${query}"` : 'Enter a search term to find projects, tasks, and users'}
            </p>
          </div>
        </div>

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full max-w-md" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          </div>
        ) : error ? (
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive flex items-center">
                <X className="mr-2 h-5 w-5" />
                Error
              </CardTitle>
              <CardDescription>{error}</CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => window.location.reload()}>Try Again</Button>
            </CardContent>
          </Card>
        ) : results && totalCount > 0 ? (
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="mb-6">
              <TabsTrigger value="all">
                All Results <Badge variant="secondary" className="ml-2">{totalCount}</Badge>
              </TabsTrigger>
              {projectCount > 0 && (
                <TabsTrigger value="projects">
                  Projects <Badge variant="secondary" className="ml-2">{projectCount}</Badge>
                </TabsTrigger>
              )}
              {taskCount > 0 && (
                <TabsTrigger value="tasks">
                  Tasks <Badge variant="secondary" className="ml-2">{taskCount}</Badge>
                </TabsTrigger>
              )}
              {userCount > 0 && (
                <TabsTrigger value="users">
                  Users <Badge variant="secondary" className="ml-2">{userCount}</Badge>
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {projectCount > 0 && (
                <div>
                  <h2 className="text-xl font-semibold mb-3">Projects</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {results.results.projects?.slice(0, 3).map(project => (
                      <SearchProjectCard key={project.id} project={project} />
                    ))}
                  </div>
                  {projectCount > 3 && (
                    <Button variant="link" onClick={() => handleTabChange('projects')}>
                      View all {projectCount} projects
                    </Button>
                  )}
                </div>
              )}

              {taskCount > 0 && (
                <div>
                  <h2 className="text-xl font-semibold mb-3">Tasks</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {results.results.tasks?.slice(0, 3).map(task => (
                      <TaskCard key={task.id} task={task} />
                    ))}
                  </div>
                  {taskCount > 3 && (
                    <Button variant="link" onClick={() => handleTabChange('tasks')}>
                      View all {taskCount} tasks
                    </Button>
                  )}
                </div>
              )}

              {userCount > 0 && (
                <div>
                  <h2 className="text-xl font-semibold mb-3">Users</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {results.results.users?.slice(0, 3).map(user => (
                      <UserCard key={user.id} user={user} />
                    ))}
                  </div>
                  {userCount > 3 && (
                    <Button variant="link" onClick={() => handleTabChange('users')}>
                      View all {userCount} users
                    </Button>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="projects">
              <h2 className="text-xl font-semibold mb-3">Projects ({projectCount})</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {results.results.projects?.map(project => (
                  <SearchProjectCard key={project.id} project={project} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="tasks">
              <h2 className="text-xl font-semibold mb-3">Tasks ({taskCount})</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {results.results.tasks?.map(task => (
                  <TaskCard key={task.id} task={task} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="users">
              <h2 className="text-xl font-semibold mb-3">Users ({userCount})</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {results.results.users?.map(user => (
                  <UserCard key={user.id} user={user} />
                ))}
              </div>
            </TabsContent>
          </Tabs>
        ) : query ? (
          <Card>
            <CardHeader>
              <CardTitle>No results found</CardTitle>
              <CardDescription>
                We couldn't find any matches for "{query}". Try adjusting your search term.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Suggestions:</p>
              <ul className="list-disc list-inside mt-2 text-muted-foreground">
                <li>Check for typos or spelling errors</li>
                <li>Try more general keywords</li>
                <li>Try different keywords</li>
              </ul>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Search for projects, tasks, and users</CardTitle>
              <CardDescription>
                Enter a search term in the search box at the top of the page to find what you're looking for.
              </CardDescription>
            </CardHeader>
          </Card>
        )}
      </main>
    </div>
  );
};

// SearchProjectCard Component - Wrapper for the main ProjectCard component
const SearchProjectCard: React.FC<{ project: any }> = ({ project }) => {
  // Map the search result project to the ProjectCardProps format
  const projectCardProps = {
    id: project.id.toString(),
    title: project.name,
    description: project.description || '',
    progress: typeof project.progress === 'number' ? project.progress : 0,
    dueDate: project.end_date || 'No date',
    priority: (project.priority as 'low' | 'medium' | 'high') || 'medium',
    status: (project.status as 'completed' | 'in-progress' | 'not-started' | 'at-risk') || 'not-started',
    members: project.members || [],
    isStarred: project.is_starred || false,
    is_group_project: project.is_group_project || false, // Added this property
    onStar: () => {}, // Empty function as we don't handle starring in search results
  };

  return <ProjectCard {...projectCardProps} />;
};

// Task Card Component
const TaskCard: React.FC<{ task: any }> = ({ task }) => {
  const getPriorityBadge = (priority: string) => {
    let variant = "default";

    if (priority === "low") {
      variant = "secondary";
    } else if (priority === "medium") {
      variant = "default";
    } else if (priority === "high") {
      variant = "destructive";
    }

    return <Badge variant={variant as any} className="capitalize">{priority}</Badge>;
  };

  const getStatusIcon = (status: string | number) => {
    // Handle both numeric and string status values for completed status
    if (
      status === 1 ||
      status === "1" ||
      status === "completed"
    ) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }

    // Handle both numeric and string status values for in-progress
    if (
      status === 2 ||
      status === "2" ||
      status === "in-progress"
    ) {
      return <Clock className="h-4 w-4 text-blue-500" />;
    }

    // Default to amber clock for todo and other statuses
    return <Clock className="h-4 w-4 text-amber-500" />;
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg flex items-center">
            {getStatusIcon(task.status)}
            <Link to={`/projects/${task.project_id}/board`} className="hover:underline ml-2">
              {task.title}
            </Link>
          </CardTitle>
          {task.priority && getPriorityBadge(task.priority)}
        </div>
        <CardDescription>
          {task.project?.name && (
            <Link to={`/projects/${task.project_id}`} className="flex items-center text-xs hover:underline">
              <FileText className="h-3 w-3 mr-1" />
              {task.project.name}
            </Link>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm line-clamp-2 text-muted-foreground">
          {task.description || "No description provided"}
        </p>
      </CardContent>
    </Card>
  );
};

// User Card Component
const UserCard: React.FC<{ user: any }> = ({ user }) => {
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex items-center space-x-4">
          <Avatar className="h-10 w-10">
            <AvatarImage src={user.profile_picture} alt={user.name} />
            <AvatarFallback>{getInitials(user.first_name, user.last_name)}</AvatarFallback>
          </Avatar>
          <div>
            <CardTitle className="text-lg">{user.name || `${user.first_name} ${user.last_name}`}</CardTitle>
            <CardDescription>{user.email}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center text-sm text-muted-foreground">
          <UserIcon className="h-4 w-4 mr-2" />
          Team Member
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchResultsPage;
