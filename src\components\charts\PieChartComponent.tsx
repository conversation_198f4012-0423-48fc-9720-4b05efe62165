import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Sector, Legend, Tooltip } from 'recharts';
import { cn } from '@/lib/utils';

interface DataItem {
  name: string;
  value: number;
  color?: string;
}

interface PieChartComponentProps {
  data: DataItem[];
  colors?: string[];
  showLegend?: boolean;
  showTooltip?: boolean;
  className?: string;
  innerRadius?: number;
  outerRadius?: number;
  height?: number;
  activeIndex?: number;
  setActiveIndex?: (index: number) => void;
}

// Default color palette
const DEFAULT_COLORS = [
  '#3498db', // Blue
  '#2ecc71', // Green
  '#e74c3c', // Red
  '#f39c12', // Orange
  '#9b59b6', // Purple
  '#1abc9c', // Teal
  '#34495e', // Dark Blue
  '#7f8c8d', // Gray
];

const renderActiveShape = (props: any) => {
  const {
    cx, cy, innerRadius, outerRadius, startAngle, endAngle,
    fill, payload, percent, value
  } = props;

  return (
    <g>
      <text x={cx} y={cy} dy={-20} textAnchor="middle" fill={fill} className="text-sm font-medium">
        {payload.name}
      </text>
      <text x={cx} y={cy} dy={8} textAnchor="middle" fill="#333" className="text-lg font-bold">
        {value}
      </text>
      <text x={cx} y={cy} dy={25} textAnchor="middle" fill="#999" className="text-xs">
        {`(${(percent * 100).toFixed(0)}%)`}
      </text>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius + 10}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={innerRadius - 5}
        outerRadius={innerRadius - 2}
        fill={fill}
      />
    </g>
  );
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border border-border p-2 rounded-md shadow-md">
        <p className="font-medium">{payload[0].name}</p>
        <p className="text-sm text-muted-foreground">
          Value: <span className="font-medium text-foreground">{payload[0].value}</span>
        </p>
        <p className="text-xs text-muted-foreground">
          {`(${(payload[0].payload.percent * 100).toFixed(0)}%)`}
        </p>
      </div>
    );
  }
  return null;
};

const PieChartComponent: React.FC<PieChartComponentProps> = ({
  data,
  colors = DEFAULT_COLORS,
  showLegend = true,
  showTooltip = true,
  className,
  innerRadius = 60,
  outerRadius = 80,
  height = 300,
  activeIndex: externalActiveIndex,
  setActiveIndex: externalSetActiveIndex,
}) => {
  const [internalActiveIndex, setInternalActiveIndex] = useState<number>(-1);

  const activeIndex = externalActiveIndex !== undefined ? externalActiveIndex : internalActiveIndex;
  const setActiveIndex = externalSetActiveIndex || setInternalActiveIndex;

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const onPieLeave = () => {
    setActiveIndex(-1);
  };

  // Calculate percentages for each item
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const dataWithPercent = data.map(item => ({
    ...item,
    percent: item.value / total
  }));

  return (
    <div className={cn("w-full", className)}>
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            activeIndex={activeIndex}
            activeShape={renderActiveShape}
            data={dataWithPercent}
            cx="50%"
            cy="50%"
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            dataKey="value"
            onMouseEnter={onPieEnter}
            onMouseLeave={onPieLeave}
            paddingAngle={2}
          >
            {dataWithPercent.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.color || colors[index % colors.length]}
                stroke="var(--background)"
                strokeWidth={2}
              />
            ))}
          </Pie>
          {showLegend && (
            <Legend
              layout="horizontal"
              verticalAlign="bottom"
              align="center"
              wrapperStyle={{ paddingTop: 20 }}
            />
          )}
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PieChartComponent;
