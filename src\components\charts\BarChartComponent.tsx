import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Car<PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { cn } from '@/lib/utils';

interface DataItem {
  [key: string]: any;
}

interface BarChartComponentProps {
  data: DataItem[];
  xAxisKey: string;
  yAxisKey: string;
  barColor?: string;
  barColors?: string[];
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  className?: string;
  height?: number;
  width?: string | number;
  layout?: 'vertical' | 'horizontal';
  stackedBars?: { key: string; color: string; name?: string }[];
  maxBarSize?: number;
  margin?: { top?: number; right?: number; bottom?: number; left?: number };
}

// Default color palette
const DEFAULT_COLORS = [
  '#3498db', // Blue
  '#2ecc71', // Green
  '#e74c3c', // Red
  '#f39c12', // Orange
  '#9b59b6', // Purple
  '#1abc9c', // Teal
  '#34495e', // Dark Blue
  '#7f8c8d', // Gray
];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border border-border p-2 rounded-md shadow-md">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`tooltip-${index}`} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: <span className="font-medium">{entry.value}</span>
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const BarChartComponent: React.FC<BarChartComponentProps> = ({
  data,
  xAxisKey,
  yAxisKey,
  barColor = '#3498db',
  barColors,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  className,
  height = 300,
  width = '100%',
  layout = 'horizontal',
  stackedBars,
  maxBarSize = 40,
  margin = { top: 20, right: 30, left: 20, bottom: 40 },
}) => {
  // If we have stacked bars, we don't need to use the yAxisKey directly
  const isStacked = !!stackedBars && stackedBars.length > 0;

  return (
    <div className={cn("w-full", className)}>
      <ResponsiveContainer width={width} height={height}>
        <BarChart
          data={data}
          layout={layout}
          margin={margin}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" opacity={0.2} />}
          
          {layout === 'horizontal' ? (
            <>
              <XAxis 
                dataKey={xAxisKey} 
                tick={{ fontSize: 12 }} 
                tickLine={false}
                axisLine={{ stroke: 'var(--border)' }}
              />
              <YAxis 
                tick={{ fontSize: 12 }} 
                tickLine={false}
                axisLine={{ stroke: 'var(--border)' }}
              />
            </>
          ) : (
            <>
              <XAxis 
                type="number" 
                tick={{ fontSize: 12 }} 
                tickLine={false}
                axisLine={{ stroke: 'var(--border)' }}
              />
              <YAxis 
                dataKey={xAxisKey} 
                type="category" 
                tick={{ fontSize: 12 }} 
                tickLine={false}
                axisLine={{ stroke: 'var(--border)' }}
              />
            </>
          )}
          
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          {showLegend && <Legend wrapperStyle={{ paddingTop: 10 }} />}
          
          {isStacked ? (
            // Render stacked bars
            stackedBars.map((bar, index) => (
              <Bar 
                key={`bar-${index}`} 
                dataKey={bar.key} 
                name={bar.name || bar.key} 
                fill={bar.color} 
                stackId="stack"
                maxBarSize={maxBarSize}
                radius={[4, 4, 0, 0]}
              />
            ))
          ) : (
            // Render single bar with optional color per bar
            <Bar 
              dataKey={yAxisKey} 
              fill={barColor}
              maxBarSize={maxBarSize}
              radius={[4, 4, 0, 0]}
            >
              {barColors && data.map((_, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={barColors[index % barColors.length] || DEFAULT_COLORS[index % DEFAULT_COLORS.length]} 
                />
              ))}
            </Bar>
          )}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BarChartComponent;
