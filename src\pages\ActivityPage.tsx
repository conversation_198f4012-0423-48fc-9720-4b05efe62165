import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Activity, Filter, Calendar, ArrowDownUp } from 'lucide-react';
import Navbar from '@/components/Navbar';
import ActivityFeed from '@/components/activity/ActivityFeed';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

const ActivityPage: React.FC = () => {
  const { t } = useTranslation();
  const [activityType, setActivityType] = useState<string>("all");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold flex items-center">
            <Activity className="mr-2 h-6 w-6 text-primary" />
            {t('activity.pageTitle', 'Activity Feed')}
          </h1>
          <p className="text-muted-foreground">
            {t('activity.pageDescription', 'Track all activities across your projects and team.')}
          </p>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-col sm:flex-row gap-3 items-start sm:items-center">
          <div className="flex items-center gap-3 flex-wrap">
            {/* Activity Type Filter */}
            <Select value={activityType} onValueChange={setActivityType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('activity.filterByType', 'Filter by type')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('activity.allTypes', 'All Types')}</SelectItem>
                <SelectItem value="project_created">{t('activity.projects', 'Projects')}</SelectItem>
                <SelectItem value="task_completed">{t('activity.tasks', 'Tasks')}</SelectItem>
                <SelectItem value="user_joined">{t('activity.team', 'Team')}</SelectItem>
                <SelectItem value="comment_added">{t('activity.comments', 'Comments')}</SelectItem>
              </SelectContent>
            </Select>

            {/* Date Range Filter */}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    !dateRange.from && "text-muted-foreground"
                  )}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y")} -{" "}
                        {format(dateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>{t('activity.dateRange', 'Date range')}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange.from}
                  selected={dateRange}
                  onSelect={setDateRange}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>

            {/* Sort Order */}
            <Button
              variant="outline"
              size="icon"
              onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
              className="h-10 w-10"
            >
              <ArrowDownUp className={cn(
                "h-4 w-4 transition-transform",
                sortOrder === 'asc' && "rotate-180"
              )} />
              <span className="sr-only">{t('activity.sortOrder', 'Sort order')}</span>
            </Button>
          </div>

          <div className="ml-auto">
            <Button
              variant="outline"
              className="gap-1.5"
              onClick={() => {
                setActivityType('all');
                setDateRange({ from: undefined, to: undefined });
                setSortOrder('desc');
              }}
            >
              <Filter className="h-4 w-4" />
              {t('activity.clearFilters', 'Clear Filters')}
            </Button>
          </div>
        </div>

        {/* Activity Feed */}
        <ActivityFeed
          limit={20}
          showFilters={false}
          className="max-w-4xl mx-auto"
          activityType={activityType === 'all' ? undefined : activityType}
          dateRange={dateRange.from ? dateRange : undefined}
          sortOrder={sortOrder}
        />
      </main>
    </div>
  );
};

export default ActivityPage;
