import { useState } from 'react';
import { User } from '@/entities/User';
import { Workspace } from '@/entities/Workspace';
import { ChevronsRight, Users, Crown } from 'lucide-react';
import AllMembersModal from './AllMembersModal';
import { useWorkspace } from '@/context/WorkspaceContext';
import { useTranslation } from 'react-i18next';
import UserAvatar from '@/components/ui/user-avatar';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

type WorkspaceTeamMembersProps = {
    currentWorkspace: Workspace | null;
    members: User[];
};

const WorkspaceTeamMembers: React.FC<WorkspaceTeamMembersProps> = ({
    currentWorkspace,
    members,
}) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { fetchWorkspaceById } = useWorkspace();
    const { t } = useTranslation();

    if (!currentWorkspace) return null;

    // Only show up to 5 members in the preview
    const displayedMembers = members
        .filter((member) => member.id !== currentWorkspace.owner.id)
        .slice(0, 4);

    const totalMembers = members.length;
    const hiddenMembersCount = totalMembers - displayedMembers.length - 1; // -1 for the owner

    const handleMemberAdded = () => {
        // Refresh workspace data when a member is added or removed
        if (currentWorkspace) {
            fetchWorkspaceById(currentWorkspace.id.toString());
        }
    };

    return (
        <>
            <motion.div
                className="bg-card border border-border rounded-xl shadow-subtle overflow-hidden mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ type: "spring", stiffness: 300, damping: 24 }}
            >
                <div className="p-5 border-b border-border flex justify-between items-center">
                    <div className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-primary" />
                        <h2 className="text-xl font-semibold">{t('workspace.team')}</h2>
                    </div>
                    <button
                        className="text-primary hover:text-primary/80 text-sm font-medium transition-colors flex items-center hover:bg-primary/10 px-2 py-1 rounded"
                        onClick={() => setIsModalOpen(true)}
                    >
                        {t('workspace.viewAll')}
                        <ChevronsRight size={16} className="ml-1" />
                    </button>
                </div>
                <div className="p-5">
                    <motion.div
                        className="space-y-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ staggerChildren: 0.1 }}
                    >
                        {/* Owner */}
                        <motion.div
                            key={currentWorkspace.owner.id}
                            className="flex items-center p-2 rounded-md hover:bg-accent/10 transition-colors"
                            initial={{ y: 10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ type: "spring", stiffness: 300, damping: 24 }}
                        >
                            <UserAvatar
                                user={currentWorkspace.owner}
                                size="md"
                                status="online"
                            />
                            <div className="ml-3 flex-1">
                                <div className="flex items-center">
                                    <h4 className="font-medium">{currentWorkspace.owner.first_name} {currentWorkspace.owner.last_name}</h4>
                                    <Badge variant="outline" className="ml-2 bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1 px-1.5 py-0">
                                        <Crown className="h-3 w-3" />
                                        <span className="text-xs">{t('workspace.owner', 'Owner')}</span>
                                    </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground">{t('workspace.administrator', 'Administrator')}</p>
                            </div>
                        </motion.div>

                        {/* Members */}
                        {displayedMembers.map((member, index) => (
                            <motion.div
                                key={member.id}
                                className="flex items-center p-2 rounded-md hover:bg-accent/10 transition-colors"
                                initial={{ y: 10, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{
                                    type: "spring",
                                    stiffness: 300,
                                    damping: 24,
                                    delay: 0.05 * (index + 1)
                                }}
                            >
                                <UserAvatar
                                    user={member}
                                    size="md"
                                />
                                <div className="ml-3">
                                    <h4 className="font-medium">{member.first_name} {member.last_name}</h4>
                                    <p className="text-xs text-muted-foreground">{t('workspace.member', 'Member')}</p>
                                </div>
                            </motion.div>
                        ))}

                        {/* Show how many more members there are */}
                        {hiddenMembersCount > 0 && (
                            <motion.div
                                className={cn(
                                    "flex items-center justify-center p-3 border border-dashed border-border rounded-lg cursor-pointer",
                                    "hover:bg-accent/10 hover:border-primary/30 transition-all"
                                )}
                                onClick={() => setIsModalOpen(true)}
                                initial={{ y: 10, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{
                                    type: "spring",
                                    stiffness: 300,
                                    damping: 24,
                                    delay: 0.05 * (displayedMembers.length + 1)
                                }}
                            >
                                <div className="flex items-center">
                                    <Users className="h-4 w-4 text-muted-foreground mr-2" />
                                    <p className="text-sm text-muted-foreground">
                                        {t('workspace.moreMembersCount', '+{{count}} more members', { count: hiddenMembersCount })}
                                    </p>
                                </div>
                            </motion.div>
                        )}
                    </div>
                </div>
            </motion.div>

            <AllMembersModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                members={members}
                currentWorkspace={currentWorkspace}
                onMemberAdded={handleMemberAdded}
            />
        </>
    );
};

export default WorkspaceTeamMembers;
