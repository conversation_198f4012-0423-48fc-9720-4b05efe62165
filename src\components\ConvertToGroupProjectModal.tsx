import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Project } from '@/entities/Project';
import { updateProject } from '@/api/projectsApi';
import { toast } from 'sonner';

interface ConvertToGroupProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project;
  onProjectUpdated: (project: Project) => void;
}

const ConvertToGroupProjectModal: React.FC<ConvertToGroupProjectModalProps> = ({
  isOpen,
  onClose,
  project,
  onProjectUpdated
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleConvert = async () => {
    try {
      setIsLoading(true);
      
      // Update the project to be a group project
      const updatedProject = await updateProject(project.id, {
        ...project,
        is_group_project: true
      });
      
      toast.success('Project converted to group project successfully');
      onProjectUpdated(updatedProject);
      onClose();
    } catch (error) {
      console.error('Error converting project:', error);
      toast.error('Failed to convert project');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Convert to Group Project</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Converting "{project.name}" to a group project will allow you to:
          </p>
          
          <ul className="list-disc pl-5 mt-2 text-sm text-gray-500 dark:text-gray-400">
            <li>Invite other users to collaborate on this project</li>
            <li>Assign different roles (Admin, Editor, Viewer) to collaborators</li>
            <li>Allow team members to work together on tasks</li>
          </ul>
          
          <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
            You will remain the owner of this project with full control.
          </p>
          
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              <strong>Note:</strong> This action cannot be undone. Once converted to a group project, 
              it cannot be changed back to a personal project.
            </p>
          </div>
        </div>
        
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <Button 
            onClick={handleConvert} 
            disabled={isLoading}
          >
            {isLoading ? 'Converting...' : 'Convert to Group Project'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConvertToGroupProjectModal;
