<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Suite</title>
    <meta name="description" content="Project Management Suite" />
    <meta name="author" content="Suite" />
    <meta property="og:image" content="/og-image.png" />
    <meta name="csrf-token" content="" />
    <link rel="icon" type="image/jpg" href="./assets/logo.png" />
    <!-- Ensure React is available globally -->
    <script>
      // Create a minimal React shim with essential methods
      window.React = window.React || {
        createElement: function() { return {}; },
        createContext: function() { return { Provider: {}, Consumer: {} }; },
        forwardRef: function(render) {
          return {
            $$typeof: Symbol.for('react.forward_ref'),
            render: render
          };
        },
        useState: function(initialState) { return [initialState, function() {}]; },
        useEffect: function() {},
        useContext: function() { return {}; },
        Fragment: Symbol.for('react.fragment')
      };
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
