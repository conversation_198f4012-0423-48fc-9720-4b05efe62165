import React from 'react';
import { User } from '@/entities/User';

/**
 * Format message text with @mentions highlighted
 * 
 * @param message The message text containing @mentions
 * @param mentionedUsers Array of mentioned users
 * @returns React elements with formatted mentions
 */
export const formatMessageWithMentions = (
  message: string,
  mentionedUsers: User[] = []
): React.ReactNode[] => {
  if (!message) return [];
  if (!mentionedUsers || mentionedUsers.length === 0) return [message];

  // Create a map of usernames to user objects for quick lookup
  const userMap = new Map<string, User>();
  mentionedUsers.forEach(user => {
    const username = `${user.first_name.toLowerCase()}${user.last_name.toLowerCase()}`;
    userMap.set(username, user);
  });

  // Regular expression to find @mentions
  const mentionRegex = /@([a-zA-Z0-9._]+)/g;
  
  // Split the message by mentions
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;
  
  while ((match = mentionRegex.exec(message)) !== null) {
    const username = match[1];
    const user = userMap.get(username.toLowerCase());
    
    // Add text before the mention
    if (match.index > lastIndex) {
      parts.push(message.substring(lastIndex, match.index));
    }
    
    // Add the mention as a styled span if the user exists
    if (user) {
      parts.push(
        <span 
          key={`mention-${match.index}`}
          className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-1 rounded font-medium"
        >
          @{username}
        </span>
      );
    } else {
      // If user not found, just add the raw @mention
      parts.push(match[0]);
    }
    
    lastIndex = match.index + match[0].length;
  }
  
  // Add any remaining text after the last mention
  if (lastIndex < message.length) {
    parts.push(message.substring(lastIndex));
  }
  
  return parts;
};
