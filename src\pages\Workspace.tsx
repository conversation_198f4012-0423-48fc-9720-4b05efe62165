import { useEffect, useRef, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import Navbar from '../components/Navbar';
import StatusCard from '../components/StatusCard';
import {
  CheckCircle,
  Clock,
  FileCheck,
  PlayCircle,
  AlertTriangle
} from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import { useWorkspace } from '@/context/WorkspaceContext';
import Loader from '@/components/Loader';
import WorkspaceHeader from '@/components/workspace/WorkspaceHeader';
import { TurkishDateHelper } from '@/utils/dateUtils';
import AllUserProjects from '@/components/workspace/AllUserProjects';
// WorkspaceTeamMembers removed as workspaces are now personal
import WorkspaceUpcomingDeadlines from '@/components/workspace/WorkspaceUpcomingDeadlines';
import { useToast } from '@/components/ui/use-toast';
import { setLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import { useTranslation } from 'react-i18next';





const Workspace = () => {
  const { user } = useAuth();
  const {
    currentWorkspace,
    isLoading,
    setIsLoading,
    deadLines,
    workspaceStatistics,
    fetchWorkspaceById,
    projects,
    members,
    error: workspaceError
  } = useWorkspace();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const { t } = useTranslation();

  const { workspaceId } = useParams();

  const hasFetchedRef = useRef(false);

  // Define loadWorkspace outside useEffect so it can be called from retry button
  const loadWorkspace = async () => {
    if (!workspaceId) {
      // Redirect to dashboard to get a default workspace
      navigate('/dashboard');
      return;
    }

    // Log for debugging
    console.log('Workspace - Loading workspace ID:', workspaceId);

    setIsLoading(true);
    setLoadingError(null);

    try {
      await fetchWorkspaceById(workspaceId);

      // Save current workspace to localStorage for persistence
      if (currentWorkspace) {
        setLocalStorageData('workspace', currentWorkspace, 24 * 60 * 60 * 1000); // 24 hours expiry
      }
    } catch (error) {
      console.error("Error loading workspace:", error);

      // Check if it's a 404 error
      if (error.response && error.response.status === 404) {
        setLoadingError(t('workspace.notFound', "Workspace not found or you don't have access to it."));
        toast({
          title: t('workspace.notFoundTitle', "Workspace not found"),
          description: t('workspace.notFoundDescription', "The workspace you're trying to access doesn't exist or you don't have permission to view it."),
          variant: "destructive",
        });

        // Redirect to dashboard to get a default workspace after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 3000);
      } else {
        setLoadingError(t('workspace.loadingError'));
        toast({
          title: t('common.error', "Error"),
          description: t('workspace.loadingError'),
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // If no workspaceId is provided, redirect to dashboard to get a default workspace
    if (!workspaceId) {
      navigate('/dashboard');
      return;
    }

    // Reset the fetch flag when workspaceId changes to ensure data is refreshed
    hasFetchedRef.current = false;

    // Load the workspace data
    loadWorkspace();
  }, [workspaceId, navigate]);

  // Update document title when workspace changes
  useEffect(() => {
    if (currentWorkspace) {
      document.title = `${currentWorkspace.name} | Suite`;
    } else {
      document.title = 'Suite';
    }
  }, [currentWorkspace]);





  // Handle workspace refresh after project creation
  const handleWorkspaceRefresh = async () => {
    if (workspaceId) {
      setIsLoading(true);
      try {
        await fetchWorkspaceById(workspaceId);
        toast({
          title: "Workspace refreshed",
          description: "Workspace data has been updated.",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to refresh workspace data.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }
  };


  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      {isLoading && <Loader />}

      {loadingError && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center mb-6">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span>{loadingError}</span>
            <button
              onClick={() => {
                hasFetchedRef.current = false;
                loadWorkspace();
              }}
              className="ml-auto bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-md text-sm"
            >
              {t('workspace.retry')}
            </button>
          </div>
        </div>
      )}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <WorkspaceHeader
          currentWorkspace={currentWorkspace}
          user={user}
          onProjectCreated={handleWorkspaceRefresh}
        />

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Projects Section - Takes up 2/3 of the grid on large screens */}
          <div className="lg:col-span-2">
            <AllUserProjects workspaceId={workspaceId} />
          </div>

          <div>
            {/* Project Statistics Overview - Compact Design */}
            <div className="bg-card border border-border rounded-xl shadow-subtle overflow-hidden mb-6">
              <div className="p-3 border-b border-border">
                <h2 className="text-sm font-semibold flex items-center">
                  <FileCheck className="h-4 w-4 text-primary mr-1.5" />
                  {t('workspace.projectsOverview')}
                </h2>
              </div>

              <div className="p-3">
                <div className="grid grid-cols-2 gap-3">
                  {/* All Projects */}
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-2.5 border border-blue-200 dark:border-blue-800/30 relative overflow-hidden">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full flex items-center justify-center bg-blue-200 dark:bg-blue-800/50 mr-2.5">
                        <FileCheck className="h-4 w-4 text-blue-700 dark:text-blue-300" />
                      </div>
                      <div>
                        <p className="text-xs font-medium text-blue-700 dark:text-blue-400">{t('workspace.statistics.allProjects')}</p>
                        <div className="flex items-center">
                          <h3 className="text-xl font-bold text-blue-900 dark:text-blue-300">{workspaceStatistics?.total_projects || 0}</h3>
                          {workspaceStatistics?.projects_percentage_change !== undefined && (
                            <span className={`text-xs font-medium px-1.5 py-0.5 rounded-full ml-2 ${
                              Number(workspaceStatistics?.projects_percentage_change) > 0
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                                : Number(workspaceStatistics?.projects_percentage_change) < 0
                                  ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400'
                            }`}>
                              {Number(workspaceStatistics?.projects_percentage_change) > 0 ? '+' : ''}
                              {workspaceStatistics?.projects_percentage_change}%
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-blue-300 dark:bg-blue-700/50"></div>
                  </div>

                  {/* In Progress */}
                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-2.5 border border-orange-200 dark:border-orange-800/30 relative overflow-hidden">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full flex items-center justify-center bg-orange-200 dark:bg-orange-800/50 mr-2.5">
                        <PlayCircle className="h-4 w-4 text-orange-700 dark:text-orange-300" />
                      </div>
                      <div>
                        <p className="text-xs font-medium text-orange-700 dark:text-orange-400">{t('workspace.statistics.inProgress')}</p>
                        <div className="flex items-center">
                          <h3 className="text-xl font-bold text-orange-900 dark:text-orange-300">{workspaceStatistics?.statuses?.in_progress?.count || 0}</h3>
                          {workspaceStatistics?.statuses?.in_progress?.percentage_change !== undefined && (
                            <span className={`text-xs font-medium px-1.5 py-0.5 rounded-full ml-2 ${
                              Number(workspaceStatistics?.statuses?.in_progress?.percentage_change) > 0
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                                : Number(workspaceStatistics?.statuses?.in_progress?.percentage_change) < 0
                                  ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400'
                            }`}>
                              {Number(workspaceStatistics?.statuses?.in_progress?.percentage_change) > 0 ? '+' : ''}
                              {workspaceStatistics?.statuses?.in_progress?.percentage_change}%
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-orange-300 dark:bg-orange-700/50"></div>
                  </div>

                  {/* Completed */}
                  <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-2.5 border border-green-200 dark:border-green-800/30 relative overflow-hidden">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full flex items-center justify-center bg-green-200 dark:bg-green-800/50 mr-2.5">
                        <CheckCircle className="h-4 w-4 text-green-700 dark:text-green-300" />
                      </div>
                      <div>
                        <p className="text-xs font-medium text-green-700 dark:text-green-400">{t('workspace.statistics.completed')}</p>
                        <div className="flex items-center">
                          <h3 className="text-xl font-bold text-green-900 dark:text-green-300">{workspaceStatistics?.statuses?.completed?.count || 0}</h3>
                          {workspaceStatistics?.statuses?.completed?.percentage_change !== undefined && (
                            <span className={`text-xs font-medium px-1.5 py-0.5 rounded-full ml-2 ${
                              Number(workspaceStatistics?.statuses?.completed?.percentage_change) > 0
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                                : Number(workspaceStatistics?.statuses?.completed?.percentage_change) < 0
                                  ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400'
                            }`}>
                              {Number(workspaceStatistics?.statuses?.completed?.percentage_change) > 0 ? '+' : ''}
                              {workspaceStatistics?.statuses?.completed?.percentage_change}%
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-green-300 dark:bg-green-700/50"></div>
                  </div>

                  {/* At Risk */}
                  <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-2.5 border border-red-200 dark:border-red-800/30 relative overflow-hidden">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full flex items-center justify-center bg-red-200 dark:bg-red-800/50 mr-2.5">
                        <Clock className="h-4 w-4 text-red-700 dark:text-red-300" />
                      </div>
                      <div>
                        <p className="text-xs font-medium text-red-700 dark:text-red-400">{t('workspace.statistics.atRisk')}</p>
                        <div className="flex items-center">
                          <h3 className="text-xl font-bold text-red-900 dark:text-red-300">{workspaceStatistics?.statuses?.at_risk?.count || 0}</h3>
                          {workspaceStatistics?.statuses?.at_risk?.percentage_change !== undefined && (
                            <span className={`text-xs font-medium px-1.5 py-0.5 rounded-full ml-2 ${
                              Number(workspaceStatistics?.statuses?.at_risk?.percentage_change) > 0
                                ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                                : Number(workspaceStatistics?.statuses?.at_risk?.percentage_change) < 0
                                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400'
                            }`}>
                              {Number(workspaceStatistics?.statuses?.at_risk?.percentage_change) > 0 ? '+' : ''}
                              {workspaceStatistics?.statuses?.at_risk?.percentage_change}%
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-red-300 dark:bg-red-700/50"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* WorkspaceTeamMembers removed as workspaces are now personal */}
            <WorkspaceUpcomingDeadlines deadlines={deadLines} />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Workspace;
