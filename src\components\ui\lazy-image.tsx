import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { getOptimizedImageUrl, ImageSize, preloadImage } from '@/utils/imageUtils';
import { Skeleton } from './skeleton';

export interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string | undefined;
  alt: string;
  size?: ImageSize;
  fallback?: React.ReactNode;
  preload?: boolean;
  className?: string;
  containerClassName?: string;
  placeholderClassName?: string;
}

/**
 * LazyImage component that optimizes image loading with:
 * - Lazy loading
 * - Image size optimization
 * - Placeholder while loading
 * - Intersection Observer for viewport detection
 */
export function LazyImage({
  src,
  alt,
  size = ImageSize.MEDIUM,
  fallback,
  preload = false,
  className,
  containerClassName,
  placeholderClassName,
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Optimize image URL
  const optimizedSrc = src ? getOptimizedImageUrl(src, size) : undefined;
  
  // Set up intersection observer to detect when image is in viewport
  useEffect(() => {
    if (!containerRef.current) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { rootMargin: '200px' } // Start loading when image is 200px from viewport
    );
    
    observer.observe(containerRef.current);
    
    return () => {
      observer.disconnect();
    };
  }, []);
  
  // Preload image if preload prop is true
  useEffect(() => {
    if (preload && optimizedSrc) {
      preloadImage(optimizedSrc).catch(() => setError(true));
    }
  }, [preload, optimizedSrc]);
  
  // Handle image load and error events
  const handleLoad = () => {
    setIsLoaded(true);
  };
  
  const handleError = () => {
    setError(true);
  };
  
  return (
    <div 
      ref={containerRef}
      className={cn("relative overflow-hidden", containerClassName)}
    >
      {isInView && !error ? (
        <img
          ref={imgRef}
          src={optimizedSrc}
          alt={alt}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0",
            className
          )}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy"
          {...props}
        />
      ) : null}
      
      {(!isInView || !isLoaded) && !error && (
        <Skeleton 
          className={cn(
            "absolute inset-0 bg-muted",
            placeholderClassName
          )} 
        />
      )}
      
      {error && fallback ? (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          {fallback}
        </div>
      ) : null}
    </div>
  );
}
