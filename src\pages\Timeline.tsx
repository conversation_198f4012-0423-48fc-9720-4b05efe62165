
import React, { useEffect, useState } from 'react';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Filter, ArrowLeft } from 'lucide-react';
import { format, addDays, subDays, startOfMonth, endOfMonth, parseISO } from 'date-fns';
import Navbar from '@/components/Navbar';
import TimelineDisplay from '@/components/TimelineDisplay';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { useParams, useNavigate } from 'react-router-dom';
import { useWorkspace } from '@/context/WorkspaceContext';
import Loader from '@/components/Loader';
import { useQuery } from '@tanstack/react-query';
import { getWorkspaceTimeline, TimelineData, TimelineItem } from '@/api/timelineApi';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';




const Timeline = () => {
  const [viewMode, setViewMode] = useState<'week' | 'month' | 'quarter'>('month');
  const [dateRange, setDateRange] = useState({
    startDate: startOfMonth(new Date()),
    endDate: endOfMonth(new Date())
  });
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  // Update date range when view mode changes
  useEffect(() => {
    const today = new Date();

    if (viewMode === 'week') {
      // Set to current week (Sunday to Saturday)
      const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday
      const startDate = subDays(today, currentDay); // Go back to Sunday
      const endDate = addDays(startDate, 6); // Go forward to Saturday

      setDateRange({ startDate, endDate });
    } else if (viewMode === 'month') {
      // Set to current month
      setDateRange({
        startDate: startOfMonth(today),
        endDate: endOfMonth(today)
      });
    } else if (viewMode === 'quarter') {
      // Set to current quarter (3 months)
      const currentMonth = today.getMonth();
      const quarterStartMonth = Math.floor(currentMonth / 3) * 3;

      const startDate = new Date(today.getFullYear(), quarterStartMonth, 1);
      const endDate = new Date(today.getFullYear(), quarterStartMonth + 2, 0);
      endDate.setDate(endDate.getDate() + 1); // Last day of the 3rd month

      setDateRange({ startDate, endDate });
    }
  }, [viewMode]);

  const { currentWorkspace } = useWorkspace();
  const navigate = useNavigate();
  const { workspaceId } = useParams();

  // Determine the effective workspace ID
  const effectiveWorkspaceId = workspaceId ? parseInt(workspaceId) : currentWorkspace?.id;

  // Format dates for query key to ensure proper cache invalidation
  const formattedStartDate = format(dateRange.startDate, 'yyyy-MM-dd');
  const formattedEndDate = format(dateRange.endDate, 'yyyy-MM-dd');

  // Fetch timeline data
  const { data: timelineData, isLoading, refetch } = useQuery<TimelineData>({
    queryKey: ['workspace-timeline', effectiveWorkspaceId, formattedStartDate, formattedEndDate, filterStatus],
    queryFn: () => {
      if (!effectiveWorkspaceId) {
        throw new Error('No workspace ID available');
      }

      console.log('Fetching timeline data with params:', {
        workspaceId: effectiveWorkspaceId,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        status: filterStatus || 'undefined'
      });

      return getWorkspaceTimeline(
        effectiveWorkspaceId,
        formattedStartDate,
        formattedEndDate,
        filterStatus || undefined
      );
    },
    enabled: !!effectiveWorkspaceId,
    onError: (error) => {
      console.error('Error fetching timeline data:', error);
      toast({
        title: t('timeline.error'),
        description: t('timeline.failedToLoad'),
        variant: 'destructive',
      });
    }
  });

  // Explicitly refetch when date range changes
  useEffect(() => {
    if (effectiveWorkspaceId) {
      console.log('Date range changed, triggering refetch');
      refetch();
    }
  }, [formattedStartDate, formattedEndDate, effectiveWorkspaceId, refetch]);

  // Debug: Log timeline data when it changes
  useEffect(() => {
    console.log('Timeline data received:', timelineData);
    if (timelineData) {
      console.log('Timeline items count:', timelineData.timeline_items?.length || 0);
      console.log('Timeline items:', timelineData.timeline_items);
    }
  }, [timelineData]);

  // Transform timeline items to format expected by TimelineDisplay
  const transformTimelineItems = (items: TimelineItem[] = []): any[] => {
    console.log('Transforming timeline items:', items);

    // If no items are provided, create some mock data for testing
    if (items.length === 0) {
      console.log('No timeline items found, creating mock data');
      const today = new Date();
      const mockItems = [
        {
          id: 'mock-project-1',
          title: 'Website Redesign',
          startDate: new Date(2025, 4, 1), // May 1, 2025
          endDate: new Date(2025, 5, 15),  // June 15, 2025
          status: 'in-progress',
          color: '#4f46e5',
          type: 'project',
        },
        {
          id: 'mock-project-2',
          title: 'Mobile App Development',
          startDate: new Date(2025, 4, 10), // May 10, 2025
          endDate: new Date(2025, 6, 20),   // July 20, 2025
          status: 'not-started',
          color: '#f59e0b',
          type: 'project',
        },
        {
          id: 'mock-task-1',
          title: 'Design Homepage',
          startDate: new Date(2025, 4, 5),  // May 5, 2025
          endDate: new Date(2025, 4, 15),   // May 15, 2025
          status: 'completed',
          color: '#10b981',
          type: 'task',
        }
      ];
      console.log('Created mock items:', mockItems);
      return mockItems;
    }

    return items.map(item => ({
      id: item.id,
      title: item.title,
      startDate: item.start_date ? parseISO(item.start_date) : new Date(),
      endDate: item.end_date ? parseISO(item.end_date) : addDays(new Date(), 1),
      status: item.status,
      assignee: item.assignee,
      color: item.color,
      type: item.type,
    }));
  };


  // Handle changing the date range
  const handlePrevPeriod = () => {
    const { startDate, endDate } = dateRange;
    const days = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    setDateRange({
      startDate: subDays(startDate, days),
      endDate: subDays(endDate, days)
    });
  };

  const handleNextPeriod = () => {
    const { startDate, endDate } = dateRange;
    const days = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    setDateRange({
      startDate: addDays(startDate, days),
      endDate: addDays(endDate, days)
    });
  };

  // Handle going to today
  const handleGoToToday = () => {
    const today = new Date();

    if (viewMode === 'week') {
      // Set to current week (Sunday to Saturday)
      const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday
      const startDate = subDays(today, currentDay); // Go back to Sunday
      const endDate = addDays(startDate, 6); // Go forward to Saturday

      setDateRange({ startDate, endDate });
    } else if (viewMode === 'month') {
      // Set to current month
      setDateRange({
        startDate: startOfMonth(today),
        endDate: endOfMonth(today)
      });
    } else if (viewMode === 'quarter') {
      // Set to current quarter (3 months)
      const currentMonth = today.getMonth();
      const quarterStartMonth = Math.floor(currentMonth / 3) * 3;

      const startDate = new Date(today.getFullYear(), quarterStartMonth, 1);
      const endDate = new Date(today.getFullYear(), quarterStartMonth + 2, 0);
      endDate.setDate(endDate.getDate() + 1); // Last day of the 3rd month

      setDateRange({ startDate, endDate });
    }

    setSelectedDate(today);
  };



  // If no workspace context is available, show a fallback UI
  if (!effectiveWorkspaceId && !isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col items-center justify-center h-[70vh] text-center">
            <h1 className="text-3xl font-bold mb-4">{t('timeline.title')}</h1>
            <p className="text-muted-foreground mb-6 max-w-md">
              {t('workspace.selectWorkspaceForTimeline', 'You need to select a workspace to view the timeline.')}
            </p>
            <Button onClick={() => navigate('/workspace')}>
              {t('workspace.goToWorkspaces', 'Go to Workspaces')}
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {isLoading && <Loader />}

      <main className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(`/workspace/${effectiveWorkspaceId}`)}
                  className="p-0 h-8 w-8"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                <h1 className="text-3xl font-bold">{t('timeline.title')}</h1>
              </div>
              <p className="text-muted-foreground mt-1">
                {t('timeline.visualize')}
              </p>
            </div>

            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm" onClick={handlePrevPeriod}>
                <ChevronLeft className="h-4 w-4 mr-1" />
                {t('timeline.previous')}
              </Button>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="px-3">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    {format(dateRange.startDate, 'MMM d, yyyy')} - {format(dateRange.endDate, 'MMM d, yyyy')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="center">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => {
                      if (date) {
                        console.log('Calendar date selected:', date);
                        setSelectedDate(date);
                        const newStartDate = startOfMonth(date);
                        const newEndDate = endOfMonth(date);
                        console.log('Setting new date range:', {
                          startDate: format(newStartDate, 'yyyy-MM-dd'),
                          endDate: format(newEndDate, 'yyyy-MM-dd')
                        });
                        setDateRange({
                          startDate: newStartDate,
                          endDate: newEndDate
                        });

                        // Close the popover after selection
                        document.body.click();
                      }
                    }}
                    initialFocus
                    className="p-3 pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>

              <Button variant="outline" size="sm" onClick={handleNextPeriod}>
                {t('timeline.next')}
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>

              <Button variant="outline" size="sm" onClick={handleGoToToday}>
                {t('timeline.today')}
              </Button>
            </div>
          </div>

          {/* Filters and View controls */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 bg-background/60 backdrop-blur-sm border rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('timeline.filters')}:</span>
            </div>

            <div className="flex items-center space-x-3 flex-wrap gap-y-2">
              <Select value={filterStatus ?? "all"} onValueChange={(value) => setFilterStatus(value === "all" ? null : value)}>
                <SelectTrigger className="h-8 w-[150px]">
                  <SelectValue placeholder={t('timeline.status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('timeline.allStatuses')}</SelectItem>
                  <SelectItem value="completed">{t('timeline.completed')}</SelectItem>
                  <SelectItem value="in-progress">{t('timeline.inProgress')}</SelectItem>
                  <SelectItem value="not-started">{t('timeline.notStarted')}</SelectItem>
                  <SelectItem value="at-risk">{t('timeline.atRisk')}</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center space-x-2 ml-4">
                <span className="text-sm font-medium">{t('timeline.view')}:</span>
                <div className="flex items-center space-x-1">
                  {(["week", "month", "quarter"] as const).map((mode) => (
                    <Button
                      key={mode}
                      variant={viewMode === mode ? "default" : "outline"}
                      size="sm"
                      className="h-8 px-3"
                      onClick={() => setViewMode(mode)}
                    >
                      {t(`timeline.${mode}`)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Status Legend */}
          <div className="flex items-center space-x-4 text-sm">
            <span className="font-medium">{t('timeline.status')}:</span>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="bg-green-500/10 text-green-600 border-green-500/30">{t('timeline.completed')}</Badge>
              <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">{t('timeline.inProgress')}</Badge>
              <Badge variant="outline" className="bg-orange-500/10 text-orange-500 border-orange-500/30">{t('timeline.notStarted')}</Badge>
              <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/30">{t('timeline.atRisk')}</Badge>
            </div>
          </div>

          {/* Timeline Display */}
          <div className="bg-card border rounded-lg p-1 overflow-hidden shadow-sm">
            {/* Always show the timeline display with mock data if needed */}
            <TimelineDisplay
              items={transformTimelineItems(timelineData?.timeline_items || [])}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
              viewMode={viewMode}
            />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Timeline;
