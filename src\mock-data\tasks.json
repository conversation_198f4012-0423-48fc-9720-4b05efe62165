[{"id": 1, "project_id": 1, "title": "Design wireframes", "description": "Create wireframes for all main pages of the website", "status": "completed", "priority": "high", "assigned_to": 1, "due_date": "2025-05-01", "created_at": "2023-04-16T09:30:00Z", "updated_at": "2023-04-25T14:20:00Z"}, {"id": 2, "project_id": 1, "title": "Implement homepage", "description": "Code the homepage based on approved wireframes", "status": "in_progress", "priority": "high", "assigned_to": 1, "due_date": "2025-05-15", "created_at": "2023-04-26T10:15:00Z", "updated_at": "2023-05-05T11:30:00Z"}, {"id": 3, "project_id": 1, "title": "Create portfolio section", "description": "Design and implement the portfolio showcase section", "status": "to_do", "priority": "medium", "assigned_to": 1, "due_date": "2025-06-01", "created_at": "2023-04-26T10:20:00Z", "updated_at": "2023-04-26T10:20:00Z"}, {"id": 4, "project_id": 2, "title": "Create app mockups", "description": "Design mockups for all app screens", "status": "in_progress", "priority": "high", "assigned_to": 2, "due_date": "2025-05-20", "created_at": "2023-05-02T09:00:00Z", "updated_at": "2023-05-10T15:45:00Z"}, {"id": 5, "project_id": 2, "title": "Set up development environment", "description": "Configure development environment and project structure", "status": "completed", "priority": "high", "assigned_to": 3, "due_date": "2025-05-10", "created_at": "2023-05-02T09:15:00Z", "updated_at": "2023-05-08T14:30:00Z"}, {"id": 6, "project_id": 2, "title": "Implement user authentication", "description": "Create login, registration, and password reset functionality", "status": "to_do", "priority": "high", "assigned_to": 3, "due_date": "2025-06-01", "created_at": "2023-05-09T10:30:00Z", "updated_at": "2023-05-09T10:30:00Z"}, {"id": 7, "project_id": 3, "title": "Create marketing strategy", "description": "Develop comprehensive marketing strategy for product launch", "status": "completed", "priority": "high", "assigned_to": 2, "due_date": "2023-06-25", "created_at": "2023-06-11T09:00:00Z", "updated_at": "2023-06-24T16:15:00Z"}, {"id": 8, "project_id": 3, "title": "Design social media assets", "description": "Create graphics and templates for social media campaign", "status": "in_progress", "priority": "medium", "assigned_to": 2, "due_date": "2023-07-10", "created_at": "2023-06-25T10:30:00Z", "updated_at": "2023-07-01T11:45:00Z"}, {"id": 9, "project_id": 3, "title": "Write press release", "description": "Draft press release for product launch", "status": "to_do", "priority": "medium", "assigned_to": 1, "due_date": "2023-07-20", "created_at": "2023-06-25T10:45:00Z", "updated_at": "2023-06-25T10:45:00Z"}, {"id": 10, "project_id": 4, "title": "Database schema design", "description": "Design new database schema for cloud platform", "status": "completed", "priority": "high", "assigned_to": 3, "due_date": "2023-07-15", "created_at": "2023-07-02T09:30:00Z", "updated_at": "2023-07-14T15:20:00Z"}, {"id": 11, "project_id": 4, "title": "Data migration script", "description": "Write script to migrate data from legacy to new database", "status": "in_progress", "priority": "high", "assigned_to": 3, "due_date": "2023-08-01", "created_at": "2023-07-15T10:00:00Z", "updated_at": "2023-07-25T14:30:00Z"}, {"id": 12, "project_id": 4, "title": "Testing and validation", "description": "Test migration process and validate data integrity", "status": "to_do", "priority": "high", "assigned_to": 3, "due_date": "2023-08-10", "created_at": "2023-07-15T10:15:00Z", "updated_at": "2023-07-15T10:15:00Z"}, {"id": 13, "project_id": 5, "title": "Requirements gathering", "description": "Collect and document all requirements for the e-commerce platform", "status": "in_progress", "priority": "high", "assigned_to": 1, "due_date": "2025-05-20", "created_at": "2023-08-02T09:00:00Z", "updated_at": "2023-08-10T11:30:00Z"}, {"id": 14, "project_id": 5, "title": "Architecture design", "description": "Design system architecture for the e-commerce platform", "status": "to_do", "priority": "high", "assigned_to": 3, "due_date": "2025-06-10", "created_at": "2023-08-02T09:15:00Z", "updated_at": "2023-08-02T09:15:00Z"}, {"id": 15, "project_id": 5, "title": "UI/UX design", "description": "Create user interface designs for all platform pages", "status": "to_do", "priority": "medium", "assigned_to": 2, "due_date": "2025-06-30", "created_at": "2023-08-02T09:30:00Z", "updated_at": "2023-08-02T09:30:00Z"}, {"id": 16, "project_id": 6, "title": "CMS feature list", "description": "Define and document all features for the custom CMS", "status": "completed", "priority": "high", "assigned_to": 2, "due_date": "2023-07-30", "created_at": "2023-07-16T10:00:00Z", "updated_at": "2023-07-29T15:45:00Z"}, {"id": 17, "project_id": 6, "title": "Database design", "description": "Design database schema for the CMS", "status": "in_progress", "priority": "high", "assigned_to": 3, "due_date": "2023-08-15", "created_at": "2023-07-30T09:30:00Z", "updated_at": "2023-08-05T14:20:00Z"}, {"id": 18, "project_id": 6, "title": "Admin interface mockups", "description": "Create mockups for the admin interface", "status": "to_do", "priority": "medium", "assigned_to": 2, "due_date": "2023-09-01", "created_at": "2023-07-30T09:45:00Z", "updated_at": "2023-07-30T09:45:00Z"}, {"id": 19, "project_id": 7, "title": "Literature review", "description": "Review existing research papers on the topic", "status": "completed", "priority": "high", "assigned_to": 3, "due_date": "2023-06-30", "created_at": "2023-06-02T10:00:00Z", "updated_at": "2023-06-28T16:30:00Z"}, {"id": 20, "project_id": 7, "title": "Data collection", "description": "Collect and prepare datasets for analysis", "status": "in_progress", "priority": "high", "assigned_to": 3, "due_date": "2023-08-15", "created_at": "2023-07-01T09:30:00Z", "updated_at": "2023-07-20T14:45:00Z"}, {"id": 21, "project_id": 7, "title": "Algorithm development", "description": "Develop and test machine learning algorithms", "status": "to_do", "priority": "high", "assigned_to": 3, "due_date": "2023-10-01", "created_at": "2023-07-01T09:45:00Z", "updated_at": "2023-07-01T09:45:00Z"}, {"id": 22, "project_id": 8, "title": "Game concept design", "description": "Define game concept, mechanics, and storyline", "status": "completed", "priority": "high", "assigned_to": 1, "due_date": "2025-06-01", "created_at": "2023-05-16T10:00:00Z", "updated_at": "2023-05-30T15:30:00Z"}, {"id": 23, "project_id": 8, "title": "Character design", "description": "Create character designs and animations", "status": "completed", "priority": "medium", "assigned_to": 2, "due_date": "2025-07-01", "created_at": "2023-06-01T09:30:00Z", "updated_at": "2023-06-29T14:45:00Z"}, {"id": 24, "project_id": 8, "title": "Game development", "description": "Implement game mechanics and levels", "status": "completed", "priority": "high", "assigned_to": 1, "due_date": "2025-08-15", "created_at": "2023-07-01T10:00:00Z", "updated_at": "2023-08-10T16:30:00Z"}, {"id": 25, "project_id": 8, "title": "Testing and optimization", "description": "Test game on different devices and optimize performance", "status": "in_progress", "priority": "high", "assigned_to": 3, "due_date": "2025-09-01", "created_at": "2023-08-15T09:30:00Z", "updated_at": "2023-08-20T11:45:00Z"}]