import React from 'react';
import { Badge } from './badge';
import { <PERSON><PERSON><PERSON><PERSON>, Clock, AlertTriangle, CircleDashed } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

export type StatusType = 'completed' | 'in-progress' | 'todo' | 'at-risk' | 'not-started' | string;

export interface StatusBadgeProps {
  status: StatusType;
  showIcon?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
  variant?: 'default' | 'pill';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  showIcon = true,
  size = 'md',
  className,
  variant = 'default',
}) => {
  const { t } = useTranslation();

  // Map status values to standardized keys
  const normalizedStatus = (() => {
    if (!status) return 'todo';

    switch(status.toLowerCase()) {
      case 'completed':
        return 'completed';
      case 'in-progress':
      case 'in progress':
        return 'in-progress';
      case 'todo':
      case 'to do':
      case 'not-started':
      case 'not started':
        return 'todo';
      case 'at-risk':
      case 'at risk':
        return 'at-risk';
      default:
        return 'todo';
    }
  })();

  const getIconSize = (size: 'xs' | 'sm' | 'md' | 'lg') => {
    switch (size) {
      case 'xs':
        return 'h-3 w-3';
      case 'sm':
        return 'h-3.5 w-3.5';
      case 'md':
        return 'h-3.5 w-3.5';
      case 'lg':
        return 'h-4 w-4';
      default:
        return 'h-3.5 w-3.5';
    }
  };

  const iconSize = getIconSize(size);

  const statusConfig = {
    completed: {
      label: t('status.completed', 'Completed'),
      variant: 'success' as const,
      icon: <CheckCircle className={iconSize} />,
    },
    'in-progress': {
      label: t('status.inProgress', 'In Progress'),
      variant: 'info' as const,
      icon: <Clock className={iconSize} />,
    },
    todo: {
      label: t('status.todo', 'To Do'),
      variant: 'secondary' as const,
      icon: <CircleDashed className={iconSize} />,
    },
    'at-risk': {
      label: t('status.atRisk', 'At Risk'),
      variant: 'warning' as const,
      icon: <AlertTriangle className={iconSize} />,
    },
  };

  const config = statusConfig[normalizedStatus] || statusConfig.todo;
  const sizeClasses = {
    xs: 'text-[10px] py-0 px-1.5',
    sm: 'text-xs py-0 px-2',
    md: 'text-xs py-0.5 px-2.5',
    lg: 'text-sm py-1 px-3',
  };

  if (variant === 'pill') {
    return (
      <div className={cn(
        'inline-flex items-center gap-1 rounded-full border',
        sizeClasses[size],
        {
          'bg-success text-success-foreground border-success/20': config.variant === 'success',
          'bg-info text-info-foreground border-info/20': config.variant === 'info',
          'bg-warning text-warning-foreground border-warning/20': config.variant === 'warning',
          'bg-destructive text-destructive-foreground border-destructive/20': config.variant === 'destructive',
          'bg-secondary text-secondary-foreground border-secondary/20': config.variant === 'secondary',
          'bg-background border-border': config.variant === 'outline',
        },
        className
      )}>
        {showIcon && config.icon}
        <span className="font-medium">{config.label}</span>
      </div>
    );
  }

  return (
    <Badge
      variant={config.variant}
      className={cn(
        'gap-1 font-medium',
        sizeClasses[size],
        className
      )}
    >
      {showIcon && config.icon}
      {config.label}
    </Badge>
  );
};

export default StatusBadge;
