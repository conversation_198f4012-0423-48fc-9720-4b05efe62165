import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import {
  Activity,
  Bell,
  Calendar,
  ChevronDown,
  Clock,
  Home,
  LayoutDashboard,
  LogOut,
  Menu,
  Moon,
  Search,
  Settings,
  Sun,
  User,
  Users,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useWorkspace } from '@/context/WorkspaceContext';
import { useTheme } from '@/context/ThemeContext';
import { getLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import { getFullImageUrl } from '@/utils/imageUtils';
import NotificationDropdown from './NotificationDropdown';
import WorkspaceSwitcher from './workspace/WorkspaceSwitcher';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTranslation } from 'react-i18next';

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
}

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, active }) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn(
        "flex items-center gap-1",
        active && "bg-accent text-accent-foreground"
      )}
      asChild
    >
      <Link to={to}>
        {icon}
        <span>{label}</span>
      </Link>
    </Button>
  );
};

const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const profileRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);
  const { role, currentWorkspace, fetchWorkspaces } = useWorkspace();
  const { isDarkMode, toggleDarkMode } = useTheme();
  const { t } = useTranslation();

  // Get workspace from localStorage
  const [workspace, setWorkspace] = useState(getLocalStorageData('workspace'));

  // Fetch workspaces if workspace is not available
  useEffect(() => {
    // Only fetch if we don't have a workspace and there are no workspaces in context
    const shouldFetchWorkspaces = (!workspace || !workspace.id) && (!currentWorkspace || !currentWorkspace.id);

    if (shouldFetchWorkspaces) {
      console.log('Navbar - No workspace available, fetching workspaces');
      fetchWorkspaces()
        .then(() => {
          // Update workspace from localStorage after fetching
          const updatedWorkspace = getLocalStorageData('workspace');
          if (updatedWorkspace) {
            setWorkspace(updatedWorkspace);
          }
        })
        .catch(error => {
          console.error('Failed to fetch workspaces:', error);
        });
    }
  }, [fetchWorkspaces, workspace, currentWorkspace]);

  // Update workspace state when currentWorkspace changes
  useEffect(() => {
    if (currentWorkspace && currentWorkspace.id) {
      setWorkspace(currentWorkspace);
      // Log for debugging
      console.log('Navbar - Updated workspace from context:', currentWorkspace.name);
    }
  }, [currentWorkspace]);


  // Create navigation items with fallback paths if workspace is undefined
  const workspaceId = workspace?.id || currentWorkspace?.id;

  const navItems = [
    {
      path: workspaceId ? `/workspace/${workspaceId}` : '/dashboard',
      icon: <LayoutDashboard size={20} />,
      label: t('navbar.workspace')
    },
    {
      path: workspaceId ? `/timeline/${workspaceId}` : '/dashboard',
      icon: <Clock size={20} />,
      label: t('navbar.timeline')
    },
    {
      path: '/calendar',
      icon: <Calendar size={20} />,
      label: t('navbar.calendar')
    },
    {
      path: '/activity',
      icon: <Activity size={20} />,
      label: t('navbar.activity', 'Activity')
    },
    {
      path: '/team',
      icon: <Users size={20} />,
      label: t('navbar.team')
    },
  ];

  // Log workspace info for debugging
  useEffect(() => {
    console.log('Navbar - Current workspace:', workspace);
    console.log('Navbar - Context workspace:', currentWorkspace);
  }, [workspace, currentWorkspace]);

  // Close profile dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setIsProfileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus search input when opened
  useEffect(() => {
    if (isSearchOpen && searchRef.current) {
      searchRef.current.focus();
    }
  }, [isSearchOpen]);

  // Close search on escape key
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsSearchOpen(false);
      }
    };

    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, []);

  // Handle search submission
  const handleSearchSubmit = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (searchQuery.trim().length >= 2) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  // Handle key press in search input
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearchSubmit();
    }
  };

  return (
    <header className="sticky top-0 z-40 border-b bg-background shadow-sm">
      <div className="container flex h-16 items-center">
        {/* Logo and Mobile Menu Toggle */}
        <div className="flex items-center mr-6">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden mr-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <Menu size={20} />
            <span className="sr-only">Toggle menu</span>
          </Button>

          <Link to="/dashboard" className="flex items-center space-x-2.5">
            <div className="h-9 w-9 bg-primary rounded-md flex items-center justify-center shadow-sm">
              <Home className="h-5 w-5 text-white" />
            </div>
            <span className="font-bold text-xl tracking-tight">{t('navbar.suite')}</span>
          </Link>
        </div>

        {/* Workspace Switcher with improved styling */}
        <div className="hidden md:flex mr-6">
          <WorkspaceSwitcher />
        </div>

        {/* Main Navigation - Desktop with improved styling */}
        <nav className="hidden md:flex items-center space-x-1.5 flex-1">
          {navItems.map((item) => (
            <Button
              key={item.path}
              variant={location.pathname === item.path ? "secondary" : "ghost"}
              size="sm"
              className={cn(
                "flex items-center gap-1.5 h-9 px-3 rounded-md transition-colors",
                location.pathname === item.path ? "bg-accent text-accent-foreground font-medium" : "hover:bg-accent/50"
              )}
              asChild
            >
              <Link to={item.path}>
                {item.icon}
                <span>{item.label}</span>
              </Link>
            </Button>
          ))}
        </nav>

        {/* Right Side Items with improved spacing and hover effects */}
        <div className="flex items-center space-x-2">
          {/* Search Button with improved hover effect */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsSearchOpen(true)}
            className="text-muted-foreground hover:bg-accent/50 rounded-full h-9 w-9"
          >
            <Search size={18} />
            <span className="sr-only">Search</span>
          </Button>

          {/* Notifications with improved styling */}
          <NotificationDropdown />

          {/* Theme Toggle with improved hover effect */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleDarkMode}
            className="text-muted-foreground hover:bg-accent/50 rounded-full h-9 w-9"
          >
            {isDarkMode ? <Sun size={18} /> : <Moon size={18} />}
            <span className="sr-only">Toggle theme</span>
          </Button>

          {/* User Menu with improved styling */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full h-9 w-9 p-0 overflow-hidden border border-border hover:border-primary/50">
                <Avatar className="h-full w-full">
                  <AvatarImage src={user?.profile_picture ? getFullImageUrl(user.profile_picture) : ''} />
                  <AvatarFallback className="bg-primary/10 text-primary dark:bg-white/90 dark:text-primary font-medium">
                    {user?.first_name?.[0]}{user?.last_name?.[0]}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 p-1">
              <DropdownMenuLabel className="px-3 py-2">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">{user?.first_name} {user?.last_name}</p>
                  <p className="text-xs text-muted-foreground">{user?.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="my-1" />
              <DropdownMenuItem asChild className="px-3 py-2 cursor-pointer">
                <Link to="/profile" className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  <span>{t('navbar.profile')}</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild className="px-3 py-2 cursor-pointer">
                <Link to="/settings" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>{t('navbar.settings')}</span>
                </Link>
              </DropdownMenuItem>
              {currentWorkspace && (
                <DropdownMenuItem asChild className="px-3 py-2 cursor-pointer">
                  <Link to={`/workspace/${currentWorkspace.id}/settings`} className="flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>{t('navbar.workspaceSettings')}</span>
                  </Link>
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator className="my-1" />
              <DropdownMenuItem onClick={logout} className="text-destructive px-3 py-2 cursor-pointer">
                <LogOut className="mr-2 h-4 w-4" />
                <span>{t('navbar.logout')}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Mobile Navigation Menu with improved styling */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t bg-background shadow-md">
          <div className="container py-3">
            <nav className="flex flex-col space-y-2">
              {navItems.map((item) => (
                <Button
                  key={item.path}
                  variant={location.pathname === item.path ? "secondary" : "ghost"}
                  size="sm"
                  className={cn(
                    "justify-start h-10 px-3",
                    location.pathname === item.path
                      ? "bg-accent text-accent-foreground font-medium"
                      : "hover:bg-accent/50"
                  )}
                  asChild
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Link to={item.path}>
                    {item.icon}
                    <span className="ml-2">{item.label}</span>
                  </Link>
                </Button>
              ))}

              {/* Additional mobile-only menu items */}
              <div className="pt-2 mt-2 border-t border-border">
                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start w-full h-10 px-3"
                  asChild
                >
                  <Link to="/profile">
                    <User size={20} />
                    <span className="ml-2">{t('navbar.profile')}</span>
                  </Link>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start w-full h-10 px-3"
                  asChild
                >
                  <Link to="/settings">
                    <Settings size={20} />
                    <span className="ml-2">{t('navbar.settings')}</span>
                  </Link>
                </Button>
              </div>
            </nav>
          </div>
        </div>
      )}

      {/* Search Overlay with improved styling */}
      {isSearchOpen && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="container flex items-center justify-center h-full">
            <div className="w-full max-w-2xl bg-card p-6 rounded-xl shadow-lg border border-border">
              <div className="flex items-center justify-between mb-5">
                <h2 className="text-lg font-semibold">{t('search.title')}</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsSearchOpen(false)}
                  className="rounded-full h-8 w-8 hover:bg-accent/50"
                >
                  <X size={18} />
                  <span className="sr-only">Close</span>
                </Button>
              </div>
              <form onSubmit={handleSearchSubmit} className="flex gap-2">
                <input
                  ref={searchRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleSearchKeyPress}
                  placeholder={t('search.placeholder')}
                  className="flex h-11 w-full rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
                <Button type="submit" className="h-11 px-4">{t('search.button')}</Button>
              </form>
              <div className="mt-4 text-xs text-muted-foreground bg-muted p-3 rounded-md">
                <span className="font-medium text-foreground">{t('search.tipTitle', 'Pro tip:')}</span> {t('search.tip')}
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
