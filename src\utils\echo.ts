import Echo from 'laravel-echo';
import Pusher from 'pusher-js';
import { getLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import createMockEcho from './mockEcho';
import { FEATURES } from '@/config';

// Initialize Pusher with your credentials
window.Pusher = Pusher;

// Get Pusher credentials from environment variables or use hardcoded values
const pusherKey = import.meta.env.VITE_PUSHER_APP_KEY || '4860b3ed2c1c0628cc5a';
const pusherCluster = import.meta.env.VITE_PUSHER_APP_CLUSTER || 'eu';

// Ensure we're using the correct Pusher credentials
console.log('Using Pusher key:', pusherKey);
console.log('Using Pusher cluster:', pusherCluster);

// Check if we have valid Pusher credentials
const hasPusherCredentials = pusherKey !== undefined && pusherKey.length > 0;

// Initialize Laravel Echo or use a mock if using mock data or credentials are not available
let echo;

try {
  if (FEATURES.USE_MOCK_DATA) {
    console.log('Using mock data mode. Using mock Echo instance.');
    echo = createMockEcho();
  } else if (hasPusherCredentials) {
    console.log('Initializing Laravel Echo with Pusher credentials');
    echo = new Echo({
      broadcaster: 'pusher',
      key: pusherKey,
      cluster: pusherCluster,
      forceTLS: true,
      encrypted: true,
      disableStats: true,
      authEndpoint: `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/broadcasting/auth`,
      auth: {
        headers: {
          Authorization: `Bearer ${getLocalStorageData('token')?.toString() || ''}`,
          Accept: 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'X-Requested-With': 'XMLHttpRequest',
          'Content-Type': 'application/json',
        },
      },
      csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
    });
    console.log('Laravel Echo initialized successfully with key:', pusherKey);
  } else {
    console.warn('No valid Pusher credentials found. Using mock Echo instance.');
    echo = createMockEcho();
  }
} catch (error) {
  console.error('Failed to initialize Laravel Echo:', error);
  echo = createMockEcho();
}

export default echo;
