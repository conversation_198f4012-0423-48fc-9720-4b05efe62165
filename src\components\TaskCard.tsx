import React, { memo } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { Task } from '@/api/tasksApi';
import { GripVertical } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import StatusBadge from '@/components/ui/status-badge';
import PriorityIndicator from '@/components/ui/priority-indicator';
import { cn } from '@/lib/utils';
import { getFullImageUrl } from '@/utils/imageUtils';

// Create a cache for task colors to avoid recalculating
const taskColorCache = new Map<string | number, string>();
const taskTitleColorCache = new Map<string | number, string>();

// Task card color palette
const taskColors = [
  "bg-blue-50 border-blue-200 border-l-4 border-l-blue-400 hover:bg-blue-100 hover:border-blue-300 dark:bg-blue-900/40 dark:border-blue-800 dark:border-l-blue-500 dark:hover:bg-blue-900/60 dark:hover:border-blue-700 dark:shadow-md dark:shadow-blue-500/10",
  "bg-purple-50 border-purple-200 border-l-4 border-l-purple-400 hover:bg-purple-100 hover:border-purple-300 dark:bg-purple-900/40 dark:border-purple-800 dark:border-l-purple-500 dark:hover:bg-purple-900/60 dark:hover:border-purple-700 dark:shadow-md dark:shadow-purple-500/10",
  "bg-pink-50 border-pink-200 border-l-4 border-l-pink-400 hover:bg-pink-100 hover:border-pink-300 dark:bg-pink-900/40 dark:border-pink-800 dark:border-l-pink-500 dark:hover:bg-pink-900/60 dark:hover:border-pink-700 dark:shadow-md dark:shadow-pink-500/10",
  "bg-indigo-50 border-indigo-200 border-l-4 border-l-indigo-400 hover:bg-indigo-100 hover:border-indigo-300 dark:bg-indigo-900/40 dark:border-indigo-800 dark:border-l-indigo-500 dark:hover:bg-indigo-900/60 dark:hover:border-indigo-700 dark:shadow-md dark:shadow-indigo-500/10",
  "bg-teal-50 border-teal-200 border-l-4 border-l-teal-400 hover:bg-teal-100 hover:border-teal-300 dark:bg-teal-900/40 dark:border-teal-800 dark:border-l-teal-500 dark:hover:bg-teal-900/60 dark:hover:border-teal-700 dark:shadow-md dark:shadow-teal-500/10",
  "bg-green-50 border-green-200 border-l-4 border-l-green-400 hover:bg-green-100 hover:border-green-300 dark:bg-green-900/40 dark:border-green-800 dark:border-l-green-500 dark:hover:bg-green-900/60 dark:hover:border-green-700 dark:shadow-md dark:shadow-green-500/10",
  "bg-yellow-50 border-yellow-200 border-l-4 border-l-yellow-400 hover:bg-yellow-100 hover:border-yellow-300 dark:bg-yellow-900/40 dark:border-yellow-800 dark:border-l-yellow-500 dark:hover:bg-yellow-900/60 dark:hover:border-yellow-700 dark:shadow-md dark:shadow-yellow-500/10",
  "bg-orange-50 border-orange-200 border-l-4 border-l-orange-400 hover:bg-orange-100 hover:border-orange-300 dark:bg-orange-900/40 dark:border-orange-800 dark:border-l-orange-500 dark:hover:bg-orange-900/60 dark:hover:border-orange-700 dark:shadow-md dark:shadow-orange-500/10",
  "bg-red-50 border-red-200 border-l-4 border-l-red-400 hover:bg-red-100 hover:border-red-300 dark:bg-red-900/40 dark:border-red-800 dark:border-l-red-500 dark:hover:bg-red-900/60 dark:hover:border-red-700 dark:shadow-md dark:shadow-red-500/10",
  "bg-cyan-50 border-cyan-200 border-l-4 border-l-cyan-400 hover:bg-cyan-100 hover:border-cyan-300 dark:bg-cyan-900/40 dark:border-cyan-800 dark:border-l-cyan-500 dark:hover:bg-cyan-900/60 dark:hover:border-cyan-700 dark:shadow-md dark:shadow-cyan-500/10",
];

// Task title colors to match the card colors
const taskTitleColors = [
  "text-blue-700 dark:text-blue-300 dark:font-medium",
  "text-purple-700 dark:text-purple-300 dark:font-medium",
  "text-pink-700 dark:text-pink-300 dark:font-medium",
  "text-indigo-700 dark:text-indigo-300 dark:font-medium",
  "text-teal-700 dark:text-teal-300 dark:font-medium",
  "text-green-700 dark:text-green-300 dark:font-medium",
  "text-yellow-700 dark:text-yellow-300 dark:font-medium",
  "text-orange-700 dark:text-orange-300 dark:font-medium",
  "text-red-700 dark:text-red-300 dark:font-medium",
  "text-cyan-700 dark:text-cyan-300 dark:font-medium",
];

// Function to get a color based on task ID
const getTaskCardColor = (taskId: number | string) => {
  // Check if we have this color in the cache
  if (taskColorCache.has(taskId)) {
    return taskColorCache.get(taskId);
  }

  // Convert string ID to number if needed
  const id = typeof taskId === 'string' ? parseInt(taskId, 10) : taskId;
  // Use modulo to cycle through the colors
  const color = taskColors[id % taskColors.length];

  // Cache the result
  taskColorCache.set(taskId, color);
  return color;
};

// Function to get a title color based on task ID
const getTaskTitleColor = (taskId: number | string) => {
  // Check if we have this color in the cache
  if (taskTitleColorCache.has(taskId)) {
    return taskTitleColorCache.get(taskId);
  }

  // Convert string ID to number if needed
  const id = typeof taskId === 'string' ? parseInt(taskId, 10) : taskId;
  // Use modulo to cycle through the colors
  const color = taskTitleColors[id % taskTitleColors.length];

  // Cache the result
  taskTitleColorCache.set(taskId, color);
  return color;
};

interface TaskCardProps {
  task: Task;
  index: number;
  canEditProject: boolean;
  onTaskClick: (task: Task) => void;
}

const TaskCard: React.FC<TaskCardProps> = memo(({
  task,
  index,
  canEditProject,
  onTaskClick
}) => {
  return (
    <Draggable
      key={task.id}
      draggableId={`task-${task.id}`}
      index={index}
      isDragDisabled={!canEditProject} // Disable dragging for viewers
    >
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          onClick={() => onTaskClick(task)}
          className={cn(
            "p-3 border rounded-lg cursor-pointer transition-all shadow-sm",
            "hover:shadow-md hover:translate-y-[-2px]",
            snapshot.isDragging && "shadow-lg border-primary ring-2 ring-primary/20 rotate-1 z-10",
            !canEditProject && "cursor-default",
            getTaskCardColor(task.id)
          )}
          style={{
            ...provided.draggableProps.style,
          }}
        >
          {/* Drag Handle */}
          <div className="flex justify-end mb-1">
            {canEditProject && (
              <div {...provided.dragHandleProps} className="cursor-grab">
                <GripVertical size={14} className="text-muted-foreground hover:text-foreground dark:text-muted-foreground/80 dark:hover:text-foreground" />
              </div>
            )}
          </div>

          {/* Task Title with Priority Indicator next to text */}
          <div className="mb-2">
            <div className="inline-flex flex-wrap items-center">
              <h4 className={cn("font-medium text-sm", getTaskTitleColor(task.id))}>{task.title}</h4>
              {task.priority && (
                <div className="flex-shrink-0 ml-1.5">
                  <PriorityIndicator priority={task.priority} size="sm" />
                </div>
              )}
            </div>
          </div>

          {/* Task Description (optional) */}
          {task.description && (
            <p className="text-xs text-muted-foreground dark:text-muted-foreground/90 line-clamp-2 mb-3">
              {task.description}
            </p>
          )}

          {/* Task Status */}
          <div className="mt-3">
            {task.status !== undefined && (
              <StatusBadge
                status={
                  // Handle both numeric and string status values
                  task.status === 0 || task.status === '0' || task.status === 'todo' || task.status === 'not-started' ? 'todo' :
                  task.status === 1 || task.status === '1' || task.status === 'in-progress' ? 'in-progress' :
                  task.status === 2 || task.status === '2' || task.status === 'completed' ? 'completed' :
                  task.status === 'at-risk' ? 'at-risk' : 'todo'
                }
                size="sm"
                variant="pill"
              />
            )}
          </div>

          {/* Task Footer */}
          <div className="flex items-center justify-between mt-3 pt-2 border-t border-border dark:border-border/50">
            {/* Due Date (if available) */}
            {task.end_date && (
              <div className={cn(
                "text-xs px-2 py-1 rounded-md flex items-center",
                new Date(task.end_date) < new Date() &&
                task.status !== 2 &&
                task.status !== '2' &&
                task.status !== 'completed'
                  ? "bg-destructive/10 text-destructive dark:bg-destructive/20 dark:text-destructive-foreground"
                  : "bg-success/10 text-success dark:bg-success/20 dark:text-success-foreground"
              )}>
                <span className={cn(
                  "mr-1.5 h-2 w-2 rounded-full",
                  new Date(task.end_date) < new Date() &&
                  task.status !== 2 &&
                  task.status !== '2' &&
                  task.status !== 'completed'
                    ? "bg-destructive dark:bg-destructive" : "bg-success dark:bg-success"
                )}></span>
                Due: {new Date(task.end_date).toLocaleDateString()}
              </div>
            )}

            {/* Task Assignees */}
            {task.assignees && task.assignees.length > 0 && (
              <div className="flex items-center -space-x-2">
                {task.assignees.slice(0, 3).map(assignee => (
                  <div key={assignee.id} className="relative">
                    <Avatar
                      className={cn(
                        "h-6 w-6 border-2",
                        assignee.status === 'former_member' ? "border-warning" : "border-background"
                      )}
                    >
                      <AvatarImage
                        src={getFullImageUrl(assignee.profile_picture)}
                        alt={`${assignee.first_name} ${assignee.last_name}`}
                        className={assignee.status === 'former_member' ? 'opacity-70' : ''}
                      />
                      <AvatarFallback className="text-xs bg-primary/10 text-primary dark:bg-white/90 dark:text-primary">
                        {assignee.first_name?.[0]}{assignee.last_name?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    {assignee.status === 'former_member' && (
                      <span className="absolute -top-1 -right-1 h-2.5 w-2.5 rounded-full bg-warning border border-white dark:border-card"></span>
                    )}
                  </div>
                ))}

                {task.assignees.length > 3 && (
                  <div className="h-6 w-6 rounded-full bg-muted dark:bg-muted/70 border-2 border-background dark:border-card flex items-center justify-center text-xs text-muted-foreground dark:text-muted-foreground/90">
                    +{task.assignees.length - 3}
                  </div>
                )}

                {/* Former member indicator */}
                {task.assignees.some(a => a.status === 'former_member') && (
                  <div className="ml-2 h-5 flex items-center">
                    <span className="text-xs px-1.5 py-0.5 bg-warning text-warning-foreground font-medium rounded-md dark:bg-warning dark:text-warning-foreground dark:shadow-sm dark:shadow-warning/20">
                      Former
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </Draggable>
  );
});

TaskCard.displayName = 'TaskCard';

export default TaskCard;
