import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import { hasProjectPermission, ProjectPermission } from './permissionUtils';

/**
 * Check if a project is a personal project
 * @param project The project to check
 * @returns True if the project is a personal project, false otherwise
 */
export const isPersonalProject = (project: Project | undefined): boolean => {
  if (!project) return false;
  return !project.is_group_project;
};

/**
 * Check if a project is a group project
 * @param project The project to check
 * @returns True if the project is a group project, false otherwise
 */
export const isGroupProject = (project: Project | undefined): boolean => {
  if (!project) return false;
  return project.is_group_project;
};

/**
 * Check if a user is the owner of a project
 * @param project The project to check
 * @param user The user to check
 * @returns True if the user is the owner of the project, false otherwise
 */
export const isProjectOwner = (project: Project | undefined, user: User | null): boolean => {
  if (!project || !user) return false;
  return project.user_id === user.id;
};

/**
 * Check if a user can convert a project to a group project
 * @param project The project to check
 * @param user The user to check
 * @returns True if the user can convert the project, false otherwise
 */
export const canConvertToGroupProject = (project: Project | undefined, user: User | null): boolean => {
  if (!project || !user) return false;

  // Only the owner can convert a personal project to a group project
  return isPersonalProject(project) && isProjectOwner(project, user);
};

/**
 * Check if a user can convert a project to a personal project
 * @param project The project to check
 * @param user The user to check
 * @returns True if the user can convert the project, false otherwise
 */
export const canConvertToPersonalProject = (project: Project | undefined, user: User | null): boolean => {
  if (!project || !user) return false;

  // Only the owner can convert a group project to a personal project
  return isGroupProject(project) && isProjectOwner(project, user);
};

/**
 * Check if a user can add members to a project
 * @param project The project to check
 * @param user The user to check
 * @returns True if the user can add members, false otherwise
 */
export const canAddMembers = (project: Project | undefined, user: User | null): boolean => {
  if (!project || !user) return false;

  // For personal projects, only the owner can add members (which will convert it)
  if (isPersonalProject(project)) {
    return isProjectOwner(project, user);
  }

  // For group projects, check the add_member permission
  return hasProjectPermission(project, user, ProjectPermission.ADD_MEMBER);
};

/**
 * Get the project type label
 * @param project The project to get the label for
 * @returns A string label for the project type
 */
export const getProjectTypeLabel = (project: Project | undefined): string => {
  if (!project) return 'Unknown';
  return project.is_group_project ? 'Group Project' : 'Personal Project';
};

/**
 * Get the project type description
 * @param project The project to get the description for
 * @returns A string description of the project type
 */
export const getProjectTypeDescription = (project: Project | undefined): string => {
  if (!project) return '';

  if (project.is_group_project) {
    return 'This is a group project that can be shared with team members';
  } else {
    return 'This is a personal project only visible to you';
  }
};
