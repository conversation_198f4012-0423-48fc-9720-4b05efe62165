import { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { getLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import Loader from '@/components/Loader';
import { useAuth } from '@/context/AuthContext';
import { getWorkspaces } from '@/api/workspacesApi';
import { useToast } from '@/components/ui/use-toast';
import Navbar from '@/components/Navbar';
import AllProjectsSection from '@/components/AllProjectsSection';
import DashboardStats from '@/components/dashboard/DashboardStats';
import UpcomingDeadlines from '@/components/dashboard/UpcomingDeadlines';
import ActivityFeed from '@/components/activity/ActivityFeed';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';

/**
 * Dashboard component that shows all projects for a user
 */
const Dashboard = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [workspaces, setWorkspaces] = useState([]);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch workspaces for the sidebar
        const workspacesData = await getWorkspaces();
        setWorkspaces(workspacesData);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: "Error loading dashboard",
          description: "Could not load your data. Please try again.",
          variant: "destructive"
        });
        setIsLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchData();
    } else {
      // If not authenticated, redirect to login
      navigate('/login');
      setIsLoading(false);
    }
  }, [navigate, isAuthenticated, toast]);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back, {user?.first_name}! Here's an overview of your projects.</p>
        </div>

        {/* Project Statistics */}
        <DashboardStats />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Projects Section - Takes up 3/4 of the grid on large screens */}
          <div className="lg:col-span-3">
            <AllProjectsSection />
          </div>

          {/* Sidebar - Takes up 1/4 of the grid on large screens */}
          <div className="space-y-6">
            <UpcomingDeadlines />
            <div className="relative">
              <ActivityFeed limit={5} showFilters={false} />
              <div className="absolute bottom-4 right-4">
                <Button variant="ghost" size="sm" asChild className="text-xs gap-1 hover:bg-primary/10">
                  <Link to="/activity">
                    View all
                    <ExternalLink className="h-3 w-3" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
