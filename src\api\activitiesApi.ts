import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';
import { formatDistanceToNow } from 'date-fns';

// Activity types
export interface Activity {
  id: number;
  type: 'project_created' | 'task_completed' | 'user_joined' | 'comment_added';
  user: {
    id: number;
    name: string;
    avatar?: string;
  };
  target: string;
  target_id: number;
  created_at: string;
}

// Normalized activity for display
export interface ActivityItem {
  id: number;
  type: 'project_created' | 'task_completed' | 'user_joined' | 'comment_added';
  user: {
    id: number;
    name: string;
    avatar?: string;
  };
  target: string;
  timestamp: string;
}

// Activity filter interface
export interface ActivityFilter {
  limit?: number;
  type?: string;
  from_date?: string;
  to_date?: string;
  sort_order?: 'asc' | 'desc';
}

// Get recent activities
export const getRecentActivities = async (filters: ActivityFilter = {}): Promise<ActivityItem[]> => {
  try {
    // Set default limit if not provided
    const params = {
      limit: filters.limit || 20,
      ...filters
    };

    const response = await api.get('/activities', { params });

    // Transform the API response to match our frontend format
    return response.data.map((activity: Activity) => ({
      id: activity.id,
      type: activity.type,
      user: activity.user,
      target: activity.target,
      timestamp: formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })
    }));
  } catch (error) {
    // Log the error but return empty array to avoid breaking the UI
    console.error('Error fetching recent activities:', error);
    return [];
  }
};
