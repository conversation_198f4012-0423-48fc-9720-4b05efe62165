
import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback, useMemo } from 'react';
import { useAuthAPI } from '@/hooks/useAuthAPI';
import { getLocalStorageData, setLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import { User, normalizeUser } from '@/entities/User';
import { getFullImageUrl } from '@/utils/imageUtils';
import api from '@/api/api';





interface AuthContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string, redirectUrl?: string) => Promise<void>;
  requestForAccount: (firstName:string, lastName:string,email: string, password:string) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<User>;
  uploadProfilePicture: (file: File) => Promise<User>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [authChecked, setAuthChecked] = useState(false);
  const auth = useAuthAPI();

  // Initial authentication check
  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        console.log('Performing initial authentication check');

        // First check if we have valid token and user data in localStorage
        const token = getLocalStorageData('token');
        const storedUserData = getLocalStorageData('user');

        // Validate token format
        const isValidToken = token && typeof token === 'string' && token.trim() !== '';
        const isValidUserData = storedUserData && storedUserData.id;

        if (!isValidToken) {
          console.log('Invalid or missing token during initial auth check');
          setUser(null);
          setAuthChecked(true);
          return;
        }

        if (isValidUserData) {
          console.log('Using stored user data for authentication');
          setUser(normalizeUser(storedUserData));

          // Set CSRF token in meta tag
          const csrfMeta = document.querySelector('meta[name="csrf-token"]');
          if (csrfMeta) {
            csrfMeta.setAttribute('content', token.toString());
          }

          // Verify with server that token is still valid
          try {
            const response = await api.get('/user');
            if (response.data && response.data.id) {
              console.log('Token verified with server');
              setLocalStorageData('user', response.data);
              setUser(normalizeUser(response.data));
            }
          } catch (error) {
            console.error('Token verification failed:', error);
            // If we get a 401 or 403, the token is invalid
            if (error.response && (error.response.status === 401 || error.response.status === 403)) {
              console.log('Clearing invalid token');
              localStorage.removeItem('token');
              localStorage.removeItem('user');
              setUser(null);
            }
          }
        } else {
          // We have a token but no valid user data, try to fetch user data
          console.log('Token exists but no valid user data, fetching from API');
          try {
            const response = await api.get('/user');
            if (response.data && response.data.id) {
              console.log('Successfully fetched user data from API');
              setLocalStorageData('user', response.data);
              setUser(normalizeUser(response.data));

              // Set CSRF token in meta tag
              const csrfMeta = document.querySelector('meta[name="csrf-token"]');
              if (csrfMeta) {
                csrfMeta.setAttribute('content', token.toString());
              }
            } else {
              console.error('API returned invalid user data');
              localStorage.removeItem('token');
              localStorage.removeItem('user');
              setUser(null);
            }
          } catch (error) {
            console.error('Failed to fetch user data:', error);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Error during authentication check:', error);
        setUser(null);
      } finally {
        setAuthChecked(true);
      }
    };

    checkAuthentication();
  }, []);

  // Set up a refresh interval to periodically check authentication
  useEffect(() => {
    // Only set up the interval if the user is authenticated
    if (!user) return;

    console.log('Setting up authentication refresh interval');

    const refreshInterval = setInterval(async () => {
      // Silently refresh the user data
      try {
        console.log('Refreshing user authentication data');

        // Check if token exists before making the request
        const token = getLocalStorageData('token');
        if (!token || typeof token !== 'string' || token.trim() === '') {
          console.error('Invalid token found during refresh, clearing user data');
          setUser(null);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          return;
        }

        const response = await api.get('/user');

        if (response.data && response.data.id) {
          console.log('Successfully refreshed user data');
          setLocalStorageData('user', response.data);
          setUser(normalizeUser(response.data));

          // Update CSRF token
          const csrfMeta = document.querySelector('meta[name="csrf-token"]');
          if (csrfMeta && token) {
            csrfMeta.setAttribute('content', token.toString());
          }
        } else {
          console.error('API returned invalid user data during refresh');
          setUser(null);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
        }
      } catch (error) {
        console.error('Failed to refresh user data:', error);

        // If we get a 401 or 403, the token is invalid, so log the user out
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          console.log('Authentication error during refresh, clearing user data');
          setUser(null);
          localStorage.removeItem('token');
          localStorage.removeItem('user');

          // Clear CSRF token
          const csrfMeta = document.querySelector('meta[name="csrf-token"]');
          if (csrfMeta) {
            csrfMeta.setAttribute('content', '');
          }
        }
      }
    }, 5 * 60 * 1000); // Refresh every 5 minutes

    return () => {
      console.log('Clearing authentication refresh interval');
      clearInterval(refreshInterval);
    };
  }, [user]);

  const login = useCallback(async (email: string, password: string, redirectUrl?: string) => {
    const userData = await auth.login(email, password, redirectUrl);
    setUser(userData);

    // Set CSRF token in meta tag
    const token = localStorage.getItem('token');
    if (token) {
      const csrfMeta = document.querySelector('meta[name="csrf-token"]');
      if (csrfMeta) {
        csrfMeta.setAttribute('content', token);
      }
    }
  }, [auth]);

  const logout = useCallback(async () => {
    const success = await auth.logout();
    if (success) {
      setUser(null);

      // Clear CSRF token in meta tag
      const csrfMeta = document.querySelector('meta[name="csrf-token"]');
      if (csrfMeta) {
        csrfMeta.setAttribute('content', '');
      }
    }
  }, [auth]);

  const updateProfile = useCallback(async (userData: Partial<User>) => {
    if (!user) throw new Error('User not authenticated');

    const updatedUserData = await auth.updateProfile(userData);

    // Update the user state with the returned data
    const updatedUser = {
      ...user,
      ...updatedUserData
    };

    setUser(updatedUser);
    return updatedUser;
  }, [user, auth]);

  const uploadProfilePicture = useCallback(async (file: File) => {
    if (!user) throw new Error('User not authenticated');

    const updatedUserData = await auth.uploadProfilePicture(file);

    // Update the user state with the returned data
    const updatedUser = {
      ...user,
      ...updatedUserData
    };

    setUser(updatedUser);
    return updatedUser;
  }, [user, auth]);

  const isLoggedIn = useCallback((): boolean => {
    const token = getLocalStorageData('token');
    const userData = getLocalStorageData('user');

    // Check both token and user data exist
    if (!token || !userData || !userData.id) {
      return false;
    }

    try {
      // Validate token format (should be a string)
      if (typeof token !== 'string' || token.trim() === '') {
        console.error('Invalid token format');
        return false;
      }

      // Additional validation could be added here

      return true;
    } catch (error) {
      console.error('Error validating authentication:', error);
      return false;
    }
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    user,
    setUser,
    isLoading: auth.isLoading,
    isAuthenticated: isLoggedIn(),
    login,
    requestForAccount: auth.requestForAccount,
    logout,
    updateProfile,
    uploadProfilePicture
  }), [
    user,
    auth.isLoading,
    isLoggedIn,
    login,
    auth.requestForAccount,
    logout,
    updateProfile,
    uploadProfilePicture
  ]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
