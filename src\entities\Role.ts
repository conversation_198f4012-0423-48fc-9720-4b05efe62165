export interface Role {
    id: number,
    name: string,
    notation: string,
    description: string | null,
    permissions: any,
}


export function normalizeRole(data: any): Role {
    return {
        id: Number(data.id),
        name: String(data.name),
        notation: String(data.notation),
        description: data.description ?? null,
        permissions: parsePermissions(data.permissions),
    };
}

function parsePermissions(raw: any): any {
    try {
        if (typeof raw === 'string') {
            return JSON.parse(raw);
        }
        return raw;
    } catch {
        return null;
    }
}