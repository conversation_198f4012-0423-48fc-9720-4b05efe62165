import React, { useState } from 'react';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import {
  isGroupProject,
  isPersonalProject,
  canConvertToGroupProject,
  canConvertToPersonalProject
} from '@/utils/projectTypeUtils';
import { convertToGroupProject, convertToPersonalProject } from '@/api/projectsApi';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { User as UserIcon, Users, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

interface ProjectTypeSelectorProps {
  project: Project;
  currentUser: User | null;
  onProjectUpdated?: (updatedProject: Project) => void;
}

/**
 * A component that allows users to select and convert between project types
 */
const ProjectTypeSelector: React.FC<ProjectTypeSelectorProps> = ({
  project,
  currentUser,
  onProjectUpdated,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [conversionType, setConversionType] = useState<'toGroup' | 'toPersonal' | null>(null);

  const isGroup = isGroupProject(project);
  const isPersonal = isPersonalProject(project);
  const canConvertToGroup = canConvertToGroupProject(project, currentUser);
  const canConvertToPersonal = canConvertToPersonalProject(project, currentUser);

  const handleConvertToGroup = async () => {
    if (!canConvertToGroup) return;

    setIsLoading(true);
    setConversionType('toGroup');
    try {
      const response = await convertToGroupProject(project.id);

      if (response && onProjectUpdated) {
        onProjectUpdated({
          ...project,
          is_group_project: true
        });
      }

      toast.success('Project converted to a group project');
    } catch (error) {
      console.error('Failed to convert project:', error);
      toast.error('Failed to convert project');
    } finally {
      setIsLoading(false);
      setConversionType(null);
    }
  };

  const handleConvertToPersonal = async () => {
    if (!canConvertToPersonal) return;

    setIsLoading(true);
    setConversionType('toPersonal');
    try {
      const response = await convertToPersonalProject(project.id);

      if (response && onProjectUpdated) {
        onProjectUpdated({
          ...project,
          is_group_project: false
        });
      }

      toast.success('Project converted to a personal project');
    } catch (error: any) {
      console.error('Failed to convert project:', error);

      // Check if there's a specific error message from the backend
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Failed to convert project');
      }
    } finally {
      setIsLoading(false);
      setConversionType(null);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Personal Project Card */}
      <Card className={`border-2 ${isPersonal ? 'border-primary' : 'border-transparent'}`}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserIcon className="mr-2" size={18} />
            Personal Project
          </CardTitle>
          <CardDescription>
            Only visible to you
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="list-disc list-inside text-sm space-y-1">
            <li>Full control over the project</li>
            <li>No sharing capabilities</li>
            <li>Simplified interface</li>
            <li>Can be converted to a group project later</li>
          </ul>
        </CardContent>
        <CardFooter>
          {isPersonal ? (
            <Button variant="outline" disabled className="w-full">
              Current Type
            </Button>
          ) : canConvertToPersonal ? (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="default" className="w-full">
                  Convert to Personal Project
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center">
                    <AlertTriangle className="mr-2 text-amber-500" size={18} />
                    Convert to Personal Project?
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    This will convert the project back to a personal project.
                    <br /><br />
                    Note that:
                    <ul className="list-disc list-inside mt-2">
                      <li>All project members will be <strong>automatically removed</strong></li>
                      <li>Only you will have access to this project</li>
                      <li>You can convert it back to a group project later if needed</li>
                    </ul>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleConvertToPersonal}
                    disabled={isLoading && conversionType === 'toPersonal'}
                  >
                    {isLoading && conversionType === 'toPersonal' ? 'Converting...' : 'Convert'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          ) : (
            <Button variant="outline" disabled className="w-full">
              {isGroup ? "Only the owner can convert" : "Cannot convert"}
            </Button>
          )}
        </CardFooter>
      </Card>

      {/* Group Project Card */}
      <Card className={`border-2 ${isGroup ? 'border-primary' : 'border-transparent'}`}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2" size={18} />
            Group Project
          </CardTitle>
          <CardDescription>
            Can be shared with team members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="list-disc list-inside text-sm space-y-1">
            <li>Invite team members to collaborate</li>
            <li>Assign different roles (Admin, Editor, Viewer)</li>
            <li>Track who made changes</li>
            <li>Can be converted back to personal if no other members</li>
          </ul>
        </CardContent>
        <CardFooter>
          {isGroup ? (
            <Button variant="outline" disabled className="w-full">
              Current Type
            </Button>
          ) : canConvertToGroup ? (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="default" className="w-full">
                  Convert to Group Project
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center">
                    <AlertTriangle className="mr-2 text-amber-500" size={18} />
                    Convert to Group Project?
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to convert this to a group project?
                    <br /><br />
                    Group projects allow you to:
                    <ul className="list-disc list-inside mt-2">
                      <li>Invite team members to collaborate</li>
                      <li>Assign different roles (Admin, Editor, Viewer)</li>
                      <li>Track who made changes</li>
                    </ul>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleConvertToGroup}
                    disabled={isLoading && conversionType === 'toGroup'}
                  >
                    {isLoading && conversionType === 'toGroup' ? 'Converting...' : 'Convert'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          ) : (
            <Button variant="outline" disabled className="w-full">
              Only the owner can convert
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default ProjectTypeSelector;
