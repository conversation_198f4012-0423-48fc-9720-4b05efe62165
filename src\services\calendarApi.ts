import { api } from './api';
import { CalendarEvent, CalendarEventFormData, DeleteType, UpdateType } from '../types/calendar';

// Get all calendar events
export const getCalendarEvents = async (startDate?: string, endDate?: string) => {
  let url = '/calendar-events';
  const params = new URLSearchParams();

  if (startDate) params.append('start_date', startDate);
  if (endDate) params.append('end_date', endDate);

  if (params.toString()) {
    url += `?${params.toString()}`;
  }

  const response = await api.get(url);
  return response.data;
};

// Get a specific calendar event
export const getCalendarEvent = async (id: string | number) => {
  const response = await api.get(`/calendar-events/${id}`);
  return response.data;
};

// Create a new calendar event
export const createCalendarEvent = async (eventData: CalendarEventFormData) => {
  try {
    console.log('Creating calendar event with data:', eventData);
    const response = await api.post('/calendar-events', eventData);
    console.log('Calendar event created successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error creating calendar event:', error);
    throw error;
  }
};

// Update a calendar event
export const updateCalendarEvent = async (id: string | number, eventData: Partial<CalendarEventFormData>) => {
  const response = await api.put(`/calendar-events/${id}`, eventData);
  return response.data;
};

// Delete a calendar event
export const deleteCalendarEvent = async (id: string | number, deleteType?: DeleteType) => {
  const params = deleteType ? { delete_type: deleteType } : {};
  const response = await api.delete(`/calendar-events/${id}`, { params });
  return response.data;
};

// Reschedule a calendar event
export const rescheduleEvent = async (id: string | number, startDate: string, endDate?: string) => {
  const response = await api.put(`/calendar-events/${id}/reschedule`, {
    start_date: startDate,
    end_date: endDate
  });
  return response.data;
};

// Get upcoming events
export const getUpcomingEvents = async () => {
  const response = await api.get('/upcoming-events');
  return response.data;
};

// Get recurring event instances for a specific date range
export const getRecurringInstances = async (id: string | number, startDate: string, endDate: string) => {
  const response = await api.get(`/calendar-events/${id}/recurring-instances`, {
    params: {
      start_date: startDate,
      end_date: endDate
    }
  });
  return response.data;
};

// Update a recurring event instance
export const updateRecurringInstance = async (
  id: string | number,
  eventData: Partial<CalendarEventFormData>,
  updateType: UpdateType = 'this'
) => {
  const data = {
    ...eventData,
    update_type: updateType
  };

  const response = await api.put(`/calendar-events/${id}`, data);
  return response.data;
};

// Delete a recurring event instance
export const deleteRecurringInstance = async (
  id: string | number,
  deleteType: DeleteType = 'this'
) => {
  const response = await api.delete(`/calendar-events/${id}`, {
    params: {
      delete_type: deleteType
    }
  });
  return response.data;
};
