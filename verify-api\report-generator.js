
const fs = require('fs');

/**
 * Generate a Markdown report
 * @param {Array} results Verification results
 * @returns {string} Markdown report
 */
function generateReport(results) {
  let report = '# API Verification Report\n\n';
  
  report += '| Endpoint | Method | Defined in Backend? | Status Code | Schema Match? | Issue | Fix Suggestion |\n';
  report += '|----------|--------|-------------------|------------|--------------|-------|----------------|\n';
  
  for (const result of results) {
    const backendStatus = result.existsInBackend ? '✅' : '❌';
    const statusClass = result.response.success ? '✅' : '❌';
    const schemaClass = result.schemaValid ? '✅' : '❌';
    
    report += `| ${result.url} | ${result.method} | ${backendStatus} | ${statusClass} ${result.response.status} | ${schemaClass} | ${result.issue || '-'} | ${result.fixSuggestion || '-'} |\n`;
  }
  
  // Add summary
  const totalEndpoints = results.length;
  const backendMissingCount = results.filter(r => !r.existsInBackend).length;
  const failedRequestsCount = results.filter(r => !r.response.success).length;
  const schemaMismatchCount = results.filter(r => !r.schemaValid).length;
  
  report += '\n## Summary\n\n';
  report += `- Total Endpoints: ${totalEndpoints}\n`;
  report += `- Missing in Backend: ${backendMissingCount}\n`;
  report += `- Failed Requests: ${failedRequestsCount}\n`;
  report += `- Schema Mismatches: ${schemaMismatchCount}\n\n`;
  
  const healthStatus = backendMissingCount === 0 && failedRequestsCount === 0 && schemaMismatchCount === 0
    ? '✅ All connected and working properly'
    : '❌ Issues found that need attention';
  
  report += `**Overall Integration Health:** ${healthStatus}\n\n`;
  
  // Add actionable steps
  if (backendMissingCount > 0 || failedRequestsCount > 0 || schemaMismatchCount > 0) {
    report += '## Actionable Next Steps\n\n';
    
    if (backendMissingCount > 0) {
      report += '### Missing Endpoints\n\n';
      report += 'Add the following routes to your Laravel api.php file:\n\n';
      
      const missingEndpoints = results.filter(r => !r.existsInBackend);
      for (const endpoint of missingEndpoints) {
        report += `\`\`\`php\nRoute::${endpoint.method.toLowerCase()}('${endpoint.url}', [YourController::class, 'yourMethod']);\n\`\`\`\n\n`;
      }
    }
    
    if (schemaMismatchCount > 0) {
      report += '### Schema Mismatches\n\n';
      report += 'Update your TypeScript interfaces or Laravel resource classes to match these schemas:\n\n';
      
      const mismatchEndpoints = results.filter(r => !r.schemaValid);
      for (const endpoint of mismatchEndpoints) {
        report += `**${endpoint.method} ${endpoint.url}**\n\n`;
        report += 'Expected schema:\n\n';
        
        if (endpoint.expectedSchema) {
          report += `\`\`\`json\n${JSON.stringify(endpoint.expectedSchema, null, 2)}\n\`\`\`\n\n`;
        } else {
          report += 'No schema available\n\n';
        }
        
        report += 'Actual response:\n\n';
        report += `\`\`\`json\n${JSON.stringify(endpoint.response.data, null, 2)}\n\`\`\`\n\n`;
      }
    }
  }
  
  return report;
}

/**
 * Save the report to a file
 * @param {string} report Markdown report content
 * @param {string} filePath Path to save the report
 */
function saveReport(report, filePath) {
  fs.writeFileSync(filePath, report);
  console.log(`Report saved to ${filePath}`);
}

module.exports = {
  generateReport,
  saveReport
};
