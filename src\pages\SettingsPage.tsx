
import React, { useState, useEffect } from 'react';
import Navbar from '../components/Navbar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { Lock, Globe, Moon, Sun } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { useLanguage } from '@/context/LanguageContext';
import { useTranslation } from 'react-i18next';
import { getNotificationPreferences, updateNotificationPreferences } from '@/api/notificationPreferencesApi';
import { useAuthAPI } from '@/hooks/useAuthAPI';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';

const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const { isDarkMode, toggleDarkMode } = useTheme();
  const { language, setLanguage } = useLanguage();
  const { t } = useTranslation();

  const handleThemeToggle = () => {
    toggleDarkMode();
    const newMode = isDarkMode ? 'light' : 'dark';
    toast({
      title: t(`settings.appearance.${newMode}ModeActivated`, `${isDarkMode ? 'Light' : 'Dark'} mode activated`),
      description: t('settings.appearance.themeChanged', 'The application theme has been changed to {{mode}} mode.', { mode: newMode }),
    });
  };

  const [emailNotifications, setEmailNotifications] = useState({
    comments: true,
    projects: true,
    newsletter: false
  });

  const [inAppNotifications, setInAppNotifications] = useState({
    taskAssigned: true,
    taskDue: true,
    projectActivity: true
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleSaveNotifications = async () => {
    setIsLoading(true);
    try {
      // Save to backend
      await updateNotificationPreferences({
        email_comments: emailNotifications.comments,
        email_projects: emailNotifications.projects,
        email_newsletter: emailNotifications.newsletter,
        in_app_task_assigned: inAppNotifications.taskAssigned,
        in_app_task_due: inAppNotifications.taskDue,
        in_app_project_activity: inAppNotifications.projectActivity
      });

      toast({
        title: "Notification settings saved",
        description: "Your notification preferences have been updated.",
      });
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast({
        title: "Error saving settings",
        description: "There was a problem saving your notification preferences.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load notification preferences from backend
  useEffect(() => {
    const fetchNotificationPreferences = async () => {
      try {
        const preferences = await getNotificationPreferences();

        setEmailNotifications({
          comments: preferences.email_comments,
          projects: preferences.email_projects,
          newsletter: preferences.email_newsletter
        });

        setInAppNotifications({
          taskAssigned: preferences.in_app_task_assigned,
          taskDue: preferences.in_app_task_due,
          projectActivity: preferences.in_app_project_activity
        });
      } catch (error) {
        console.error('Error fetching notification preferences:', error);
        // Fallback to localStorage if API fails
        const savedEmailSettings = localStorage.getItem('emailNotifications');
        const savedInAppSettings = localStorage.getItem('inAppNotifications');

        if (savedEmailSettings) {
          try {
            setEmailNotifications(JSON.parse(savedEmailSettings));
          } catch (e) {
            console.error('Error parsing saved email notification settings:', e);
          }
        }

        if (savedInAppSettings) {
          try {
            setInAppNotifications(JSON.parse(savedInAppSettings));
          } catch (e) {
            console.error('Error parsing saved in-app notification settings:', e);
          }
        }
      }
    };

    fetchNotificationPreferences();
  }, []);

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [passwordErrors, setPasswordErrors] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);
  const [deleteAccountPassword, setDeleteAccountPassword] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');

  const { updatePassword, deleteAccount } = useAuthAPI();

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordForm(prev => ({ ...prev, [field]: value }));
    setPasswordErrors(prev => ({ ...prev, [field]: '' }));
  };

  const validatePasswordForm = () => {
    const errors = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
    let isValid = true;

    if (!passwordForm.currentPassword) {
      errors.currentPassword = 'Current password is required';
      isValid = false;
    }

    if (!passwordForm.newPassword) {
      errors.newPassword = 'New password is required';
      isValid = false;
    } else if (passwordForm.newPassword.length < 8) {
      errors.newPassword = 'Password must be at least 8 characters';
      isValid = false;
    }

    if (!passwordForm.confirmPassword) {
      errors.confirmPassword = 'Please confirm your new password';
      isValid = false;
    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    setPasswordErrors(errors);
    return isValid;
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePasswordForm()) {
      return;
    }

    try {
      setIsChangingPassword(true);
      await updatePassword(
        passwordForm.currentPassword,
        passwordForm.newPassword,
        passwordForm.confirmPassword
      );

      // Reset form after successful update
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      console.error('Failed to update password:', error);
      // Error handling is done in the updatePassword function
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleDeleteAccount = async () => {
    // Verify the confirmation text
    if (deleteConfirmText !== 'delete my account') {
      toast({
        variant: "destructive",
        title: "Confirmation failed",
        description: "Please type 'delete my account' to confirm deletion",
      });
      return;
    }

    // Verify password is provided
    if (!deleteAccountPassword) {
      toast({
        variant: "destructive",
        title: "Password required",
        description: "Please enter your password to confirm account deletion",
      });
      return;
    }

    try {
      setIsDeletingAccount(true);
      await deleteAccount(deleteAccountPassword);
      // The deleteAccount function handles navigation and toast notifications
    } catch (error) {
      console.error('Failed to delete account:', error);
      // Error handling is done in the deleteAccount function
    } finally {
      setIsDeletingAccount(false);
      setDeleteDialogOpen(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-foreground">{t('settings.title')}</h1>
          <p className="text-muted-foreground mt-1">
            {t('settings.subtitle')}
          </p>
        </div>

        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid grid-cols-3 md:w-[400px]">
            <TabsTrigger value="general">{t('settings.tabs.general')}</TabsTrigger>
            <TabsTrigger value="notifications">{t('settings.tabs.notifications')}</TabsTrigger>
            <TabsTrigger value="security">{t('settings.tabs.security')}</TabsTrigger>
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('settings.appearance.title')}</CardTitle>
                <CardDescription>
                  {t('settings.appearance.subtitle')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {isDarkMode ? (
                      <Moon className="h-5 w-5" />
                    ) : (
                      <Sun className="h-5 w-5" />
                    )}
                    <Label htmlFor="dark-mode">{t('settings.appearance.darkMode')}</Label>
                  </div>
                  <Switch
                    id="dark-mode"
                    checked={isDarkMode}
                    onCheckedChange={handleThemeToggle}
                  />
                </div>


                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Globe className="h-5 w-5" />
                    <Label htmlFor="language">{t('settings.appearance.language')}</Label>
                  </div>
                  <select
                    id="language"
                    className="form-select rounded-md border-input bg-background px-3 py-1"
                    value={language}
                    onChange={(e) => {
                      setLanguage(e.target.value as 'en' | 'es' | 'fr' | 'tr');
                      const languageName = e.target.options[e.target.selectedIndex].text;
                      toast({
                        title: t('settings.languages.changed', 'Language changed'),
                        description: t('settings.languages.changedDescription', 'The application language has been changed to {{language}}', { language: languageName }),
                      });
                    }}
                  >
                    <option value="en">{t('settings.languages.english')}</option>
                    <option value="es">{t('settings.languages.spanish')}</option>
                    <option value="fr">{t('settings.languages.french')}</option>
                    <option value="tr">{t('settings.languages.turkish')}</option>
                  </select>
                </div>
              </CardContent>
            </Card>


          </TabsContent>

          {/* Notification Settings */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Email Notifications</CardTitle>
                <CardDescription>
                  Choose what types of emails you receive
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email-comments" className="font-medium">Comments and Mentions</Label>
                    <p className="text-sm text-muted-foreground">Get notified when someone mentions you or comments on your projects</p>
                  </div>
                  <Switch
                    id="email-comments"
                    checked={emailNotifications.comments}
                    onCheckedChange={(checked) => setEmailNotifications(prev => ({ ...prev, comments: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email-projects" className="font-medium">Project Updates</Label>
                    <p className="text-sm text-muted-foreground">Get notified about project status changes and milestones</p>
                  </div>
                  <Switch
                    id="email-projects"
                    checked={emailNotifications.projects}
                    onCheckedChange={(checked) => setEmailNotifications(prev => ({ ...prev, projects: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email-newsletter" className="font-medium">Newsletter and Updates</Label>
                    <p className="text-sm text-muted-foreground">Receive product updates and newsletters</p>
                  </div>
                  <Switch
                    id="email-newsletter"
                    checked={emailNotifications.newsletter}
                    onCheckedChange={(checked) => setEmailNotifications(prev => ({ ...prev, newsletter: checked }))}
                  />
                </div>

                <Button
                  onClick={handleSaveNotifications}
                  className="mt-2"
                  disabled={isLoading}
                >
                  {isLoading ? "Saving..." : "Save Notification Preferences"}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>In-App Notifications</CardTitle>
                <CardDescription>
                  Configure your in-app notification settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="notify-task-assigned">Task Assigned</Label>
                  <Switch
                    id="notify-task-assigned"
                    checked={inAppNotifications.taskAssigned}
                    onCheckedChange={(checked) => setInAppNotifications(prev => ({ ...prev, taskAssigned: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="notify-task-due">Task Due Soon</Label>
                  <Switch
                    id="notify-task-due"
                    checked={inAppNotifications.taskDue}
                    onCheckedChange={(checked) => setInAppNotifications(prev => ({ ...prev, taskDue: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="notify-project-activity">Project Activity</Label>
                  <Switch
                    id="notify-project-activity"
                    checked={inAppNotifications.projectActivity}
                    onCheckedChange={(checked) => setInAppNotifications(prev => ({ ...prev, projectActivity: checked }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Settings */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Password</CardTitle>
                <CardDescription>
                  Update your password
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-4" onSubmit={handleChangePassword}>
                  <div className="space-y-2">
                    <Label htmlFor="current-password">Current Password</Label>
                    <div className="relative">
                      <input
                        id="current-password"
                        type="password"
                        className={`w-full rounded-md border ${passwordErrors.currentPassword ? 'border-destructive' : 'border-input'} bg-background px-3 py-2`}
                        placeholder="••••••••"
                        value={passwordForm.currentPassword}
                        onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                      />
                      <Lock className="absolute right-3 top-2 h-4 w-4 text-muted-foreground" />
                    </div>
                    {passwordErrors.currentPassword && (
                      <p className="text-destructive text-xs mt-1">{passwordErrors.currentPassword}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <div className="relative">
                      <input
                        id="new-password"
                        type="password"
                        className={`w-full rounded-md border ${passwordErrors.newPassword ? 'border-destructive' : 'border-input'} bg-background px-3 py-2`}
                        placeholder="••••••••"
                        value={passwordForm.newPassword}
                        onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                      />
                      <Lock className="absolute right-3 top-2 h-4 w-4 text-muted-foreground" />
                    </div>
                    {passwordErrors.newPassword && (
                      <p className="text-destructive text-xs mt-1">{passwordErrors.newPassword}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <div className="relative">
                      <input
                        id="confirm-password"
                        type="password"
                        className={`w-full rounded-md border ${passwordErrors.confirmPassword ? 'border-destructive' : 'border-input'} bg-background px-3 py-2`}
                        placeholder="••••••••"
                        value={passwordForm.confirmPassword}
                        onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                      />
                      <Lock className="absolute right-3 top-2 h-4 w-4 text-muted-foreground" />
                    </div>
                    {passwordErrors.confirmPassword && (
                      <p className="text-destructive text-xs mt-1">{passwordErrors.confirmPassword}</p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="mt-2"
                    disabled={isChangingPassword}
                  >
                    {isChangingPassword ? (
                      <>
                        <span className="mr-2">Updating...</span>
                        <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </>
                    ) : (
                      "Update Password"
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-destructive flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Delete Account
                </CardTitle>
                <CardDescription>
                  Permanently delete your account and all associated data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  This action is irreversible. Once you delete your account, all your data including workspaces, projects, and tasks will be permanently removed.
                </p>

                <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="destructive">
                      Delete Account
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle className="text-destructive flex items-center">
                        <AlertTriangle className="h-5 w-5 mr-2" />
                        Delete Account Permanently
                      </DialogTitle>
                      <DialogDescription>
                        This action cannot be undone. All your data will be permanently deleted.
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="delete-password">Enter your password</Label>
                        <input
                          id="delete-password"
                          type="password"
                          className="w-full rounded-md border border-input bg-background px-3 py-2"
                          placeholder="Your current password"
                          value={deleteAccountPassword}
                          onChange={(e) => setDeleteAccountPassword(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="delete-confirm">
                          Type <span className="font-semibold">delete my account</span> to confirm
                        </Label>
                        <input
                          id="delete-confirm"
                          type="text"
                          className="w-full rounded-md border border-input bg-background px-3 py-2"
                          placeholder="delete my account"
                          value={deleteConfirmText}
                          onChange={(e) => setDeleteConfirmText(e.target.value)}
                        />
                      </div>
                    </div>

                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setDeleteDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleDeleteAccount}
                        disabled={isDeletingAccount}
                      >
                        {isDeletingAccount ? (
                          <>
                            <span className="mr-2">Deleting...</span>
                            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          </>
                        ) : (
                          "Delete Account"
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default SettingsPage;
