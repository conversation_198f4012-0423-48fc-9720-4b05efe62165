import React from 'react';
import { Project } from '@/entities/Project';
import { isGroupProject, isPersonalProject } from '@/utils/projectTypeUtils';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { User, Users } from 'lucide-react';

interface ProjectTypeIndicatorProps {
  project: Project;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * A component that displays an indicator for the project type (personal or group)
 */
const ProjectTypeIndicator: React.FC<ProjectTypeIndicatorProps> = ({
  project,
  showLabel = false,
  size = 'md',
  className = '',
}) => {
  const isGroup = isGroupProject(project);
  const isPersonal = isPersonalProject(project);

  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16,
  };

  const badgeClasses = {
    sm: 'px-1.5 py-0 text-[10px]',
    md: 'px-2 py-0.5 text-xs',
    lg: 'px-2.5 py-1 text-sm',
  };

  const iconSize = iconSizes[size];
  const badgeClass = badgeClasses[size];

  if (isGroup) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="secondary"
              className={`${badgeClass} ${className} bg-primary/10 text-primary hover:bg-primary/20 dark:bg-primary/20 dark:text-primary-foreground dark:hover:bg-primary/30 dark:shadow-sm dark:shadow-primary/10`}
            >
              <Users size={iconSize} className="mr-1 dark:text-primary-foreground" />
              {showLabel && 'Group'}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>Group Project - Can be shared with team members</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (isPersonal) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="secondary"
              className={`${badgeClass} ${className} bg-muted text-muted-foreground hover:bg-muted/80 dark:bg-muted/70 dark:text-muted-foreground/90 dark:hover:bg-muted/60 dark:shadow-sm dark:shadow-muted/10`}
            >
              <User size={iconSize} className="mr-1 dark:text-foreground/80" />
              {showLabel && 'Personal'}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>Personal Project - Only visible to you</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return null;
};

export default ProjectTypeIndicator;
