# Profile Pictures Directory

This directory contains profile pictures for the static version of the application.

For a production deployment, you should replace these placeholder images with actual user profile pictures.

## Mock User Profile Pictures

- `john-doe.jpg` - Profile picture for <PERSON> (user ID: 1)
- `jane-smith.jpg` - Profile picture for <PERSON> (user ID: 2)
- `mike-johnson.jpg` - Profile picture for <PERSON> (user ID: 3)

## Default Avatar

If a user doesn't have a profile picture, the application will use the default avatar from `/placeholder-avatar.png`.
