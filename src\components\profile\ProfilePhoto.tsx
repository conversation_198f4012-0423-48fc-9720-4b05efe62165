
import React, { useRef, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, Loader, ImageIcon } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { getFullImageUrl, compressImage, ImageSize } from '@/utils/imageUtils';
import { LazyImage } from '@/components/ui/lazy-image';

interface ProfilePhotoProps {
  firstName?: string;
  lastName?: string;
  profilePicture?: string;
}

const ProfilePhoto: React.FC<ProfilePhotoProps> = ({ firstName, lastName, profilePicture }) => {
  const { uploadProfilePicture, user } = useAuth();
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(getFullImageUrl(profilePicture));
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isAuthenticated = !!user;

  const handleAvatarUpload = () => {
    // Trigger the hidden file input
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please select an image file (JPEG, PNG, etc.)",
      });
      return;
    }

    // Check file size (max 10MB before compression)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: "Please select an image smaller than 10MB",
      });
      return;
    }

    // Create a preview URL
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Upload the file to the server
    setIsUploading(true);

    try {
      // Compress the image before uploading
      const compressedBlob = await compressImage(file, 500); // Compress to max 500KB

      // Convert blob to File object
      const compressedFile = new File([compressedBlob], file.name, {
        type: file.type,
        lastModified: new Date().getTime()
      });

      console.log(`Original size: ${(file.size / 1024).toFixed(2)}KB, Compressed size: ${(compressedFile.size / 1024).toFixed(2)}KB`);

      // Use the dedicated profile picture upload function
      await uploadProfilePicture(compressedFile);

      // Toast notification is shown by the uploadProfilePicture function
    } catch (error) {
      console.error('Error processing or uploading profile picture:', error);
      // Reset preview on error
      setPreviewUrl(getFullImageUrl(profilePicture));

      toast({
        variant: "destructive",
        title: "Upload failed",
        description: "There was a problem uploading your profile picture. Please try again.",
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Authentication check is done at the top of the component

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Photo</CardTitle>
        <CardDescription>
          Update your profile picture
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center space-y-4">
        <Avatar className="h-24 w-24">
          {previewUrl ? (
            <LazyImage
              src={previewUrl}
              alt={`${firstName} ${lastName}`}
              size={ImageSize.SMALL}
              className="h-full w-full object-cover"
              fallback={
                <AvatarFallback className="text-2xl">
                  {firstName?.charAt(0)}{lastName?.charAt(0)}
                </AvatarFallback>
              }
            />
          ) : (
            <AvatarFallback className="text-2xl">
              {firstName?.charAt(0)}{lastName?.charAt(0)}
            </AvatarFallback>
          )}
        </Avatar>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          className="hidden"
        />
        {isAuthenticated ? (
          <Button
            onClick={handleAvatarUpload}
            variant="outline"
            className="w-full"
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload Photo
              </>
            )}
          </Button>
        ) : (
          <Button
            variant="outline"
            className="w-full"
            disabled={true}
            title="Please log in to upload a profile picture"
          >
            <Upload className="mr-2 h-4 w-4" />
            Log in to upload
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default ProfilePhoto;
