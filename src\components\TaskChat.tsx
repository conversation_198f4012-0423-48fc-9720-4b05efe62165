import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Chat, getTaskChats, createChat, deleteChat } from '@/api/chatsApi';
import { Task } from '@/api/tasksApi';
import { useAuth } from '@/context/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useEcho } from '@/utils/useEcho';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import MentionInput from '@/components/MentionInput';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Send,
  Trash2
} from 'lucide-react';
import { format } from 'date-fns';
import { formatMessageWithMentions } from '@/utils/mentionUtils.tsx';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import { Project } from '@/entities/Project';
import { getFullImageUrl } from '@/utils/imageUtils';

interface TaskChatProps {
  task: Task;
  project: Project | undefined;
  message?: string;
  setMessage?: (message: string) => void;
  handleSendMessage?: () => void;
  canAddComments?: boolean;
  highlightCommentId?: string | null;
}

const TaskChat: React.FC<TaskChatProps> = ({
  task,
  project,
  message: externalMessage,
  setMessage: externalSetMessage,
  handleSendMessage: externalHandleSendMessage,
  canAddComments: externalCanAddComments,
  highlightCommentId
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const commentRefs = useRef<{[key: number]: React.RefObject<HTMLDivElement>}>({});
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const commentIdFromUrl = searchParams.get('comment');

  const [internalMessage, setInternalMessage] = useState('');

  // Use external state if provided, otherwise use internal state
  const message = externalMessage !== undefined ? externalMessage : internalMessage;
  const setMessage = externalSetMessage || setInternalMessage;

  // Check permissions - all users including viewers should be able to comment
  const internalCanAddComments = hasProjectPermission(project, user, ProjectPermission.ADD_COMMENT);
  const canAddComments = externalCanAddComments !== undefined ? externalCanAddComments : internalCanAddComments;

  // Fetch chat messages
  const { data: chats = [], isLoading } = useQuery({
    queryKey: ['chats', task.id],
    queryFn: () => getTaskChats(task.id),
    enabled: !!task.id
  });

  // Create chat message mutation
  const createChatMutation = useMutation({
    mutationFn: createChat,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats', task.id] });
      setMessage('');
    },
    onError: (error) => {
      toast({
        title: "Failed to send message",
        description: "There was an error sending your message. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Delete chat message mutation
  const deleteChatMutation = useMutation({
    mutationFn: deleteChat,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats', task.id] });
      toast({
        title: "Message deleted",
        description: "Your message has been deleted."
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to delete message",
        description: "There was an error deleting your message. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Initialize refs for each chat message
  useEffect(() => {
    if (chats && chats.length > 0) {
      // Create refs for each chat message
      chats.forEach(chat => {
        if (!commentRefs.current[chat.id]) {
          commentRefs.current[chat.id] = React.createRef();
        }
      });
    }
  }, [chats]);

  // Scroll to the highlighted comment or to the bottom when new messages arrive
  useEffect(() => {
    // If there's a comment ID in the URL, scroll to that comment
    if (commentIdFromUrl && chats && chats.length > 0) {
      const commentId = parseInt(commentIdFromUrl);
      const commentRef = commentRefs.current[commentId];

      if (commentRef && commentRef.current) {
        setTimeout(() => {
          commentRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
          // Add a highlight effect
          if (commentRef.current) {
            commentRef.current.classList.add('bg-primary/10');
            commentRef.current.classList.add('animate-pulse-once');

            // Remove the highlight after a few seconds
            setTimeout(() => {
              if (commentRef.current) {
                commentRef.current.classList.remove('bg-primary/10');
                commentRef.current.classList.remove('animate-pulse-once');
              }
            }, 3000);
          }
        }, 300);
      }
    } else {
      // Otherwise, scroll to the bottom
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [chats, commentIdFromUrl]);

  // Set up real-time chat message listener
  const echo = useEcho();
  useEffect(() => {
    if (!echo || !task.id) return;

    try {
      // Listen for new chat messages on the task channel
      console.log(`Subscribing to task.${task.id} channel`);
      const channel = echo.private(`task.${task.id}`);

      if (channel) {
        channel.listen('.chat.message.created', (eventData: any) => {
          console.log('Received real-time chat message:', eventData);

          // Extract the chat data from the event
          const data = eventData;

          // Only add the message if it's from another user (our own messages are added via the mutation)
          if (data && data.user_id !== user?.id) {
            queryClient.setQueryData(['chats', task.id], (oldData: Chat[] | undefined) => {
              if (!oldData) return [data];
              // Check if the message already exists in the chat list
              const messageExists = oldData.some(chat => chat.id === data.id);
              if (messageExists) return oldData;
              return [...oldData, data];
            });
          }
        });

        return () => {
          try {
            // Properly clean up the listener
            channel.stopListening('.chat.message.created');
            // Also try to leave the channel to clean up resources
            echo.leave(`task.${task.id}`);
          } catch (error) {
            console.error('Error cleaning up chat message listener:', error);
          }
        };
      }
    } catch (error) {
      console.error('Error setting up chat message listener:', error);
    }
  }, [echo, task.id, queryClient, user?.id]);

  // Handle sending a message
  const internalHandleSendMessage = () => {
    if (!message.trim()) return;

    createChatMutation.mutate({
      task_id: task.id,
      message: message.trim()
    });
  };

  // Use external handler if provided
  const handleSendMessage = externalHandleSendMessage || internalHandleSendMessage;

  // Handle deleting a message
  const handleDeleteMessage = (chatId: number) => {
    if (window.confirm('Are you sure you want to delete this message?')) {
      deleteChatMutation.mutate(chatId);
    }
  };

  // Format date for display
  const formatMessageDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`;
  };

  return (
    <div className="flex flex-col h-full">

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto mb-4 space-y-4 max-h-[300px]">
        {isLoading ? (
          <div className="flex justify-center items-center h-20">
            <p className="text-sm text-muted-foreground">Loading messages...</p>
          </div>
        ) : chats.length === 0 ? (
          <div className="flex justify-center items-center h-20 text-center">
            <p className="text-sm text-muted-foreground">No comments yet. Be the first to comment!</p>
          </div>
        ) : (
          chats.map((chat: Chat) => (
            <div
              key={chat.id}
              className="flex gap-3 group p-3 rounded-md transition-colors hover:bg-muted/20"
              ref={commentRefs.current[chat.id]}
              id={`comment-${chat.id}`}
            >
              <Avatar className="h-8 w-8 flex-shrink-0">
                {chat.user?.profile_picture ? (
                  <AvatarImage src={getFullImageUrl(chat.user.profile_picture)} alt={`${chat.user?.first_name} ${chat.user?.last_name}`} />
                ) : (
                  <AvatarFallback>
                    {getUserInitials(chat.user?.first_name, chat.user?.last_name)}
                  </AvatarFallback>
                )}
              </Avatar>

              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <span className="font-medium text-sm">
                      {chat.user?.first_name} {chat.user?.last_name}
                    </span>
                    <span className="text-xs text-muted-foreground ml-2">
                      {chat.created_at && formatMessageDate(chat.created_at)}
                    </span>
                  </div>

                  {/* Delete button - only visible for own messages */}
                  {user && chat.user_id === user.id && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => handleDeleteMessage(chat.id)}
                    >
                      <Trash2 className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  )}
                </div>

                <div className="mt-1 text-sm">
                  {formatMessageWithMentions(chat.message, chat.mentionedUsers || [])}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input area */}
      {canAddComments && (
        <div className="mt-auto">
          <Separator className="mb-4" />
          <div className="flex gap-2 p-2 bg-muted/10 rounded-md border border-border/30">
            <div className="flex-1">
              <MentionInput
                value={message}
                onChange={setMessage}
                placeholder="Write a comment..."
                className="min-h-[80px] resize-none bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0"
                projectId={project?.id}
              />
            </div>
            <div className="flex items-end pb-1">
              <Button
                type="button"
                size="icon"
                variant="default"
                onClick={handleSendMessage}
                disabled={!message.trim() || createChatMutation.isPending}
                title="Send message"
                className="h-9 w-9"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskChat;
