import React, { useState, useEffect } from 'react';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import { Role } from '@/entities/Role';
import {
  isGroupProject,
  isProjectOwner,
  canAddMembers
} from '@/utils/projectTypeUtils';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import {
  getProjectRoles,
  removeUserFromProject,
  updateUserRole,
  getRoles,
  sendProjectInvitation,
  getProjectInvitations,
  cancelProjectInvitation,
  getProject,
  addUserToProject
} from '@/api/projectsApi';
import { searchUsers } from '@/api/usersApi';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  UserPlus,
  UserMinus,
  MoreVertical,
  UserCog,
  Shield,
  Edit,
  Trash,
  Search,
  X,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface ProjectMember {
  user_id: number;
  name: string;
  email: string;
  role_id: number;
  role_name: string;
  is_owner: boolean;
  joined_at: string;
}

interface ProjectInvitation {
  id: number;
  project_id: number;
  invited_by_user_id: number;
  email: string;
  role_id: number;
  token: string;
  expires_at: string;
  created_at: string;
  updated_at: string;
  invitedBy?: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    profile_picture?: string;
  };
  role?: {
    id: number;
    name: string;
  };
}

interface ProjectMemberManagerProps {
  project: Project;
  currentUser: User | null;
  onProjectUpdated?: (updatedProject: Project) => void;
}

/**
 * A component that allows users to manage project members
 */
const ProjectMemberManager: React.FC<ProjectMemberManagerProps> = ({
  project,
  currentUser,
  onProjectUpdated,
}) => {
  const [members, setMembers] = useState<ProjectMember[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [pendingInvitations, setPendingInvitations] = useState<ProjectInvitation[]>([]);
  const [showInvitations, setShowInvitations] = useState(false);
  const [invitationEmail, setInvitationEmail] = useState('');

  const isGroup = isGroupProject(project);
  const isOwner = isProjectOwner(project, currentUser);
  const canAdd = canAddMembers(project, currentUser);
  const canRemove = hasProjectPermission(project, currentUser, ProjectPermission.REMOVE_MEMBER);
  const canChangeRole = hasProjectPermission(project, currentUser, ProjectPermission.CHANGE_MEMBER_ROLE);

  // Load members, roles, and invitations
  useEffect(() => {
    if (!project) return;

    const loadData = async () => {
      setIsLoading(true);
      try {
        // Load project members
        const membersResponse = await getProjectRoles(project.id);
        console.log('Project members response:', membersResponse);

        if (membersResponse) {
          setMembers(membersResponse);
          console.log('Members set to:', membersResponse);
        }

        // Load available roles
        const rolesResponse = await getRoles();
        if (rolesResponse) {
          setRoles(rolesResponse);
        }

        // Load pending invitations
        if (isGroup && canAdd) {
          try {
            console.log('Loading pending invitations for project:', project.id);
            const invitationsResponse = await getProjectInvitations(project.id);
            console.log('Pending invitations response:', invitationsResponse);

            if (invitationsResponse && Array.isArray(invitationsResponse)) {
              console.log(`Found ${invitationsResponse.length} pending invitations`);
              setPendingInvitations(invitationsResponse);

              // If there are pending invitations, automatically show them
              if (invitationsResponse.length > 0) {
                setShowInvitations(true);
              }
            } else {
              console.log('No pending invitations found or invalid response');
              setPendingInvitations([]);
            }
          } catch (invitationError) {
            console.error('Failed to load project invitations:', invitationError);
            // Don't show an error toast for this, as it's not critical
            setPendingInvitations([]);
          }
        }
      } catch (error) {
        console.error('Failed to load project members:', error);
        toast.error('Failed to load project members');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [project, isGroup, canAdd]);

  // Handle user search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    try {
      const results = await searchUsers(searchQuery);

      // Filter out users who are already members
      const filteredResults = results.filter(user =>
        !members.some(member => member.user_id === user.id)
      );

      setSearchResults(filteredResults);
    } catch (error) {
      console.error('Failed to search users:', error);
      toast.error('Failed to search users');
    } finally {
      setIsSearching(false);
    }
  };

  // Handle adding a user to the project
  const handleAddUser = async () => {
    if (!selectedUser || !selectedRole) {
      toast.error('Please select a user and a role');
      return;
    }

    // Get the user's full name
    const userName = selectedUser.name ||
                    `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim() ||
                    selectedUser.email;

    setIsLoading(true);
    try {
      const response = await addUserToProject(project.id, selectedUser.id, selectedRole);

      // Close the dialog immediately to provide better UX
      setAddDialogOpen(false);

      // Show a toast message that the user is being added
      toast.success(`Adding ${userName} to the project...`);

      // Wait a moment to ensure the backend has time to process
      setTimeout(async () => {
        try {
          // Refresh the member list
          const membersResponse = await getProjectRoles(project.id);
          if (membersResponse) {
            setMembers(membersResponse);
          }

          // Update the project if it was converted from personal to group
          if (!isGroup && onProjectUpdated) {
            onProjectUpdated({
              ...project,
              is_group_project: true
            });
          }

          toast.success(`${userName} added to the project successfully`);
        } catch (refreshError) {
          console.error('Error refreshing member list:', refreshError);
          // Even if refresh fails, the user was likely added
          toast.success(`${userName} was added, but you may need to refresh the page to see the changes`);
        }
      }, 1000); // Wait 1 second before refreshing

      // Reset form state
      setSelectedUser(null);
      setSelectedRole(null);
      setSearchQuery('');
      setSearchResults([]);
    } catch (error) {
      console.error('Failed to add user to project:', error);
      toast.error('Failed to add user to project');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sending an invitation
  const handleSendInvitation = async () => {
    if (!selectedRole) {
      toast.error('Please select a role for the invitation');
      return;
    }

    // Use either the selected user's email or the manually entered email
    const email = selectedUser ? selectedUser.email : invitationEmail;

    if (!email) {
      toast.error('Please enter an email address');
      return;
    }

    setIsLoading(true);
    try {
      // Send the invitation
      const response = await sendProjectInvitation(project.id, email, selectedRole);

      // If the response has an error property, it means the API call failed but already showed a toast
      if (response && response.error) {
        // Just reset the form state, don't show another toast
        setSelectedUser(null);
        setInvitationEmail('');
        setSearchQuery('');
        setSearchResults([]);
        return;
      }

      // Close the dialog immediately to provide better UX
      setAddDialogOpen(false);

      // Check if the user exists (this info comes from the backend)
      const userExists = response.user_exists;

      // Show a toast message about the invitation
      toast.success(
        userExists
          ? `Invitation sent to ${email}. They will be added once they accept.`
          : `Invitation sent to ${email}. They will need to create an account and accept.`
      );

      // Refresh the invitations list
      try {
        const invitationsResponse = await getProjectInvitations(project.id);
        if (invitationsResponse) {
          console.log('Received pending invitations:', invitationsResponse);
          setPendingInvitations(invitationsResponse);
          // Automatically show the invitations section when a new invitation is sent
          setShowInvitations(true);
        }
      } catch (invitationError) {
        console.error('Failed to refresh invitations:', invitationError);
        // Try again after a short delay
        setTimeout(async () => {
          try {
            const retryResponse = await getProjectInvitations(project.id);
            if (retryResponse) {
              console.log('Retry received pending invitations:', retryResponse);
              setPendingInvitations(retryResponse);
              setShowInvitations(true);
            }
          } catch (retryError) {
            console.error('Failed to refresh invitations on retry:', retryError);
          }
        }, 1000);
      }

      // Update the project if it was converted from personal to group
      if (!isGroup && onProjectUpdated) {
        onProjectUpdated({
          ...project,
          is_group_project: true
        });
      }

      // Reset the form
      setSelectedUser(null);
      setInvitationEmail('');
      setSearchQuery('');
      setSearchResults([]);
    } catch (error) {
      console.error('Failed to send invitation:', error);

      // Improved error handling with specific messages
      let errorMessage = 'Failed to send invitation';

      if (error.response?.data?.message) {
        // Check for specific error messages from the backend
        const backendMessage = error.response.data.message;

        if (backendMessage.includes('cannot invite yourself') ||
            backendMessage.includes('You cannot invite yourself')) {
          errorMessage = 'You cannot invite yourself to a project';
        } else if (backendMessage.includes('already a member')) {
          errorMessage = 'This user is already a member of the project';
        } else if (backendMessage.includes('permission')) {
          errorMessage = 'You do not have permission to invite members to this project';
        } else {
          errorMessage = backendMessage;
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancelling an invitation
  const handleCancelInvitation = async (invitationId: number, email: string) => {
    setIsLoading(true);
    try {
      await cancelProjectInvitation(project.id, invitationId);

      // Refresh the invitations list
      try {
        console.log('Refreshing invitations after cancellation');
        const invitationsResponse = await getProjectInvitations(project.id);
        console.log('Updated invitations after cancellation:', invitationsResponse);

        if (invitationsResponse && Array.isArray(invitationsResponse)) {
          setPendingInvitations(invitationsResponse);

          // If there are no more invitations, hide the invitations section
          if (invitationsResponse.length === 0) {
            setShowInvitations(false);
          }
        } else {
          setPendingInvitations([]);
          setShowInvitations(false);
        }
      } catch (refreshError) {
        console.error('Failed to refresh invitations after cancellation:', refreshError);
        // Try again after a short delay
        setTimeout(async () => {
          try {
            const retryResponse = await getProjectInvitations(project.id);
            if (retryResponse) {
              setPendingInvitations(retryResponse);
            }
          } catch (retryError) {
            console.error('Failed to refresh invitations on retry:', retryError);
          }
        }, 1000);
      }

      toast.success(`Invitation to ${email} cancelled`);
    } catch (error) {
      console.error('Failed to cancel invitation:', error);
      toast.error('Failed to cancel invitation');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle removing a user from the project
  const handleRemoveUser = async (userId: number, userName: string) => {
    setIsLoading(true);
    try {
      console.log(`Attempting to remove user ${userId} (${userName}) from project ${project.id}`);

      // Make sure userId is a number
      const userIdNum = Number(userId);
      if (isNaN(userIdNum)) {
        throw new Error(`Invalid user ID: ${userId}`);
      }

      await removeUserFromProject(project.id, userIdNum);

      // Refresh the member list
      const membersResponse = await getProjectRoles(project.id);
      if (membersResponse) {
        setMembers(membersResponse);
      }

      // Update the project if needed
      if (onProjectUpdated) {
        // Refresh the project data to get the latest state
        try {
          const updatedProject = await getProject(project.id.toString());
          onProjectUpdated(updatedProject);
        } catch (error) {
          console.error('Failed to refresh project data:', error);
        }
      }

      toast.success(`${userName} removed from the project`);
    } catch (error) {
      console.error('Failed to remove user from project:', error);

      // Show a more detailed error message if available
      if (error.response && error.response.data && error.response.data.message) {
        toast.error(`Error: ${error.response.data.message}`);
      } else {
        toast.error('Failed to remove user from project');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle changing a user's role
  const handleChangeRole = async (userId: number, roleId: number, userName: string) => {
    setIsLoading(true);
    try {
      // Find the member in the members list to get their name
      const member = members.find(m => (m.user_id === userId || m.id === userId));

      // Use the provided userName, or get it from the member object, or use a fallback
      const displayName = userName ||
                         (member ? (member.name || `${member.first_name || ''} ${member.last_name || ''}`.trim() || member.email || 'User') : 'User');

      console.log(`Attempting to change role for user ${userId} (${displayName}) to role ${roleId} in project ${project.id}`);

      // Make sure userId and roleId are numbers
      const userIdNum = Number(userId);
      const roleIdNum = Number(roleId);

      if (isNaN(userIdNum)) {
        throw new Error(`Invalid user ID: ${userId}`);
      }

      if (isNaN(roleIdNum)) {
        throw new Error(`Invalid role ID: ${roleId}`);
      }

      // Use addUserToProject instead of updateUserRole to avoid the SQL error
      // The backend will update the role if the user already exists
      await addUserToProject(project.id, userIdNum, roleIdNum);

      // Refresh the member list
      const membersResponse = await getProjectRoles(project.id);
      if (membersResponse) {
        setMembers(membersResponse);
      }

      // Get the role name for a more descriptive message
      const roleName = roles.find(r => r.id === roleIdNum)?.name || 'new role';

      toast.success(`${displayName}'s role updated to ${roleName}`);
    } catch (error) {
      console.error('Failed to update user role:', error);

      // Show a more detailed error message if available
      if (error.response && error.response.data && error.response.data.message) {
        toast.error(`Error: ${error.response.data.message}`);
      } else {
        toast.error('Failed to update user role');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Get the initials for the avatar
  const getInitials = (name?: string) => {
    if (!name || name.trim() === '') {
      return 'U'; // Default for unknown user
    }
    return name
      .split(' ')
      .map(part => part[0] || '')
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Render role badge
  const renderRoleBadge = (roleName: string, isOwner: boolean) => {
    if (isOwner) {
      return (
        <Badge variant="default" className="bg-amber-500 hover:bg-amber-600">
          <Shield size={12} className="mr-1" />
          Owner
        </Badge>
      );
    }

    switch (roleName.toLowerCase()) {
      case 'admin':
        return (
          <Badge variant="default">
            <Shield size={12} className="mr-1" />
            Admin
          </Badge>
        );
      case 'editor':
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            <Edit size={12} className="mr-1" />
            Editor
          </Badge>
        );
      case 'viewer':
        return (
          <Badge variant="outline">
            Viewer
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {roleName}
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Members</CardTitle>
        <CardDescription>
          {isGroup
            ? 'Manage who has access to this project'
            : 'This is a personal project visible only to you'}
        </CardDescription>
      </CardHeader>

      <CardContent>
        {isGroup && canAdd && (
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium">
                Pending Invitations
                {pendingInvitations.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {pendingInvitations.length}
                  </Badge>
                )}
              </h3>
              {pendingInvitations.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowInvitations(!showInvitations)}
                >
                  {showInvitations ? 'Hide' : 'Show'}
                </Button>
              )}
            </div>

            {pendingInvitations.length === 0 ? (
              <div className="text-sm text-muted-foreground p-3 border rounded-md bg-muted/20">
                No pending invitations.
              </div>
            ) : showInvitations && (
              <div className="border rounded-md divide-y bg-card">
                {pendingInvitations.map(invitation => (
                  <div key={invitation.id} className="flex items-center justify-between p-3">
                    <div>
                      <div className="font-medium">{invitation.email}</div>
                      <div className="text-xs text-muted-foreground">
                        Role: {invitation.role?.name || 'Unknown'} •
                        Invited: {new Date(invitation.created_at).toLocaleDateString()}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCancelInvitation(invitation.id, invitation.email)}
                    >
                      <Trash size={16} className="text-destructive" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {isLoading && members.length === 0 ? (
          <div className="text-center p-8 text-muted-foreground">
            Loading members...
          </div>
        ) : members.length === 0 ? (
          <div className="text-center p-8 text-muted-foreground">
            {isGroup
              ? 'No members found.'
              : 'This is a personal project visible only to you.'}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                {(canRemove || canChangeRole) && (
                  <TableHead className="text-right">Actions</TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {members.map(member => {
                console.log('Rendering member:', member);
                return (
                <TableRow key={member.user_id}>
                  <TableCell>
                    <div className="flex items-center">
                      <Avatar className="h-8 w-8 mr-2">
                        <AvatarFallback className="bg-primary/10 text-primary dark:bg-white/90 dark:text-primary">{getInitials(member.name)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-xs text-muted-foreground">{member.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {renderRoleBadge(member.role_name, member.is_owner)}
                  </TableCell>
                  {(canRemove || canChangeRole) && (
                    <TableCell className="text-right">
                      {member.is_owner ? (
                        <Button variant="ghost" size="icon" disabled>
                          <MoreVertical size={16} />
                        </Button>
                      ) : (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical size={16} />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {canChangeRole && (
                              <>
                                <DropdownMenuItem
                                  disabled={member.role_id === 1}
                                  onClick={() => {
                                    console.log('Make Admin clicked for user:', member);
                                    // Use user_id if available, otherwise fall back to id
                                    const userId = member.user_id || member.id;
                                    if (userId) {
                                      handleChangeRole(userId, 1, member.name);
                                    } else {
                                      toast.error('User ID is missing');
                                    }
                                  }}
                                >
                                  <Shield size={16} className="mr-2" />
                                  Make Admin
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  disabled={member.role_id === 2}
                                  onClick={() => {
                                    console.log('Make Editor clicked for user:', member);
                                    // Use user_id if available, otherwise fall back to id
                                    const userId = member.user_id || member.id;
                                    if (userId) {
                                      handleChangeRole(userId, 2, member.name);
                                    } else {
                                      toast.error('User ID is missing');
                                    }
                                  }}
                                >
                                  <Edit size={16} className="mr-2" />
                                  Make Editor
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  disabled={member.role_id === 3}
                                  onClick={() => {
                                    console.log('Make Viewer clicked for user:', member);
                                    // Use user_id if available, otherwise fall back to id
                                    const userId = member.user_id || member.id;
                                    if (userId) {
                                      handleChangeRole(userId, 3, member.name);
                                    } else {
                                      toast.error('User ID is missing');
                                    }
                                  }}
                                >
                                  <UserCog size={16} className="mr-2" />
                                  Make Viewer
                                </DropdownMenuItem>
                              </>
                            )}

                            {canRemove && (
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                    <UserMinus size={16} className="mr-2" />
                                    Remove from Project
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Remove Member</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to remove {member.name} from this project?
                                      They will lose access to all project resources.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => {
                                        console.log('Remove clicked for user:', member);
                                        // Use user_id if available, otherwise fall back to id
                                        const userId = member.user_id || member.id;
                                        if (userId) {
                                          handleRemoveUser(userId, member.name);
                                        } else {
                                          toast.error('User ID is missing');
                                        }
                                      }}
                                    >
                                      Remove
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              );
              })}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default ProjectMemberManager;
