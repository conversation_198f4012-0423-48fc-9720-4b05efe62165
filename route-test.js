import axios from 'axios';

// Configuration
const API_BASE_URL = 'http://127.0.0.1:8000/api';
const FRONTEND_BASE_URL = 'http://localhost:8080';

// List of key API endpoints to test
const apiEndpoints = [
  { method: 'GET', url: '/login', expectStatus: 401 }, // Should return 401 when not authenticated
  { method: 'GET', url: '/user', expectStatus: [200, 401] }, // Should return user or 401 if not authenticated
  { method: 'GET', url: '/workspaces', expectStatus: [200, 401] }, // Should return workspaces or 401 if not authenticated
  { method: 'GET', url: '/users', expectStatus: [200, 401] }, // Should return users or 401 if not authenticated
  { method: 'GET', url: '/calendar-events', expectStatus: [200, 401] }, // Should return events or 401 if not authenticated
];

// Function to test API endpoints
async function testApiEndpoints() {
  console.log('Testing API endpoints...');
  console.log('======================');

  for (const endpoint of apiEndpoints) {
    try {
      console.log(`Testing ${endpoint.method} ${endpoint.url}...`);

      const response = await axios({
        method: endpoint.method.toLowerCase(),
        url: `${API_BASE_URL}${endpoint.url}`,
        data: endpoint.data,
        validateStatus: () => true, // Don't throw on error status codes
      });

      const expectedStatuses = Array.isArray(endpoint.expectStatus)
        ? endpoint.expectStatus
        : [endpoint.expectStatus];

      if (expectedStatuses.includes(response.status)) {
        console.log(`✅ ${endpoint.method} ${endpoint.url} - Status: ${response.status} (Expected: ${expectedStatuses.join(' or ')})`);
      } else {
        console.log(`❌ ${endpoint.method} ${endpoint.url} - Status: ${response.status} (Expected: ${expectedStatuses.join(' or ')})`);
        console.log(`   Response: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint.method} ${endpoint.url} - Error: ${error.message}`);
    }
  }
}

// Main function
async function main() {
  try {
    await testApiEndpoints();
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the script
main();
