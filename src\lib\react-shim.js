/**
 * React Shim
 *
 * This file ensures React is available globally for components that expect it.
 */

// Import React - this will be properly bundled by Vite
import * as ReactModule from 'react';

// Make React available globally in a safe way
if (typeof window !== 'undefined') {
  // Get the existing React object or create a new one
  const existingReact = window.React || {};

  // Create a merged React object with all exports
  window.React = {
    ...existingReact,
    ...ReactModule
  };

  // Ensure critical methods are available
  if (!window.React.forwardRef) window.React.forwardRef = ReactModule.forwardRef;
  if (!window.React.createContext) window.React.createContext = ReactModule.createContext;
  if (!window.React.createElement) window.React.createElement = ReactModule.createElement;
}

// Export React for module usage
export default ReactModule;
