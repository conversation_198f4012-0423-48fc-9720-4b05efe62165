import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { getProject, updateProject, deleteProject, leaveProject } from '@/api/projectsApi';
import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import ProjectSettings from '@/components/project/ProjectSettings';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { CalendarIcon, Loader2, AlertTriangle, LogOut } from 'lucide-react';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { ProjectPriority, ProjectProgress } from '@/entities/Project';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

const EditProject = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Get the tab parameter from the URL
  const location = window.location;
  const searchParams = new URLSearchParams(location.search);
  const tabParam = searchParams.get('tab');

  const [project, setProject] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);
  const [isOwner, setIsOwner] = useState(false);

  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<string>('');
  const [progress, setProgress] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Fetch project data
  useEffect(() => {
    const fetchProject = async () => {
      if (!projectId) return;

      try {
        setIsLoading(true);
        const data = await getProject(parseInt(projectId));
        setProject(data);

        // Initialize form with project data
        setName(data.name || '');
        setDescription(data.description || '');
        setPriority(data.priority?.toString() || '');
        setProgress(data.progress?.toString() || '');
        setStartDate(data.start_date ? new Date(data.start_date) : undefined);
        setEndDate(data.end_date ? new Date(data.end_date) : undefined);

        // Check if current user is the project owner
        setIsOwner(user?.id === data.user_id);
      } catch (error) {
        console.error('Error fetching project:', error);
        toast({
          title: 'Error',
          description: 'Failed to load project details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProject();
  }, [projectId, toast]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!projectId) return;

    try {
      setIsSaving(true);

      const updatedProject = {
        name,
        description,
        priority: parseInt(priority),
        progress: parseInt(progress),
        start_date: startDate ? format(startDate, 'yyyy-MM-dd') : null,
        end_date: endDate ? format(endDate, 'yyyy-MM-dd') : null,
      };

      await updateProject(parseInt(projectId), updatedProject);

      toast({
        title: 'Success',
        description: 'Project updated successfully',
      });

      // Navigate back to project details
      navigate(`/projects/${projectId}`);
    } catch (error) {
      console.error('Error updating project:', error);
      toast({
        title: 'Error',
        description: 'Failed to update project',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle project deletion
  const handleDelete = async () => {
    if (!projectId) return;

    try {
      setIsDeleting(true);

      await deleteProject(parseInt(projectId));

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['starredProjects'] });

      toast({
        title: 'Success',
        description: 'Project deleted successfully',
      });

      // Navigate to the projects page instead of /workspace
      navigate('/projects');
    } catch (error) {
      console.error('Error deleting project:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete project',
        variant: 'destructive',
      });
      setIsDeleting(false);
    }
  };

  // Handle leaving project
  const handleLeaveProject = async () => {
    if (!projectId) return;

    try {
      setIsLeaving(true);

      await leaveProject(parseInt(projectId));

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['starredProjects'] });

      // Navigate to the success page with project information
      navigate('/projects/leave-success', {
        state: {
          projectName: project.name
        }
      });
    } catch (error) {
      console.error('Error leaving project:', error);
      toast({
        title: 'Error',
        description: 'Failed to leave the project',
        variant: 'destructive',
      });
      setIsLeaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container max-w-4xl mx-auto px-4 py-8 flex items-center justify-center h-[calc(100vh-80px)]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  // Check if project exists or if user doesn't have edit permission
  if (!project || !hasProjectPermission(project, user, ProjectPermission.EDIT_PROJECT)) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-destructive flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                {!project ? "Project Not Found" : "Permission Denied"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                {!project
                  ? "The project you're trying to edit doesn't exist or you don't have permission to access it."
                  : "You don't have permission to edit this project. Only admins and editors can edit projects."}
              </p>
            </CardContent>
            <CardFooter>
              <Button onClick={() => navigate(project ? `/projects/${projectId}` : '/workspace')}>
                {project ? "Back to Project" : "Back to Workspace"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="container max-w-4xl mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Edit Project</h1>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={() => navigate(`/projects/${projectId}`)}>
              Cancel
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                {isOwner && hasProjectPermission(project, user, ProjectPermission.DELETE_PROJECT) ? (
                  <Button variant="destructive">Delete Project</Button>
                ) : (
                  <Button variant="destructive">
                    <LogOut className="mr-2 h-4 w-4" />
                    Leave Project
                  </Button>
                )}
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    {isOwner && hasProjectPermission(project, user, ProjectPermission.DELETE_PROJECT) ? (
                      "This action cannot be undone. This will permanently delete the project and all associated tasks, comments, and files."
                    ) : (
                      "You will be removed from this project and lose access to it. You can be added back by a project admin later."
                    )}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={isOwner && hasProjectPermission(project, user, ProjectPermission.DELETE_PROJECT) ? handleDelete : handleLeaveProject}
                    disabled={isOwner && hasProjectPermission(project, user, ProjectPermission.DELETE_PROJECT) ? isDeleting : isLeaving}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isOwner && hasProjectPermission(project, user, ProjectPermission.DELETE_PROJECT) ? (
                      isDeleting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Deleting...
                        </>
                      ) : (
                        'Delete Project'
                      )
                    ) : (
                      isLeaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Leaving...
                        </>
                      ) : (
                        'Leave Project'
                      )
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {/* Project Details Form */}
        <Card className="mb-6">
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
              <CardDescription>
                Update your project information below
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Project Name
                </label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter project name"
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium">
                  Description
                </label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter project description"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="priority" className="text-sm font-medium">
                    Priority
                  </label>
                  <Select value={priority} onValueChange={setPriority}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Low</SelectItem>
                      <SelectItem value="2">Medium</SelectItem>
                      <SelectItem value="3">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="status" className="text-sm font-medium">
                    Status
                  </label>
                  <Select value={progress} onValueChange={setProgress}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Not Started</SelectItem>
                      <SelectItem value="1">In Progress</SelectItem>
                      <SelectItem value="2">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Start Date
                  </label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, 'PPP') : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={setStartDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    End Date
                  </label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, 'PPP') : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        initialFocus
                        disabled={(date) => startDate ? date < startDate : false}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex justify-end space-x-3">
              <Button
                type="submit"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* Project Settings (includes Members tab) */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-4">Project Settings</h2>
          <ProjectSettings
            project={project}
            defaultTab={tabParam === 'members' ? 'members' : 'general'}
            onProjectUpdated={(updatedProject) => {
              setProject(updatedProject);
              // Refresh the project data
              queryClient.invalidateQueries({ queryKey: ['project', projectId] });
              queryClient.invalidateQueries({ queryKey: ['projects'] });
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default EditProject;
