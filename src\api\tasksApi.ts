
import api from './api';
import { toast } from "@/hooks/use-toast";
import { QueryClient } from '@tanstack/react-query';
import { getErrorMessage } from '@/utils/errorUtils';

// Task types
export interface Task {
  id: number;
  project_id: number;
  list_id: number; // Changed from optional to required as it's used in KanbanBoard
  title: string;
  description?: string;
  status: string;
  priority: string;
  position?: number; // Position within the list for ordering
  start_date?: string;
  end_date?: string;
  cover_image?: string;
  background?: string;
  created_at?: string;
  updated_at?: string;
  assignees?: {
    id: number;
    first_name: string;
    last_name: string;
    profile_picture?: string;
    status?: 'active' | 'former_member'; // Added status field to identify former members
    is_active_member?: boolean; // Added flag to check if user is still a project member
  }[];
}

// Task filter interface
export interface TaskFilter {
  project_id?: number;
  status?: string;
  priority?: string;
  assignee_id?: number;
  due_date_start?: string;
  due_date_end?: string;
  search?: string;
  sort_field?: string;
  sort_direction?: 'asc' | 'desc';
}

// Get all tasks with optional filters
export const getTasks = async (filtersOrProjectId: TaskFilter | number = {}) => {
  try {
    // Handle the case where a project ID is passed directly
    let params: TaskFilter = {};

    if (typeof filtersOrProjectId === 'number') {
      // If a number is passed, it's a project ID
      params = { project_id: filtersOrProjectId };
    } else {
      // Otherwise, it's a filter object
      params = filtersOrProjectId;
    }

    const response = await api.get('/tasks', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching tasks:', error);
    toast({
      title: "Failed to fetch tasks",
      description: "Could not load the tasks for this project",
      variant: "destructive"
    });
    throw error;
  }
};

// Get a specific task
export const getTask = async (id: number) => {
  try {
    const response = await api.get(`/tasks/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching task:', error);
    toast({
      title: "Failed to fetch task",
      description: "Could not load the requested task",
      variant: "destructive"
    });
    throw error;
  }
};

// Create a new task
export const createTask = async (taskData: Partial<Task>) => {
  try {
    const response = await api.post('/tasks', taskData);

    // Get the project ID from the task data
    const projectId = response.data.project_id || taskData.project_id;

    // If we have a project ID, invalidate the project data as well
    if (projectId) {
      const queryClient = new QueryClient();
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
    }

    return response.data;
  } catch (error) {
    console.error('Error creating task:', error);
    toast({
      title: "Failed to create task",
      description: "Could not create the new task",
      variant: "destructive"
    });
    throw error;
  }
};

// Update a task
export const updateTask = async (id: number, taskData: Partial<Task>) => {
  try {
    const response = await api.put(`/tasks/${id}`, taskData);

    // Get the project ID from the response or from the task data
    const projectId = response.data.project_id || taskData.project_id;

    // If we have a project ID, invalidate the project data as well
    if (projectId) {
      const queryClient = new QueryClient();
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });

      // Note: The actual project progress update is handled by the backend
      // when task status changes, but we invalidate the queries to ensure
      // the UI reflects the latest data
    }

    return response.data;
  } catch (error) {
    console.error('Error updating task:', error);
    toast({
      title: "Failed to update task",
      description: "Could not update the task",
      variant: "destructive"
    });
    throw error;
  }
};

// Delete a task
export const deleteTask = async (id: number, projectId?: number) => {
  try {
    // First get the task to get its project ID if not provided
    if (!projectId) {
      try {
        const taskResponse = await api.get(`/tasks/${id}`);
        projectId = taskResponse.data.project_id;
      } catch (e) {
        console.error('Error fetching task before deletion:', e);
      }
    }

    const response = await api.delete(`/tasks/${id}`);

    // If we have a project ID, invalidate the project data as well
    if (projectId) {
      const queryClient = new QueryClient();
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
    }

    return response.data;
  } catch (error) {
    console.error('Error deleting task:', error);
    toast({
      title: "Failed to delete task",
      description: "Could not delete the task",
      variant: "destructive"
    });
    throw error;
  }
};

// Add assignees to a task
export const addTaskAssignees = async (taskId: number, assigneeIds: number[]) => {
  try {
    const response = await api.post(`/tasks/${taskId}/assignees`, {
      assignees: assigneeIds
    });
    return response.data;
  } catch (error) {
    console.error('Error adding assignees to task:', error);
    toast({
      title: "Failed to add assignees",
      description: "Could not add assignees to the task",
      variant: "destructive"
    });
    throw error;
  }
};

// Remove assignees from a task
export const removeTaskAssignees = async (taskId: number, assigneeIds: number[]) => {
  try {
    const response = await api.delete(`/tasks/${taskId}/assignees`, {
      data: { assignees: assigneeIds }
    });
    return response.data;
  } catch (error) {
    console.error('Error removing assignees from task:', error);
    toast({
      title: "Failed to remove assignees",
      description: "Could not remove assignees from the task",
      variant: "destructive"
    });
    throw error;
  }
};

// Archive a task
export const archiveTask = async (id: number) => {
  try {
    const response = await api.post(`/tasks/${id}/archive`);
    return response.data;
  } catch (error) {
    console.error('Error archiving task:', error);
    toast({
      title: "Failed to archive task",
      description: "Could not archive the task",
      variant: "destructive"
    });
    throw error;
  }
};

// Unarchive a task
export const unarchiveTask = async (id: number) => {
  try {
    const response = await api.delete(`/tasks/${id}/archive`);
    return response.data;
  } catch (error) {
    console.error('Error unarchiving task:', error);
    toast({
      title: "Failed to unarchive task",
      description: "Could not unarchive the task",
      variant: "destructive"
    });
    throw error;
  }
};

// Reorder tasks within a list
export const reorderTasks = async (listId: number, taskIds: number[]) => {
  try {
    const response = await api.post(`/lists/${listId}/reorder-tasks`, {
      task_ids: taskIds
    });
    return response.data;
  } catch (error) {
    console.error('Error reordering tasks:', error);
    toast({
      title: "Failed to reorder tasks",
      description: "Could not update the task order",
      variant: "destructive"
    });
    throw error;
  }
};

// Get project users for task assignment and filtering
export const getProjectUsers = async (projectId: string | number) => {
  try {
    const response = await api.get(`/projects/${projectId}/users`);
    return response.data;
  } catch (error) {
    console.error('Error fetching project users:', error);
    toast({
      title: "Failed to fetch users",
      description: "Could not load the users for this project",
      variant: "destructive"
    });
    throw error;
  }
};

// Add an assignee to a task (for task reassignment)
export const addAssigneeToTask = async (taskId: number, assigneeIds: number[]) => {
  try {
    const response = await api.post(`/tasks/${taskId}/assignees`, {
      assignees: assigneeIds
    });
    return response.data;
  } catch (error) {
    console.error('Error adding assignee to task:', error);
    toast({
      title: "Failed to add assignee",
      description: "Could not add the assignee to the task",
      variant: "destructive"
    });
    throw error;
  }
};

// Remove an assignee from a task (for task reassignment)
export const removeAssigneeFromTask = async (taskId: number, assigneeIds: number[]) => {
  try {
    const response = await api.delete(`/tasks/${taskId}/assignees`, {
      data: { assignees: assigneeIds }
    });
    return response.data;
  } catch (error) {
    console.error('Error removing assignee from task:', error);
    toast({
      title: "Failed to remove assignee",
      description: "Could not remove the assignee from the task",
      variant: "destructive"
    });
    throw error;
  }
};
