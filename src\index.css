
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse-subtle {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

@keyframes pulse-once {
  0% {
    background-color: rgba(var(--primary), 0.2);
  }
  50% {
    background-color: rgba(var(--primary), 0.1);
  }
  100% {
    background-color: transparent;
  }
}

@layer base {
  :root {
    /* Primary colors - Jira Blue */
    --primary: 212 100% 40%; /* #0052CC */
    --primary-foreground: 0 0% 100%; /* #FFFFFF */

    /* Background colors */
    --background: 0 0% 100%; /* #FFFFFF */
    --foreground: 215 35% 20%; /* #172B4D */

    /* Card colors */
    --card: 0 0% 100%; /* #FFFFFF */
    --card-foreground: 215 35% 20%; /* #172B4D */

    /* Popover colors */
    --popover: 0 0% 100%; /* #FFFFFF */
    --popover-foreground: 215 35% 20%; /* #172B4D */

    /* Secondary colors */
    --secondary: 220 14% 96%; /* #F4F5F7 */
    --secondary-foreground: 215 35% 20%; /* #172B4D */

    /* Muted colors */
    --muted: 220 14% 96%; /* #F4F5F7 */
    --muted-foreground: 215 20% 48%; /* #6B778C */

    /* Accent colors */
    --accent: 210 20% 96%; /* #F4F5F7 */
    --accent-foreground: 215 35% 20%; /* #172B4D */

    /* Status colors */
    --success: 153 55% 46%; /* #36B37E */
    --success-foreground: 0 0% 100%; /* #FFFFFF */
    --warning: 38 100% 50%; /* #FFAB00 */
    --warning-foreground: 215 35% 20%; /* #172B4D */
    --destructive: 7 100% 60%; /* #FF5630 */
    --destructive-foreground: 0 0% 100%; /* #FFFFFF */
    --info: 254 40% 54%; /* #6554C0 */
    --info-foreground: 0 0% 100%; /* #FFFFFF */

    /* Subtle background colors */
    --success-subtle: 153 55% 94%; /* #E3FCEF */
    --warning-subtle: 38 100% 95%; /* #FFFAE6 */
    --destructive-subtle: 7 100% 95%; /* #FFEBE6 */
    --info-subtle: 254 40% 95%; /* #EAE6FF */

    /* Priority colors */
    --priority-high: 7 100% 60%; /* #FF5630 */
    --priority-high-foreground: 0 0% 100%; /* #FFFFFF */
    --priority-medium: 38 100% 50%; /* #FFAB00 */
    --priority-medium-foreground: 215 35% 20%; /* #172B4D */
    --priority-low: 210 100% 65%; /* #4C9AFF */
    --priority-low-foreground: 0 0% 100%; /* #FFFFFF */

    /* Border and input colors */
    --border: 220 13% 86%; /* #DFE1E6 */
    --input: 220 13% 86%; /* #DFE1E6 */
    --ring: 212 100% 40%; /* #0052CC */

    /* Radius */
    --radius: 0.375rem;

    /* Sidebar colors */
    --sidebar-background: 0 0% 100%; /* #FFFFFF */
    --sidebar-foreground: 215 35% 20%; /* #172B4D */
    --sidebar-primary: 212 100% 40%; /* #0052CC */
    --sidebar-primary-foreground: 0 0% 100%; /* #FFFFFF */
    --sidebar-accent: 220 14% 96%; /* #F4F5F7 */
    --sidebar-accent-foreground: 215 35% 20%; /* #172B4D */
    --sidebar-border: 220 13% 86%; /* #DFE1E6 */
    --sidebar-ring: 212 100% 40%; /* #0052CC */

    /* Typography */
    --font-display: 3rem;
    --font-h1: 2.5rem;
    --font-h2: 2rem;
    --font-h3: 1.5rem;
    --font-h4: 1.25rem;
    --font-h5: 1.125rem;
    --font-h6: 1rem;
    --font-body-lg: 1.125rem;
    --font-body: 1rem;
    --font-body-sm: 0.875rem;
    --font-caption: 0.75rem;

    /* Line heights */
    --line-height-headings: 1.2;
    --line-height-body: 1.5;

    /* Letter spacing */
    --letter-spacing-headings: -0.02em;
    --letter-spacing-body: 0;
    --letter-spacing-caps: 0.05em;

    /* Font weights */
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Base spacing scale */
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Component-specific spacing */
    --card-padding: var(--space-5);
    --card-gap: var(--space-4);
    --button-padding-x: var(--space-4);
    --button-padding-y: var(--space-2);
    --form-group-margin: var(--space-4);
    --input-padding-x: var(--space-3);
    --input-padding-y: var(--space-2);
    --nav-item-padding-x: var(--space-3);
    --nav-item-padding-y: var(--space-2);

    /* Layout spacing */
    --container-padding-mobile: var(--space-4);
    --container-padding-tablet: var(--space-6);
    --container-padding-desktop: var(--space-8);
    --grid-gap-tight: var(--space-2);
    --grid-gap-default: var(--space-4);
    --grid-gap-loose: var(--space-6);
    --grid-gap-wide: var(--space-8);
  }

  .dark {
    /* Background colors - dark slate gray with subtle blue tint */
    --background: 215 30% 12%; /* #171E2E - Dark slate background with subtle blue */
    --foreground: 210 25% 98%; /* #F8FAFC - Brighter text for better contrast */

    /* Card colors - slightly elevated from background */
    --card: 215 28% 17%; /* #1F2937 - Slightly lighter than background */
    --card-foreground: 210 25% 98%; /* #F8FAFC - Bright text */

    /* Popover colors - same as card for consistency */
    --popover: 215 28% 17%; /* #1F2937 - Same as card */
    --popover-foreground: 210 25% 98%; /* #F8FAFC */

    /* Primary colors - purple accent for dark mode */
    --primary: 250 60% 60%; /* #7C5CFF - Purple accent */
    --primary-foreground: 0 0% 100%; /* #FFFFFF */

    /* Secondary colors - more distinct from background */
    --secondary: 215 25% 27%; /* #2D3748 - More distinct secondary */
    --secondary-foreground: 210 25% 98%; /* #F8FAFC */

    /* Muted colors - better contrast */
    --muted: 215 20% 25%; /* #2A3341 - Subtle muted background */
    --muted-foreground: 210 20% 80%; /* #C5D0E6 - More visible muted text */

    /* Accent colors - more vibrant */
    --accent: 250 30% 30%; /* #3A3A66 - Purple-tinted accent */
    --accent-foreground: 210 25% 98%; /* #F8FAFC */

    /* Status colors - more vibrant for dark mode */
    --success: 153 70% 50%; /* #2ECB7E - Brighter green */
    --warning: 38 100% 60%; /* #FFBC1F - Brighter yellow */
    --destructive: 7 100% 65%; /* #FF6B4A - Brighter red */
    --info: 254 60% 60%; /* #7B6EE6 - Brighter purple */

    /* Priority colors - more vibrant */
    --priority-high: 7 100% 65%; /* #FF6B4A - Brighter red */
    --priority-high-foreground: 0 0% 100%; /* #FFFFFF */
    --priority-medium: 38 100% 60%; /* #FFBC1F - Brighter yellow */
    --priority-medium-foreground: 220 40% 10%; /* #0A1628 - Dark text on yellow */
    --priority-low: 210 100% 70%; /* #66ADFF - Brighter blue */
    --priority-low-foreground: 0 0% 100%; /* #FFFFFF */

    /* Subtle background colors - more visible in dark mode */
    --success-subtle: 153 40% 25%; /* #1F5D3D - More visible green */
    --warning-subtle: 38 40% 25%; /* #5D4A1F - More visible yellow */
    --destructive-subtle: 7 40% 25%; /* #5D2A1F - More visible red */
    --info-subtle: 254 40% 25%; /* #352A5D - More visible purple */

    /* Border and input colors - more visible */
    --border: 215 15% 25%; /* #2D3748 - Subtle borders */
    --input: 215 15% 25%; /* #2D3748 */
    --ring: 250 60% 60%; /* #7C5CFF - Match primary */

    /* Sidebar colors - more distinct */
    --sidebar-background: 215 35% 10%; /* #111827 - Darker sidebar */
    --sidebar-foreground: 210 25% 98%; /* #F8FAFC */
    --sidebar-primary: 250 60% 60%; /* #7C5CFF - Match primary */
    --sidebar-accent: 250 30% 30%; /* #3A3A66 */
    --sidebar-border: 215 15% 25%; /* #2D3748 */
  }

  /* Responsive adjustments for typography */
  @media (max-width: 768px) {
    :root {
      --font-display: 2.5rem;
      --font-h1: 2rem;
      --font-h2: 1.75rem;
      --font-h3: 1.375rem;
      --font-h4: 1.125rem;
      --font-h5: 1rem;
      --font-h6: 0.875rem;

      /* Adjust spacing for tablet */
      --card-padding: var(--space-4);
      --container-padding: var(--space-4);
      --grid-gap-loose: var(--space-4);
      --grid-gap-wide: var(--space-6);
    }
  }

  @media (max-width: 480px) {
    :root {
      --font-display: 2rem;
      --font-h1: 1.75rem;
      --font-h2: 1.5rem;
      --font-h3: 1.25rem;
      --font-h4: 1.125rem;
      --font-h5: 1rem;
      --font-h6: 0.875rem;

      /* Adjust spacing for mobile */
      --card-padding: var(--space-3);
      --container-padding: var(--space-3);
      --grid-gap-default: var(--space-3);
      --grid-gap-loose: var(--space-4);
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Update heading styles */
  h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-headings);
    letter-spacing: var(--letter-spacing-headings);
    margin-top: 0;
    margin-bottom: 0.5em;
  }

  h1 {
    font-size: var(--font-h1);
    font-weight: var(--font-weight-bold);
  }

  h2 {
    font-size: var(--font-h2);
  }

  h3 {
    font-size: var(--font-h3);
  }

  h4 {
    font-size: var(--font-h4);
  }

  h5 {
    font-size: var(--font-h5);
  }

  h6 {
    font-size: var(--font-h6);
  }

  p {
    margin-top: 0;
    margin-bottom: 1em;
    line-height: var(--line-height-body);
  }

  small {
    font-size: var(--font-body-sm);
  }

  /* Custom transition classes */
  .transition-all-200 {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-all-300 {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-transform-300 {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Glassmorphism */
  .glass {
    @apply bg-white/80 backdrop-blur-lg dark:bg-black/20;
  }

  /* Dark mode specific glass effect */
  .dark .glass {
    @apply bg-black/20 backdrop-blur-lg;
  }

  /* Interactive elements */
  .interactive {
    @apply transition-all duration-200 hover:scale-[1.02] active:scale-[0.98];
  }

  /* Page transitions */
  .page-enter {
    @apply opacity-0;
  }

  .page-enter-active {
    @apply opacity-100 transition-all duration-300;
  }

  .page-exit {
    @apply opacity-100;
  }

  .page-exit-active {
    @apply opacity-0 transition-all duration-300;
  }
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full dark:bg-muted-foreground/40;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50 dark:bg-muted-foreground/60;
}

/* Dark mode image adjustments */
.dark img:not([class*="no-dark-filter"]) {
  filter: brightness(0.9) contrast(1.1);
}

/* Dark mode input placeholder styles */
.dark input::placeholder,
.dark textarea::placeholder {
  @apply text-muted-foreground/50;
}

/* Dark mode focus styles */
.dark *:focus-visible {
  @apply ring-offset-background;
}

/* Typography enhancements */
h1, h2, h3, h4, h5, h6 {
  @apply font-medium leading-tight tracking-tight;
}

/* Status and Priority Utilities */
@layer utilities {
  /* Typography utility classes */
  .text-display {
    font-size: var(--font-display);
    line-height: 1.1;
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing-headings);
  }

  .text-h1 {
    font-size: var(--font-h1);
    line-height: 1.2;
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing-headings);
  }

  .text-h2 {
    font-size: var(--font-h2);
    line-height: 1.2;
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-headings);
  }

  .text-h3 {
    font-size: var(--font-h3);
    line-height: 1.3;
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-headings);
  }

  .text-h4 {
    font-size: var(--font-h4);
    line-height: 1.4;
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-headings);
  }

  .text-h5 {
    font-size: var(--font-h5);
    line-height: 1.4;
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-headings);
  }

  .text-h6 {
    font-size: var(--font-h6);
    line-height: 1.5;
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-headings);
  }

  .text-body-lg {
    font-size: var(--font-body-lg);
    line-height: var(--line-height-body);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing-body);
  }

  .text-body {
    font-size: var(--font-body);
    line-height: var(--line-height-body);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing-body);
  }

  .text-body-sm {
    font-size: var(--font-body-sm);
    line-height: var(--line-height-body);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing-body);
  }

  .text-caption {
    font-size: var(--font-caption);
    line-height: var(--line-height-body);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing-body);
  }

  .text-all-caps {
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-caps);
    font-weight: var(--font-weight-medium);
  }

  /* Spacing utility classes */
  .p-card {
    padding: var(--card-padding);
  }

  .px-card {
    padding-left: var(--card-padding);
    padding-right: var(--card-padding);
  }

  .py-card {
    padding-top: var(--card-padding);
    padding-bottom: var(--card-padding);
  }

  .p-button {
    padding: var(--button-padding-y) var(--button-padding-x);
  }

  .p-input {
    padding: var(--input-padding-y) var(--input-padding-x);
  }

  .p-nav-item {
    padding: var(--nav-item-padding-y) var(--nav-item-padding-x);
  }

  .gap-card {
    gap: var(--card-gap);
  }

  .gap-tight {
    gap: var(--grid-gap-tight);
  }

  .gap-default {
    gap: var(--grid-gap-default);
  }

  .gap-loose {
    gap: var(--grid-gap-loose);
  }

  .gap-wide {
    gap: var(--grid-gap-wide);
  }

  .mb-form-group {
    margin-bottom: var(--form-group-margin);
  }

  /* Status colors */
  .bg-status-success {
    background-color: hsl(var(--success));
  }
  .bg-status-warning {
    background-color: hsl(var(--warning));
  }
  .bg-status-destructive {
    background-color: hsl(var(--destructive));
  }
  .bg-status-info {
    background-color: hsl(var(--info));
  }

  /* Status subtle backgrounds */
  .bg-status-success-subtle {
    background-color: hsl(var(--success-subtle));
  }
  .bg-status-warning-subtle {
    background-color: hsl(var(--warning-subtle));
  }
  .bg-status-destructive-subtle {
    background-color: hsl(var(--destructive-subtle));
  }
  .bg-status-info-subtle {
    background-color: hsl(var(--info-subtle));
  }

  /* Priority colors */
  .bg-priority-high {
    background-color: hsl(var(--priority-high));
  }
  .bg-priority-medium {
    background-color: hsl(var(--priority-medium));
  }
  .bg-priority-low {
    background-color: hsl(var(--priority-low));
  }

  /* Text colors */
  .text-status-success {
    color: hsl(var(--success));
  }
  .text-status-warning {
    color: hsl(var(--warning));
  }
  .text-status-destructive {
    color: hsl(var(--destructive));
  }
  .text-status-info {
    color: hsl(var(--info));
  }

  /* Priority text colors */
  .text-priority-high {
    color: hsl(var(--priority-high));
  }
  .text-priority-medium {
    color: hsl(var(--priority-medium));
  }
  .text-priority-low {
    color: hsl(var(--priority-low));
  }
}
