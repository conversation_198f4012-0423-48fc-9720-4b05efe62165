
const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const chalk = require('chalk');

/**
 * Recursively find all .tsx files in the frontend directory
 * @param {string} dir Directory to scan
 * @returns {string[]} Array of file paths
 */
function findTsxFiles(dir) {
  let results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      results = results.concat(findTsxFiles(filePath));
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      results.push(filePath);
    }
  }
  
  return results;
}

/**
 * Extract HTTP calls from a React component file
 * @param {string} filePath Path to the .tsx file
 * @returns {Array} Array of API call objects
 */
function extractApiCalls(filePath) {
  const sourceCode = fs.readFileSync(filePath, 'utf-8');
  const apiCalls = [];
  
  try {
    // Parse the file into an AST
    const ast = parser.parse(sourceCode, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript']
    });
    
    // Traverse the AST to find API calls
    traverse(ast, {
      // Look for axios calls
      CallExpression(path) {
        // Check for axios direct calls: axios.get(), axios.post(), etc.
        if (
          path.node.callee.type === 'MemberExpression' &&
          path.node.callee.object.name === 'axios'
        ) {
          addAxiosCall(path, apiCalls, filePath);
        }
        
        // Check for api.get(), api.post(), etc. (custom axios instance)
        if (
          path.node.callee.type === 'MemberExpression' &&
          path.node.callee.object.name === 'api'
        ) {
          addAxiosCall(path, apiCalls, filePath);
        }
        
        // Check for fetch API calls
        if (path.node.callee.name === 'fetch') {
          addFetchCall(path, apiCalls, filePath);
        }
      }
    });
    
    return apiCalls;
  } catch (error) {
    console.error(chalk.red(`Error parsing ${filePath}:`), error);
    return [];
  }
}

/**
 * Add an axios API call to the apiCalls array
 */
function addAxiosCall(path, apiCalls, filePath) {
  const method = path.node.callee.property.name;
  if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
    const urlArg = path.node.arguments[0];
    if (urlArg && urlArg.type === 'StringLiteral') {
      apiCalls.push({
        method: method.toUpperCase(),
        url: urlArg.value,
        payload: method !== 'get' && path.node.arguments[1] ? extractObjectShape(path.node.arguments[1]) : null,
        sourceFile: filePath
      });
    }
  }
}

/**
 * Add a fetch API call to the apiCalls array
 */
function addFetchCall(path, apiCalls, filePath) {
  const urlArg = path.node.arguments[0];
  if (urlArg) {
    const optionsArg = path.node.arguments[1];
    let method = 'GET';
    let payload = null;
    
    if (optionsArg && optionsArg.type === 'ObjectExpression') {
      // Extract method from fetch options
      const methodProp = optionsArg.properties.find(
        prop => prop.key.name === 'method'
      );
      if (methodProp && methodProp.value.type === 'StringLiteral') {
        method = methodProp.value.value;
      }
      
      // Extract body from fetch options
      const bodyProp = optionsArg.properties.find(
        prop => prop.key.name === 'body'
      );
      if (bodyProp) {
        payload = extractObjectShape(bodyProp.value);
      }
    }
    
    apiCalls.push({
      method,
      url: urlArg.type === 'StringLiteral' ? urlArg.value : 'dynamic-url',
      payload,
      sourceFile: filePath
    });
  }
}

/**
 * Extract the shape of an object from AST node
 * @param {Object} node AST node
 * @returns {Object|null} Object shape or null if not extractable
 */
function extractObjectShape(node) {
  if (!node) return null;
  
  // Handle direct object literals
  if (node.type === 'ObjectExpression') {
    const shape = {};
    for (const prop of node.properties) {
      if (prop.key.type === 'Identifier') {
        shape[prop.key.name] = getValueType(prop.value);
      }
    }
    return shape;
  }
  
  // Handle variables - we can't determine the exact shape
  if (node.type === 'Identifier') {
    return `Variable: ${node.name}`;
  }
  
  // Handle JSON.stringify() calls
  if (
    node.type === 'CallExpression' &&
    node.callee.type === 'MemberExpression' &&
    node.callee.object.name === 'JSON' &&
    node.callee.property.name === 'stringify'
  ) {
    return extractObjectShape(node.arguments[0]);
  }
  
  return 'Complex expression';
}

/**
 * Get the type of a value from AST node
 * @param {Object} node AST node
 * @returns {string} Type description
 */
function getValueType(node) {
  if (!node) return 'undefined';
  
  switch (node.type) {
    case 'StringLiteral':
      return `string: "${node.value}"`;
    case 'NumericLiteral':
      return `number: ${node.value}`;
    case 'BooleanLiteral':
      return `boolean: ${node.value}`;
    case 'NullLiteral':
      return 'null';
    case 'ObjectExpression':
      return extractObjectShape(node);
    case 'ArrayExpression':
      return 'array';
    case 'Identifier':
      return `variable: ${node.name}`;
    default:
      return node.type;
  }
}

/**
 * Helper function to infer schema name from URL
 * @param {string} url Endpoint URL
 * @returns {string} Inferred schema name
 */
function inferSchemaNameFromUrl(url) {
  // Remove leading/trailing slashes and query params
  const cleanUrl = url.replace(/^\/|\/$/g, '').split('?')[0];
  
  // Split into segments
  const segments = cleanUrl.split('/');
  
  // Try to find a good candidate for the schema name
  // Usually it's the last non-parameter segment
  for (let i = segments.length - 1; i >= 0; i--) {
    const segment = segments[i];
    if (!segment.includes(':')) {
      // Convert to singular and capitalize
      return segment
        .replace(/s$/, '') // Remove trailing 's'
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('');
    }
  }
  
  return 'Unknown';
}

module.exports = {
  findTsxFiles,
  extractApiCalls,
  inferSchemaNameFromUrl
};
