import React from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import Navbar from '@/components/Navbar';
import { getProject } from '@/api/projectsApi';
import { getTasks } from '@/api/tasksApi';
import {
  List, Calendar, BarChart, Kanban, PieChart, BarChart2,
  CheckCircle, AlertTriangle, Clock, Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import BackButton from '@/components/BackButton';
import {
  ChartCard,
  PieChartComponent
} from '@/components/charts';
import { motion } from 'framer-motion';

const ReportsView: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Fetch project
  const { data: project, isLoading: projectLoading } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => getProject(Number(projectId)),
    enabled: !!projectId
  });

  // Fetch tasks
  const { data: tasks = [], isLoading: tasksLoading } = useQuery({
    queryKey: ['tasks', projectId],
    queryFn: () => getTasks(Number(projectId)),
    enabled: !!projectId
  });

  const handleViewChange = (view: string) => {
    navigate(`/projects/${projectId}/${view}`);
  };

  // Calculate task statistics from tasks data
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter((task: any) => task.status === 'completed').length;
  const inProgressTasks = tasks.filter((task: any) => task.status === 'in-progress').length;
  const notStartedTasks = tasks.filter((task: any) => task.status === 'not-started').length;
  const atRiskTasks = tasks.filter((task: any) => task.status === 'at-risk').length;

  // Task priority breakdown
  const highPriorityTasks = tasks.filter((task: any) => task.priority === 'high').length;
  const mediumPriorityTasks = tasks.filter((task: any) => task.priority === 'medium').length;
  const lowPriorityTasks = tasks.filter((task: any) => task.priority === 'low').length;

  // Prepare chart data
  const statusDistributionData = [
    { name: 'To Do', value: notStartedTasks, color: '#3498db' },
    { name: 'In Progress', value: inProgressTasks, color: '#f39c12' },
    { name: 'Completed', value: completedTasks, color: '#2ecc71' },
    { name: 'At Risk', value: atRiskTasks, color: '#e74c3c' },
  ].filter(item => item.value > 0); // Only include statuses that have tasks

  const priorityDistributionData = [
    { name: 'High', value: highPriorityTasks, color: '#e74c3c' },
    { name: 'Medium', value: mediumPriorityTasks, color: '#f39c12' },
    { name: 'Low', value: lowPriorityTasks, color: '#3498db' },
  ].filter(item => item.value > 0); // Only include priorities that have tasks

  if (projectLoading || tasksLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">Loading project data...</p>
          </div>
        </main>
      </div>
    );
  }

  // Check if user has permission to view the project
  const canViewProject = hasProjectPermission(project, user, ProjectPermission.VIEW_PROJECT);

  if (!canViewProject) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">You don't have permission to view this project.</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate('/dashboard')}
              >
                Go to Dashboard
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="max-w-7xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <BackButton />
                <h1 className="text-3xl font-bold text-foreground">
                  {project ? project.name : 'Project Reports'}
                </h1>
              </div>
              <p className="text-muted-foreground mt-1">
                {project?.description || 'Manage your tasks and track progress'}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('board')}
              >
                <Kanban size={16} className="mr-1.5" />
                Kanban
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('list')}
              >
                <List size={16} className="mr-1.5" />
                List
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('calendar')}
              >
                <Calendar size={16} className="mr-1.5" />
                Calendar
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center"
              >
                <BarChart size={16} className="mr-1.5" />
                Reports
              </Button>
            </div>
          </div>
        </div>

        {/* Reports View */}
        <div className="space-y-4">
          <div className="flex items-center justify-center w-full max-w-2xl mx-auto border-b pb-2">
            <div className="flex items-center gap-1.5 px-4 py-2 border-b-2 border-primary font-medium">
              <PieChart className="h-4 w-4" />
              <span>Overview</span>
            </div>
          </div>

          {/* Overview Content */}
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <BarChart className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-muted-foreground">Total Tasks</p>
                        <h3 className="text-2xl font-bold">{totalTasks}</h3>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <Card className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-muted-foreground">Completed</p>
                        <h3 className="text-2xl font-bold">{completedTasks}</h3>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <Card className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center">
                        <Clock className="h-6 w-6 text-amber-600" />
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-muted-foreground">In Progress</p>
                        <h3 className="text-2xl font-bold">{inProgressTasks}</h3>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <Card className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                        <AlertTriangle className="h-6 w-6 text-red-600" />
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-muted-foreground">At Risk</p>
                        <h3 className="text-2xl font-bold">{atRiskTasks}</h3>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ChartCard
                title="Task Status Distribution"
                description="Breakdown of tasks by current status"
                icon={<PieChart className="h-5 w-5" />}
                isLoading={false}
                isError={false}
                insight="Shows the distribution of tasks across different status categories"
              >
                <PieChartComponent
                  data={statusDistributionData}
                  height={300}
                />
              </ChartCard>

              <ChartCard
                title="Task Priority Distribution"
                description="Breakdown of tasks by priority level"
                icon={<BarChart2 className="h-5 w-5" />}
                isLoading={false}
                isError={false}
                insight="Shows the distribution of tasks across different priority levels"
              >
                <PieChartComponent
                  data={priorityDistributionData}
                  height={300}
                />
              </ChartCard>
            </div>

            <ChartCard
              title="Recent Activity"
              description="Latest updates and changes"
              icon={<Activity className="h-5 w-5" />}
              fullWidth
            >
              {tasks.length > 0 ? (
                <div className="space-y-4">
                  {tasks
                    .sort((a: any, b: any) => new Date(b.updated_at || '').getTime() - new Date(a.updated_at || '').getTime())
                    .slice(0, 5)
                    .map((task: any) => (
                      <div key={task.id} className="flex items-start justify-between border-b border-border pb-3">
                        <div>
                          <h4 className="font-medium">{task.title}</h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Status: {task.status}
                          </p>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {task.updated_at && new Date(task.updated_at).toLocaleString()}
                        </div>
                      </div>
                    ))
                  }
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No tasks found for this project.
                </div>
              )}
            </ChartCard>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ReportsView;
