import { useState } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Workspace } from "@/entities/Workspace";
import { User } from "@/entities/User";
import { useTranslation } from "react-i18next";
import CreateProjectModal from "@/components/project/CreateProjectModal";

interface WorkspaceHeaderProps {
  currentWorkspace: Workspace | null;
  user: User | null;
  handleCreateProject?: () => void;
  onProjectCreated?: () => void;
}

const WorkspaceHeader: React.FC<WorkspaceHeaderProps> = ({
  currentWorkspace,
  user,
  onProjectCreated
}) => {
  const { t } = useTranslation();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Handle project creation button click
  const handleCreateProject = () => {
    setIsCreateModalOpen(true);
  };

  // Handle successful project creation
  const handleProjectCreated = () => {
    // Notify parent component
    if (onProjectCreated) {
      onProjectCreated();
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-foreground">{currentWorkspace?.name}</h1>
          <p className="text-muted-foreground mt-1">
            {t('workspace.welcome', 'Welcome back, {{name}}! Here\'s what\'s happening today.', { name: `${user?.first_name} ${user?.last_name}` })}
          </p>
        </div>
        <Button onClick={handleCreateProject} className="flex items-center gap-1">
          <Plus size={16} />
          Create New Project
        </Button>
      </div>

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleProjectCreated}
      />
    </div>
  );
};

export default WorkspaceHeader;