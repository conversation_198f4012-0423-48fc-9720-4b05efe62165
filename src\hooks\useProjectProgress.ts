import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Task } from '@/api/tasksApi';
import { Project } from '@/entities/Project';
import { calculateProjectProgress } from '@/utils/projectUtils';
import api from '@/api/api';

/**
 * Hook to manage project progress updates based on task changes
 */
export const useProjectProgress = () => {
  const queryClient = useQueryClient();

  /**
   * Update project progress on the server
   * 
   * @param projectId The project ID
   * @param tasks Optional array of tasks to calculate progress from
   */
  const updateProjectProgress = useCallback(async (
    projectId: number,
    tasks?: Task[]
  ) => {
    try {
      // Get the current project data from the cache
      const cachedProject = queryClient.getQueryData<Project>(['project', projectId]);
      
      // Calculate the new progress percentage
      const progressPercentage = calculateProjectProgress(cachedProject, tasks);
      
      // Call the API to update the project progress
      await api.post(`/projects/${projectId}/update-progress`, {
        completion_percentage: progressPercentage
      });
      
      // Invalidate the project and projects queries to refetch with updated data
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['starredProjects'] });
      
      return progressPercentage;
    } catch (error) {
      console.error('Error updating project progress:', error);
      throw error;
    }
  }, [queryClient]);

  /**
   * Update project progress after task creation
   * 
   * @param projectId The project ID
   * @param newTask The newly created task
   */
  const updateProgressAfterTaskCreation = useCallback(async (
    projectId: number,
    newTask: Task
  ) => {
    try {
      // Get the current project data from the cache
      const cachedProject = queryClient.getQueryData<Project>(['project', projectId]);
      
      if (!cachedProject) return;
      
      // Get all tasks for this project from the cache
      const cachedTasks = queryClient.getQueryData<Task[]>(['tasks', projectId]) || [];
      
      // Add the new task to the tasks array
      const updatedTasks = [...cachedTasks, newTask];
      
      // Update the project progress
      return await updateProjectProgress(projectId, updatedTasks);
    } catch (error) {
      console.error('Error updating project progress after task creation:', error);
    }
  }, [queryClient, updateProjectProgress]);

  /**
   * Update project progress after task update
   * 
   * @param projectId The project ID
   * @param updatedTask The updated task
   */
  const updateProgressAfterTaskUpdate = useCallback(async (
    projectId: number,
    updatedTask: Task
  ) => {
    try {
      // Get all tasks for this project from the cache
      const cachedTasks = queryClient.getQueryData<Task[]>(['tasks', projectId]) || [];
      
      // Update the task in the tasks array
      const updatedTasks = cachedTasks.map(task => 
        task.id === updatedTask.id ? updatedTask : task
      );
      
      // Update the project progress
      return await updateProjectProgress(projectId, updatedTasks);
    } catch (error) {
      console.error('Error updating project progress after task update:', error);
    }
  }, [queryClient, updateProjectProgress]);

  /**
   * Update project progress after task deletion
   * 
   * @param projectId The project ID
   * @param taskId The ID of the deleted task
   */
  const updateProgressAfterTaskDeletion = useCallback(async (
    projectId: number,
    taskId: number
  ) => {
    try {
      // Get all tasks for this project from the cache
      const cachedTasks = queryClient.getQueryData<Task[]>(['tasks', projectId]) || [];
      
      // Remove the deleted task from the tasks array
      const updatedTasks = cachedTasks.filter(task => task.id !== taskId);
      
      // Update the project progress
      return await updateProjectProgress(projectId, updatedTasks);
    } catch (error) {
      console.error('Error updating project progress after task deletion:', error);
    }
  }, [queryClient, updateProjectProgress]);

  return {
    updateProjectProgress,
    updateProgressAfterTaskCreation,
    updateProgressAfterTaskUpdate,
    updateProgressAfterTaskDeletion
  };
};
