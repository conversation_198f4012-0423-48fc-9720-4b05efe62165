
import { toast } from "@/hooks/use-toast";

export const handleApiError = (error: any, title: string, description: string, throwError = true) => {
  console.error(error);
  toast({
    title,
    description,
    variant: "destructive"
  });

  if (throwError) {
    throw error;
  }

  // Return an object with the error message so the component can handle it
  return {
    error: true,
    message: description,
    originalError: error
  };
};
