
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User as LucideUser, Mail, Phone, Briefcase, Search, Plus, X, FolderKanban, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { getAllUsers, getTeamMembers, ProjectMembers } from '@/api/usersApi';
import { searchUserByEmail } from '@/api/usersApi';
import { User } from '@/entities/User';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getFullImageUrl } from '@/utils/imageUtils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Loader from '@/components/Loader';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useWorkspace } from '@/context/WorkspaceContext';
import { sendWorkspaceInvitation } from '@/api/workspaceInvitationsApi';

const TeamPage: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { currentWorkspace } = useWorkspace();
  const navigate = useNavigate();
  const [teamMembersByProject, setTeamMembersByProject] = useState<{[projectId: string]: ProjectMembers}>({});
  const [allTeamMembers, setAllTeamMembers] = useState<User[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Invite member dialog state
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('editor');
  const [isInviting, setIsInviting] = useState(false);
  const [inviteError, setInviteError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        setIsLoading(true);

        // Get team members grouped by project
        const membersByProject = await getTeamMembers();
        setTeamMembersByProject(membersByProject);

        // Create a unique list of all team members across all projects
        const uniqueMembers = new Map<number, User>();

        // Add current user to the list
        if (user) {
          uniqueMembers.set(user.id, user);
        }

        // Add all project members
        Object.values(membersByProject).forEach(projectData => {
          projectData.members.forEach(member => {
            uniqueMembers.set(member.id, member);
          });
        });

        const allMembers = Array.from(uniqueMembers.values());
        setAllTeamMembers(allMembers);
        setFilteredMembers(allMembers);

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching team members:', err);
        setError('Failed to load team members. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, [user]);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredMembers(allTeamMembers);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = allTeamMembers.filter(member =>
        (member.name?.toLowerCase().includes(query)) ||
        (member.email.toLowerCase().includes(query)) ||
        (member.job_title?.toLowerCase().includes(query))
      );
      setFilteredMembers(filtered);
    }
  }, [searchQuery, allTeamMembers]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleInvite = async () => {
    if (!inviteEmail.trim()) {
      setInviteError('Please enter an email address');
      return;
    }

    if (!currentWorkspace) {
      setInviteError('No active workspace selected');
      return;
    }

    setIsInviting(true);
    setInviteError(null);

    try {
      // Map role names to role IDs
      const roleIdMap = {
        'admin': 1,
        'editor': 2,
        'viewer': 3
      };

      const roleId = roleIdMap[inviteRole as keyof typeof roleIdMap];

      // Send the invitation through the API
      const response = await sendWorkspaceInvitation(
        currentWorkspace.id,
        inviteEmail,
        roleId
      );

      // Check if the user exists (this info comes from the backend)
      const userExists = response.user_exists;

      // If the user exists, we can add them to the local list for immediate UI update
      if (userExists) {
        try {
          const foundUser = await searchUserByEmail(inviteEmail);
          if (foundUser) {
            // Check if user is already in the list
            const isAlreadyMember = allTeamMembers.some(member => member.id === foundUser.id);

            if (!isAlreadyMember) {
              // Add the user to the team members list
              const updatedMembers = [...allTeamMembers, foundUser];
              setAllTeamMembers(updatedMembers);
              setFilteredMembers(updatedMembers);
            }
          }
        } catch (error) {
          console.error('Error fetching user details:', error);
          // Continue with the invitation process even if we can't update the UI immediately
        }
      }

      // Close the dialog and show success message
      setInviteDialogOpen(false);
      setInviteEmail('');
      setInviteRole('editor');

      toast({
        title: "Invitation sent",
        description: userExists
          ? `An invitation has been sent to ${inviteEmail}. They will be added to the workspace once they accept.`
          : `An invitation has been sent to ${inviteEmail}. They will need to create an account and accept the invitation.`,
      });

      // Refresh the team members list
      const membersByProject = await getTeamMembers();
      setTeamMembersByProject(membersByProject);

    } catch (err: any) {
      console.error('Error inviting user:', err);

      // Improved error handling
      let errorMessage = "Failed to invite user. Please try again.";

      // Check for specific error cases
      if (err.response?.status === 403) {
        errorMessage = "You don't have permission to invite members to this workspace.";
      } else if (err.response?.status === 409) {
        errorMessage = "An invitation has already been sent to this email address.";
      } else if (err.message) {
        errorMessage = err.message;
      }

      setInviteError(errorMessage);
    } finally {
      setIsInviting(false);
    }
  };

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase();
  };

  // Function to get a role if none is provided
  const getRole = (member: User) => {
    if (member.job_title) return member.job_title;

    // If the current user, return "Admin"
    if (member.id === user?.id) return "Admin";

    // Otherwise return "Team Member"
    return "Team Member";
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 px-2 h-8"
                onClick={() => {
                  // Navigate to the workspace page if we have a current workspace
                  if (currentWorkspace && currentWorkspace.id) {
                    navigate(`/workspace/${currentWorkspace.id}`);
                  } else {
                    // Fallback to dashboard if no workspace is available
                    navigate('/dashboard');
                  }
                }}
              >
                <ArrowLeft size={16} />
                Back to Workspace
              </Button>
            </div>
            <h1 className="text-3xl font-bold text-foreground">Team Members</h1>
            <p className="text-muted-foreground mt-1">
              Manage and view your team members
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search team members..."
                className="pl-9 w-full sm:w-[250px]"
                value={searchQuery}
                onChange={handleSearch}
              />
            </div>

            <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
              <DialogTrigger asChild>
                <Button disabled={!currentWorkspace}>
                  <Plus className="h-4 w-4 mr-2" />
                  Invite Member
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Invite Team Member</DialogTitle>
                  <DialogDescription>
                    {currentWorkspace
                      ? `Invite someone to join the "${currentWorkspace.name}" workspace.`
                      : "Please select a workspace first to invite members."}
                  </DialogDescription>
                </DialogHeader>
                {currentWorkspace ? (
                  <>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={inviteEmail}
                          onChange={(e) => setInviteEmail(e.target.value)}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="role">Role</Label>
                        <Select value={inviteRole} onValueChange={setInviteRole}>
                          <SelectTrigger id="role">
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="admin">Admin</SelectItem>
                            <SelectItem value="editor">Editor</SelectItem>
                            <SelectItem value="viewer">Viewer</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      {inviteError && (
                        <div className="text-sm text-red-500">{inviteError}</div>
                      )}
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setInviteDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleInvite} disabled={isInviting}>
                        {isInviting ? <Loader className="mr-2 h-4 w-4" /> : null}
                        {isInviting ? "Inviting..." : "Send Invitation"}
                      </Button>
                    </DialogFooter>
                  </>
                ) : (
                  <div className="py-4">
                    <p className="text-center text-muted-foreground">
                      You need to select a workspace before you can invite team members.
                    </p>
                    <div className="mt-4 flex justify-center">
                      <Button variant="outline" onClick={() => setInviteDialogOpen(false)}>
                        Close
                      </Button>
                    </div>
                  </div>
                )}
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader />
          </div>
        ) : error ? (
          <div className="text-center py-10 text-red-500">
            <p>{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        ) : (
          <>
            {filteredMembers.length === 0 ? (
              <div className="text-center py-10 text-muted-foreground">
                <p>No team members found matching your search.</p>
                {searchQuery && (
                  <Button
                    variant="link"
                    className="mt-2"
                    onClick={() => setSearchQuery('')}
                  >
                    Clear Search
                  </Button>
                )}
              </div>
            ) : Object.keys(teamMembersByProject).length === 0 && filteredMembers.length === 1 && filteredMembers[0].id === user?.id ? (
              <div className="text-center py-10 text-muted-foreground">
                <p>You are not a member of any team projects yet.</p>
                <p className="mt-2">Join a project or create a new group project to see team members here.</p>
              </div>
            ) : (
              <div className="space-y-8">
                {/* Current user card always shown first */}
                {user && filteredMembers.some(member => member.id === user.id) && (
                  <div>
                    <h2 className="text-xl font-semibold mb-4">Your Profile</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <Card className="border border-primary/20 bg-primary/5 hover:shadow-md transition-shadow">
                        <CardHeader className="pb-2 flex flex-row items-center justify-between">
                          <div>
                            <CardTitle>{user.name}</CardTitle>
                            <CardDescription className="capitalize">
                              {getRole(user)}
                              <Badge variant="outline" className="ml-2 bg-primary/10">You</Badge>
                            </CardDescription>
                          </div>
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={getFullImageUrl(user.profile_picture)} alt={user.name} />
                            <AvatarFallback>{user.name ? getInitials(user.name) : '?'}</AvatarFallback>
                          </Avatar>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center text-sm text-muted-foreground mb-2">
                            <Mail className="mr-2 h-4 w-4" />
                            <span>{user.email}</span>
                          </div>
                          {user.phone && (
                            <div className="flex items-center text-sm text-muted-foreground mb-2">
                              <Phone className="mr-2 h-4 w-4" />
                              <span>{user.phone}</span>
                            </div>
                          )}
                          {user.job_title && (
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Briefcase className="mr-2 h-4 w-4" />
                              <span>{user.job_title}</span>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                )}

                {/* Team members grouped by project */}
                {Object.entries(teamMembersByProject).map(([projectId, projectData]) => {
                  // Get members for this project that match the search query
                  const projectMembers = projectData.members.filter(member =>
                    filteredMembers.some(filteredMember => filteredMember.id === member.id)
                  );

                  // Skip this project if no members match the search query
                  if (projectMembers.length === 0) return null;

                  return (
                    <div key={projectId}>
                      <h2 className="text-xl font-semibold mb-4 flex items-center">
                        <FolderKanban className="mr-2 h-5 w-5" />
                        {projectData.projectName} Team
                      </h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {projectMembers
                          .filter(member => member.id !== user?.id) // Filter out current user as we've already displayed them
                          .map((member) => (
                            <Card
                              key={`${projectId}-${member.id}`}
                              className="hover:shadow-md transition-shadow"
                            >
                              <CardHeader className="pb-2 flex flex-row items-center justify-between">
                                <div>
                                  <CardTitle>{member.name}</CardTitle>
                                  <CardDescription>{getRole(member)}</CardDescription>
                                </div>
                                <Avatar className="h-12 w-12">
                                  <AvatarImage src={getFullImageUrl(member.profile_picture)} alt={member.name} />
                                  <AvatarFallback>{member.name ? getInitials(member.name) : '?'}</AvatarFallback>
                                </Avatar>
                              </CardHeader>
                              <CardContent>
                                <div className="flex items-center text-sm text-muted-foreground mb-2">
                                  <Mail className="mr-2 h-4 w-4" />
                                  <span>{member.email}</span>
                                </div>
                                {member.phone && (
                                  <div className="flex items-center text-sm text-muted-foreground mb-2">
                                    <Phone className="mr-2 h-4 w-4" />
                                    <span>{member.phone}</span>
                                  </div>
                                )}
                                {member.job_title && (
                                  <div className="flex items-center text-sm text-muted-foreground">
                                    <Briefcase className="mr-2 h-4 w-4" />
                                    <span>{member.job_title}</span>
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          ))
                        }
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}
      </main>
    </div>
  );
};

export default TeamPage;
