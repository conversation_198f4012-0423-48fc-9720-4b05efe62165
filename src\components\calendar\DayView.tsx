import React, { useState } from 'react';
import { format, addHours, isSameDay, parseISO } from 'date-fns';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { CalendarEvent } from '@/api/calendarApi';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { rescheduleEvent } from '@/api/calendarApi';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

// Define the time slots for the day view (30-minute intervals)
const timeSlots = Array.from({ length: 48 }, (_, i) => {
  const hour = Math.floor(i / 2);
  const minute = i % 2 === 0 ? 0 : 30;
  return { hour, minute };
});

// Map event types to colors with dark mode support
const typeColors: Record<string, string> = {
  task: 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900/30 dark:border-blue-800 dark:text-blue-300',
  meeting: 'bg-purple-50 border-purple-200 text-purple-700 dark:bg-purple-900/30 dark:border-purple-800 dark:text-purple-300',
  deadline: 'bg-red-50 border-red-200 text-red-700 dark:bg-red-900/30 dark:border-red-800 dark:text-red-300',
  milestone: 'bg-green-50 border-green-200 text-green-700 dark:bg-green-900/30 dark:border-green-800 dark:text-green-300',
};

interface DayViewProps {
  date: Date;
  events: CalendarEvent[];
  onDateChange: (date: Date) => void;
  onEventClick: (eventId: string | number) => void;
}

const DayView: React.FC<DayViewProps> = ({ date, events, onDateChange, onEventClick }) => {
  const { toast } = useToast();
  const [draggingEventId, setDraggingEventId] = useState<string | null>(null);

  // Filter events for the selected day
  const dayEvents = events.filter(event => isSameDay(new Date(event.date), date));

  // Get the time slot for an event
  const getEventTimeSlot = (event: CalendarEvent) => {
    const eventDate = new Date(event.date);
    const hour = eventDate.getHours();
    const minute = eventDate.getMinutes();
    return hour * 2 + (minute >= 30 ? 1 : 0);
  };

  // Get the duration of an event in 30-minute slots
  const getEventDuration = (event: CalendarEvent) => {
    if (!event.endDate) return 1; // Default to 30 minutes if no end date

    const startDate = new Date(event.date);
    const endDate = new Date(event.endDate);

    // If not the same day, only show until end of day
    if (!isSameDay(startDate, endDate)) {
      const endOfDay = new Date(startDate);
      endOfDay.setHours(23, 59, 59);
      return Math.ceil((endOfDay.getTime() - startDate.getTime()) / (30 * 60 * 1000));
    }

    return Math.ceil((endDate.getTime() - startDate.getTime()) / (30 * 60 * 1000));
  };

  // Handle drag end event
  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If there's no destination or the item was dropped back in its original position
    if (!destination ||
        (destination.droppableId === source.droppableId &&
         destination.index === source.index)) {
      setDraggingEventId(null);
      return;
    }

    // Extract the event ID from the draggableId (format: event-{id})
    const eventId = draggableId.split('-')[1];

    // Find the event that was dragged
    const draggedEvent = events.find(event => event.id.toString() === eventId);

    if (!draggedEvent) {
      console.error('Could not find the dragged event');
      setDraggingEventId(null);
      return;
    }

    // Calculate the new time based on the destination slot
    const slotIndex = parseInt(destination.droppableId.split('-')[1]);
    const hour = Math.floor(slotIndex / 2);
    const minute = slotIndex % 2 === 0 ? 0 : 30;

    // Create a new date with the same day but updated time
    const newDate = new Date(date);
    newDate.setHours(hour, minute, 0, 0);

    // Calculate the new end date if the event has a duration
    let newEndDate = undefined;
    if (draggedEvent.endDate) {
      const duration = getEventDuration(draggedEvent);
      newEndDate = new Date(newDate);
      newEndDate.setMinutes(newEndDate.getMinutes() + duration * 30);
    }

    // Format the dates for the API
    const formattedStartDate = format(newDate, 'yyyy-MM-dd HH:mm:ss');
    const formattedEndDate = newEndDate ? format(newEndDate, 'yyyy-MM-dd HH:mm:ss') : undefined;

    // Call the API to update the event time
    rescheduleEvent(eventId, formattedStartDate, formattedEndDate)
      .then(() => {
        toast({
          title: "Event rescheduled",
          description: `"${draggedEvent.title}" has been moved to ${format(newDate, 'h:mm a')}`,
        });
      })
      .catch(error => {
        toast({
          title: "Error",
          description: "Failed to reschedule the event. Please try again.",
          variant: "destructive"
        });
      })
      .finally(() => {
        setDraggingEventId(null);
      });
  };

  // Navigate to previous day
  const goToPreviousDay = () => {
    const previousDay = new Date(date);
    previousDay.setDate(previousDay.getDate() - 1);
    onDateChange(previousDay);
  };

  // Navigate to next day
  const goToNextDay = () => {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    onDateChange(nextDay);
  };

  return (
    <div className="bg-card text-card-foreground rounded-lg shadow dark:shadow-md dark:shadow-black/10">
      {/* Day navigation */}
      <div className="flex justify-between items-center p-4 border-b border-border">
        <Button variant="outline" size="sm" onClick={goToPreviousDay}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous
        </Button>
        <h2 className="text-xl font-semibold">{format(date, 'EEEE, MMMM d, yyyy')}</h2>
        <Button variant="outline" size="sm" onClick={goToNextDay}>
          Next
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>

      {/* Time slots */}
      <div className="overflow-y-auto max-h-[calc(100vh-200px)] p-4">
        <DragDropContext onDragEnd={handleDragEnd} onDragStart={(start) => setDraggingEventId(start.draggableId.split('-')[1])}>
          <div className="grid grid-cols-[80px_1fr] gap-2">
            {/* Time labels */}
            <div className="space-y-4">
              {timeSlots.filter((_, i) => i % 2 === 0).map((slot) => (
                <div key={`label-${slot.hour}`} className="h-16 flex items-center justify-end pr-2 text-sm text-muted-foreground">
                  {format(new Date().setHours(slot.hour, 0, 0, 0), 'h a')}
                </div>
              ))}
            </div>

            {/* Time slots with events */}
            <div className="space-y-2">
              {timeSlots.map((slot, index) => {
                const slotTime = new Date(date);
                slotTime.setHours(slot.hour, slot.minute, 0, 0);

                // Find events that start at this time slot
                const slotEvents = dayEvents.filter(event => {
                  const eventSlot = getEventTimeSlot(event);
                  return eventSlot === index;
                });

                return (
                  <Droppable droppableId={`slot-${index}`} key={`slot-${index}`}>
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className={cn(
                          "h-8 border-l-2 pl-2",
                          slot.minute === 0
                            ? "border-gray-300 dark:border-gray-600"
                            : "border-gray-200 dark:border-gray-700",
                          snapshot.isDraggingOver
                            ? "bg-blue-50 dark:bg-blue-900/20"
                            : ""
                        )}
                      >
                        {/* Time label for each slot */}
                        <div className="text-xs text-muted-foreground">
                          {slot.minute === 0 && (
                            <span>{format(slotTime, 'h:mm a')}</span>
                          )}
                        </div>

                        {/* Events in this slot */}
                        {slotEvents.map((event, eventIndex) => (
                          <Draggable
                            key={`event-${event.id}`}
                            draggableId={`event-${event.id}`}
                            index={eventIndex}
                          >
                            {(provided, snapshot) => {
                              const duration = getEventDuration(event);

                              return (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  className={cn(
                                    "px-2 py-1 rounded-md text-xs font-medium border mb-1 cursor-pointer",
                                    typeColors[event.type] || "bg-gray-50 border-gray-200 text-gray-700 dark:bg-gray-800/50 dark:border-gray-700 dark:text-gray-300",
                                    snapshot.isDragging ? "shadow-lg opacity-90" : "",
                                    `h-${Math.min(duration * 8, 32)}` // Limit height for very long events
                                  )}
                                  style={{
                                    ...provided.draggableProps.style,
                                    height: `${Math.min(duration * 8, 32)}px`,
                                  }}
                                  onClick={() => onEventClick(event.id)}
                                >
                                  <div className="flex items-center space-x-1">
                                    {event.priority === 'high' && (
                                      <span className="inline-block w-2 h-2 bg-red-500 dark:bg-red-400 rounded-full mr-1"></span>
                                    )}
                                    <span className="truncate">{event.title}</span>
                                  </div>
                                  {duration > 1 && (
                                    <div className="text-[10px] opacity-70 mt-1">
                                      {format(new Date(event.date), 'h:mm a')} -
                                      {event.endDate ? format(new Date(event.endDate), ' h:mm a') : ''}
                                    </div>
                                  )}
                                </div>
                              );
                            }}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                );
              })}
            </div>
          </div>
        </DragDropContext>
      </div>
    </div>
  );
};

export default DayView;
