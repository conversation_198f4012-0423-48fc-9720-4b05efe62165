import { AxiosRequestConfig, AxiosResponse } from 'axios';
import users from '@/mock-data/users.json';
import workspaces from '@/mock-data/workspaces.json';
import projects from '@/mock-data/projects.json';
import roles from '@/mock-data/roles.json';
import projectUsers from '@/mock-data/project-users.json';
import originalTasks from '@/mock-data/tasks.json';
import enhancedTasks from '@/mock-data/enhanced-tasks.json';
import lists from '@/mock-data/lists.json';
import chats from '@/mock-data/chats.json';
import activities from '@/mock-data/activities.json';
import { getLocalStorageData, setLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import { User, normalizeUser } from '@/entities/User';

// Combine original tasks with enhanced tasks, with enhanced tasks taking precedence
const tasks = [...originalTasks, ...enhancedTasks.filter(et =>
  !originalTasks.some(ot => ot.id === et.id && ot.project_id === et.project_id)
)];

// Helper function to simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to create a mock response
const createResponse = <T>(data: T, status = 200): AxiosResponse<T> => ({
  data,
  status,
  statusText: status === 200 ? 'OK' : 'Error',
  headers: {},
  config: {} as AxiosRequestConfig
});

// Mock API implementation
const mockApi = {
  // Authentication endpoints
  login: async (email: string, password: string) => {
    await delay(500); // Simulate network delay

    const user = users.find(u => u.email === email);

    if (!user || password !== 'password123') {
      throw {
        response: {
          status: 401,
          data: {
            message: 'Invalid email or password',
            type: 1
          }
        }
      };
    }

    const workspace = workspaces.find(w => w.user_id === user.id);

    if (!workspace) {
      throw {
        response: {
          status: 404,
          data: {
            message: 'No workspace found for this user',
            type: 1
          }
        }
      };
    }

    // Generate a mock token
    const token = `mock-token-${user.id}-${Date.now()}`;

    // Store user and token in localStorage
    setLocalStorageData('user', user);
    setLocalStorageData('token', token);
    setLocalStorageData('workspace', workspace);

    return createResponse({
      user,
      workspace,
      access_token: token
    });
  },

  logout: async () => {
    await delay(300);

    // Clear localStorage
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('workspace');

    return createResponse({ message: 'Logged out successfully' });
  },

  // User endpoints
  getUser: async () => {
    await delay(300);

    const userData = getLocalStorageData('user');

    if (!userData) {
      throw {
        response: {
          status: 401,
          data: {
            message: 'Unauthenticated',
            type: 1
          }
        }
      };
    }

    return createResponse(userData);
  },

  updateUser: async (userId: number, userData: Partial<User>) => {
    await delay(500);

    const currentUser = getLocalStorageData('user');

    if (!currentUser || currentUser.id !== userId) {
      throw {
        response: {
          status: 403,
          data: {
            message: 'Unauthorized to update this user',
            type: 1
          }
        }
      };
    }

    // Update user data
    const updatedUser = {
      ...currentUser,
      ...userData,
      updated_at: new Date().toISOString()
    };

    // Update in localStorage
    setLocalStorageData('user', updatedUser);

    return createResponse({
      user: updatedUser,
      message: 'User updated successfully'
    });
  },

  uploadProfilePicture: async (userId: number, file: File) => {
    await delay(1000); // Longer delay to simulate upload

    const currentUser = getLocalStorageData('user');

    if (!currentUser || currentUser.id !== userId) {
      throw {
        response: {
          status: 403,
          data: {
            message: 'Unauthorized to update this user',
            type: 1
          }
        }
      };
    }

    // Create a mock profile picture URL
    // In a real implementation, we would save the file to the public directory
    const fileName = `user-${userId}-${Date.now()}.jpg`;
    const profilePicturePath = `/assets/profile-pictures/${fileName}`;

    // Update user data
    const updatedUser = {
      ...currentUser,
      profile_picture: profilePicturePath,
      updated_at: new Date().toISOString()
    };

    // Update in localStorage
    setLocalStorageData('user', updatedUser);

    return createResponse({
      user: updatedUser,
      message: 'Profile picture updated successfully'
    });
  },

  // Workspace endpoints
  getWorkspaces: async () => {
    await delay(300);

    const currentUser = getLocalStorageData('user');

    if (!currentUser) {
      throw {
        response: {
          status: 401,
          data: {
            message: 'Unauthenticated',
            type: 1
          }
        }
      };
    }

    // Filter workspaces for the current user
    const userWorkspaces = workspaces.filter(w => w.user_id === currentUser.id);

    return createResponse(userWorkspaces);
  },

  getWorkspace: async (workspaceId: number) => {
    await delay(300);

    const workspace = workspaces.find(w => w.id === workspaceId);

    if (!workspace) {
      throw {
        response: {
          status: 404,
          data: {
            message: 'Workspace not found',
            type: 1
          }
        }
      };
    }

    // Get the workspace owner
    const owner = users.find(u => u.id === workspace.user_id);

    // Get projects for this workspace
    const workspaceProjects = projects.filter(p => p.workspace_id === workspaceId);

    // Get upcoming deadlines (projects ending soon)
    const currentDate = new Date();
    const twoWeeksFromNow = new Date();
    twoWeeksFromNow.setDate(currentDate.getDate() + 14);

    const upcomingDeadlines = workspaceProjects.filter(p => {
      if (!p.end_date) return false;
      const endDate = new Date(p.end_date);
      return endDate >= currentDate && endDate <= twoWeeksFromNow;
    });

    // Get workspace members (all users who have projects in this workspace)
    const projectIds = workspaceProjects.map(p => p.id);
    const memberIds = new Set<number>();

    projectUsers.forEach(pu => {
      if (projectIds.includes(pu.project_id)) {
        memberIds.add(pu.user_id);
      }
    });

    const members = Array.from(memberIds).map(id => users.find(u => u.id === id)).filter(Boolean);

    // Mock statistics
    const statistics = {
      total_projects: workspaceProjects.length,
      completed_projects: workspaceProjects.filter(p => p.progress === 3).length,
      in_progress_projects: workspaceProjects.filter(p => p.progress === 1 || p.progress === 2).length,
      not_started_projects: workspaceProjects.filter(p => p.progress === 0).length,
      total_members: members.length,
      upcoming_deadlines: upcomingDeadlines.length
    };

    // Create enhanced workspace object with owner
    const enhancedWorkspace = {
      ...workspace,
      owner: owner
    };

    return createResponse({
      workspace: enhancedWorkspace,
      projects: workspaceProjects,
      upcoming_deadlines: upcomingDeadlines,
      members: members,
      statistics: statistics,
      role: roles[0] // Assign admin role by default
    });
  },

  // Project endpoints
  getProjects: async (workspaceId: number) => {
    await delay(400);

    // Filter projects for the workspace
    const workspaceProjects = projects.filter(p => p.workspace_id === workspaceId);

    // Add members to each project
    const enhancedProjects = workspaceProjects.map(project => {
      // Get project members
      const projectUserEntries = projectUsers.filter(pu => pu.project_id === project.id);
      const members = projectUserEntries.map(pu => {
        const user = users.find(u => u.id === pu.user_id);
        const role = roles.find(r => r.id === pu.role_id);

        if (!user) return null;

        return {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          profile_picture: user.profile_picture,
          pivot: {
            role_id: pu.role_id,
            role_name: role ? role.name : 'Unknown'
          }
        };
      }).filter(Boolean);

      // Return enhanced project with members
      return {
        ...project,
        members
      };
    });

    return createResponse(enhancedProjects);
  },

  getAllProjects: async () => {
    await delay(400);

    // Add members to each project
    const enhancedProjects = projects.map(project => {
      // Get project members
      const projectUserEntries = projectUsers.filter(pu => pu.project_id === project.id);
      const members = projectUserEntries.map(pu => {
        const user = users.find(u => u.id === pu.user_id);
        const role = roles.find(r => r.id === pu.role_id);

        if (!user) return null;

        return {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          profile_picture: user.profile_picture,
          pivot: {
            role_id: pu.role_id,
            role_name: role ? role.name : 'Unknown'
          }
        };
      }).filter(Boolean);

      // Return enhanced project with members
      return {
        ...project,
        members
      };
    });

    return createResponse(enhancedProjects);
  },

  getProject: async (projectId: number | string) => {
    await delay(300);

    // Convert projectId to number if it's a string
    const id = typeof projectId === 'string' ? parseInt(projectId) : projectId;

    // Default to project ID 1 if the ID is invalid
    const validId = isNaN(id) || id <= 0 ? 1 : id;

    console.log(`[Mock API] Getting project with ID: ${validId}`);
    const project = projects.find(p => p.id === validId);

    if (!project) {
      console.log(`[Mock API] Project with ID ${projectId} not found`);
      throw {
        response: {
          status: 404,
          data: {
            message: 'Project not found',
            type: 1
          }
        }
      };
    }

    // Get project members
    const projectUserEntries = projectUsers.filter(pu => pu.project_id === projectId);
    const members = projectUserEntries.map(pu => {
      const user = users.find(u => u.id === pu.user_id);
      const role = roles.find(r => r.id === pu.role_id);

      if (!user) return null;

      return {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        profile_picture: user.profile_picture,
        pivot: {
          role_id: pu.role_id,
          role_name: role ? role.name : 'Unknown'
        }
      };
    }).filter(Boolean);

    // Create enhanced project with members
    const enhancedProject = {
      ...project,
      members
    };

    console.log(`[Mock API] Returning project with ${members.length} members`);
    return createResponse(enhancedProject);
  },

  // Project users endpoints
  getProjectUsers: async (projectId: number | string) => {
    await delay(400);

    // Convert projectId to number if it's a string
    const id = typeof projectId === 'string' ? parseInt(projectId) : projectId;

    // Default to project ID 1 if the ID is invalid
    const validId = isNaN(id) || id <= 0 ? 1 : id;

    console.log(`[Mock API] Getting users for project with ID: ${validId}`);

    // Check if project exists
    const project = projects.find(p => p.id === validId);

    if (!project) {
      console.log(`[Mock API] Project with ID ${validId} not found when fetching users`);
      throw {
        response: {
          status: 404,
          data: {
            message: 'Project not found',
            type: 1
          }
        }
      };
    }

    // Filter project users for the project
    const projectUserEntries = projectUsers.filter(pu => pu.project_id === validId);
    console.log(`[Mock API] Found ${projectUserEntries.length} user entries for project ${validId}`);

    // Get full user data for each project user
    const projectUsersWithData = projectUserEntries.map(pu => {
      const user = users.find(u => u.id === pu.user_id);
      const role = roles.find(r => r.id === pu.role_id);

      return {
        ...pu,
        user,
        role
      };
    });

    console.log(`[Mock API] Returning ${projectUsersWithData.length} users for project ${validId}`);
    return createResponse(projectUsersWithData);
  },

  // Task endpoints
  getAllTasks: async () => {
    await delay(300);

    const currentUser = getLocalStorageData('user');

    if (!currentUser) {
      throw {
        response: {
          status: 401,
          data: {
            message: 'Unauthenticated',
            type: 1
          }
        }
      };
    }

    // Get all tasks assigned to the current user
    const userTasks = tasks.filter(task => task.assigned_to === currentUser.id);

    // Enhance tasks with project data
    const enhancedTasks = userTasks.map(task => {
      const project = projects.find(p => p.id === task.project_id);
      return {
        ...task,
        // Map due_date to end_date to match the Task interface
        end_date: task.due_date,
        project: project ? {
          id: project.id,
          name: project.name,
          slug: project.slug
        } : null
      };
    });

    return createResponse(enhancedTasks);
  },

  getProjectTasks: async (projectId: number | string) => {
    await delay(300);

    // Convert projectId to number if it's a string
    const id = typeof projectId === 'string' ? parseInt(projectId) : projectId;

    // Default to project ID 1 if the ID is invalid
    const validId = isNaN(id) || id <= 0 ? 1 : id;

    console.log(`[Mock API] Getting tasks for project with ID: ${validId}`);

    // Check if project exists
    const project = projects.find(p => p.id === validId);

    if (!project) {
      console.log(`[Mock API] Project with ID ${projectId} not found when fetching tasks`);
      throw {
        response: {
          status: 404,
          data: {
            message: 'Project not found',
            type: 1
          }
        }
      };
    }

    // Get tasks for this project
    const projectTasks = tasks.filter(t => t.project_id === validId);

    // Enhance tasks with assignee data
    const enhancedTasks = projectTasks.map(task => {
      const assignee = users.find(u => u.id === task.assigned_to);
      return {
        ...task,
        // Map due_date to end_date to match the Task interface
        end_date: task.due_date,
        assignee: assignee ? {
          id: assignee.id,
          name: `${assignee.first_name} ${assignee.last_name}`,
          profile_picture: assignee.profile_picture
        } : null
      };
    });

    return createResponse(enhancedTasks);
  },

  getTask: async (taskId: number) => {
    await delay(200);

    const task = tasks.find(t => t.id === taskId);

    if (!task) {
      throw {
        response: {
          status: 404,
          data: {
            message: 'Task not found',
            type: 1
          }
        }
      };
    }

    // Get related data
    const project = projects.find(p => p.id === task.project_id);
    const assignee = users.find(u => u.id === task.assigned_to);

    // Create enhanced task object
    const enhancedTask = {
      ...task,
      // Map due_date to end_date to match the Task interface
      end_date: task.due_date,
      project: project ? {
        id: project.id,
        name: project.name,
        slug: project.slug
      } : null,
      assignee: assignee ? {
        id: assignee.id,
        name: `${assignee.first_name} ${assignee.last_name}`,
        profile_picture: assignee.profile_picture
      } : null
    };

    return createResponse(enhancedTask);
  },

  // List endpoints
  getProjectLists: async (projectId: number | string) => {
    await delay(300);

    // Convert projectId to number if it's a string
    const id = typeof projectId === 'string' ? parseInt(projectId) : projectId;

    // Default to project ID 1 if the ID is invalid
    const validId = isNaN(id) || id <= 0 ? 1 : id;

    console.log(`[Mock API] Getting lists for project with ID: ${validId}`);

    // Check if project exists
    const project = projects.find(p => p.id === validId);

    if (!project) {
      console.log(`[Mock API] Project with ID ${validId} not found when fetching lists`);
      throw {
        response: {
          status: 404,
          data: {
            message: 'Project not found',
            type: 1
          }
        }
      };
    }

    // Get lists for this project
    const projectLists = lists.filter(l => l.project_id === validId);
    console.log(`[Mock API] Found ${projectLists.length} lists for project ${validId}`);

    // Sort lists by position
    const sortedLists = [...projectLists].sort((a, b) => a.position - b.position);

    // For each list, get its tasks
    const listsWithTasks = sortedLists.map(list => {
      // First try to get tasks by list_id
      let listTasks = tasks
        .filter(t => t.project_id === validId && t.list_id === list.id);

      // If no tasks found by list_id, fall back to the old method of inferring by status
      if (listTasks.length === 0) {
        listTasks = tasks
          .filter(t => t.project_id === validId)
          .filter(t => {
            // Map task status to list
            if (list.title.toLowerCase().includes('to do') && t.status === 'to_do') return true;
            if (list.title.toLowerCase().includes('in progress') && t.status === 'in_progress') return true;
            if ((list.title.toLowerCase().includes('completed') || list.title.toLowerCase().includes('done')) && t.status === 'completed') return true;

            // For other lists, distribute tasks based on list position
            if (list.title.toLowerCase().includes('backlog') && t.status === 'to_do') return true;
            if (list.title.toLowerCase().includes('planning') && t.status === 'to_do') return true;
            if (list.title.toLowerCase().includes('design') && t.status === 'in_progress') return true;
            if (list.title.toLowerCase().includes('development') && t.status === 'in_progress') return true;
            if (list.title.toLowerCase().includes('testing') && t.status === 'in_progress') return true;
            if (list.title.toLowerCase().includes('review') && t.status === 'in_progress') return true;
            if (list.title.toLowerCase().includes('deployment') && t.status === 'completed') return true;
            if (list.title.toLowerCase().includes('release') && t.status === 'completed') return true;

            return false;
          });
      }

      // Enhance tasks with assignee data
      const enhancedTasks = listTasks.map(task => {
        const assignee = users.find(u => u.id === task.assigned_to);
        return {
          ...task,
          // Map due_date to end_date to match the Task interface
          end_date: task.due_date,
          assignee: assignee ? {
            id: assignee.id,
            name: `${assignee.first_name} ${assignee.last_name}`,
            profile_picture: assignee.profile_picture
          } : null
        };
      });

      return {
        ...list,
        tasks: enhancedTasks
      };
    });

    console.log(`[Mock API] Returning ${listsWithTasks.length} lists with tasks for project ${validId}`);
    return createResponse(listsWithTasks);
  },

  createList: async (listData: any) => {
    await delay(400);

    // Generate a new ID
    const newId = Math.max(...lists.map(l => l.id)) + 1;

    // Create the new list
    const newList = {
      id: newId,
      project_id: listData.project_id,
      title: listData.title,
      description: listData.description || '',
      position: listData.position || lists.filter(l => l.project_id === listData.project_id).length + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to lists array (in a real implementation, this would be persisted to a database)
    lists.push(newList as any);

    return createResponse(newList);
  },

  updateList: async (listId: number, listData: any) => {
    await delay(300);

    // Find the list
    const listIndex = lists.findIndex(l => l.id === listId);

    if (listIndex === -1) {
      throw {
        response: {
          status: 404,
          data: {
            message: 'List not found',
            type: 1
          }
        }
      };
    }

    // Update the list
    const updatedList = {
      ...lists[listIndex],
      ...listData,
      updated_at: new Date().toISOString()
    };

    // Update in the array (in a real implementation, this would update the database)
    lists[listIndex] = updatedList as any;

    return createResponse(updatedList);
  },

  deleteList: async (listId: number) => {
    await delay(300);

    // Find the list
    const listIndex = lists.findIndex(l => l.id === listId);

    if (listIndex === -1) {
      throw {
        response: {
          status: 404,
          data: {
            message: 'List not found',
            type: 1
          }
        }
      };
    }

    // Remove from the array (in a real implementation, this would delete from the database)
    lists.splice(listIndex, 1);

    return createResponse({ message: 'List deleted successfully' });
  },

  reorderLists: async (projectId: number, listIds: number[]) => {
    await delay(400);

    // Update positions for each list
    listIds.forEach((listId, index) => {
      const listIndex = lists.findIndex(l => l.id === listId);
      if (listIndex !== -1) {
        lists[listIndex] = {
          ...lists[listIndex],
          position: index + 1,
          updated_at: new Date().toISOString()
        } as any;
      }
    });

    return createResponse({ message: 'Lists reordered successfully' });
  },

  // Timeline endpoints
  getWorkspaceTimeline: async (workspaceId: number, startDate?: string, endDate?: string, status?: string) => {
    await delay(400);

    console.log(`[Mock API] Getting workspace timeline for workspace ${workspaceId}`);
    console.log(`[Mock API] Timeline params: startDate=${startDate}, endDate=${endDate}, status=${status}`);

    // Get projects for this workspace
    const workspaceProjects = projects.filter(p => p.workspace_id === workspaceId);
    console.log(`[Mock API] Found ${workspaceProjects.length} projects for workspace ${workspaceId}`);

    // Log the projects for debugging
    if (workspaceProjects.length > 0) {
      console.log('[Mock API] Projects found:', workspaceProjects.map(p => ({ id: p.id, name: p.name, start_date: p.start_date, end_date: p.end_date })));
    } else {
      console.log('[Mock API] No projects found for this workspace');
    }

    // Generate timeline items from projects and tasks
    const timelineItems = [];

    // Update project dates to be more current (2023 -> 2025)
    const updateDate = (dateStr) => {
      if (!dateStr) return null;
      const date = new Date(dateStr);
      date.setFullYear(2025); // Force year to be 2025
      return date.toISOString().split('T')[0];
    };

    // Add projects to timeline
    for (const project of workspaceProjects) {
      // Skip projects without dates
      if (!project.start_date || !project.end_date) {
        console.log(`[Mock API] Skipping project ${project.id} (${project.name}) - missing dates`);
        continue;
      }

      // Update dates to be more current
      const updatedStartDate = updateDate(project.start_date);
      const updatedEndDate = updateDate(project.end_date);

      console.log(`[Mock API] Project ${project.id} (${project.name}) dates: ${project.start_date} -> ${updatedStartDate}, ${project.end_date} -> ${updatedEndDate}`);

      // Skip projects that don't match the status filter
      if (status && project.progress) {
        const projectStatus = project.progress === 3 ? 'completed' :
                             project.progress > 0 ? 'in-progress' : 'not-started';
        if (status !== projectStatus) {
          console.log(`[Mock API] Skipping project ${project.id} (${project.name}) - status filter mismatch`);
          continue;
        }
      }

      // Get project owner
      const owner = users.find(u => u.id === project.user_id);

      // Add project to timeline
      timelineItems.push({
        id: `project-${project.id}`,
        type: 'project',
        title: project.name,
        start_date: updatedStartDate,
        end_date: updatedEndDate,
        status: project.progress === 3 ? 'completed' :
                project.progress > 0 ? 'in-progress' : 'not-started',
        priority: project.priority === 3 ? 'high' :
                 project.priority === 2 ? 'medium' : 'low',
        owner: owner ? {
          id: owner.id,
          name: `${owner.first_name} ${owner.last_name}`,
          avatar: owner.profile_picture
        } : undefined,
        color: '#4f46e5', // Default color for projects
        project_id: project.id
      });

      console.log(`[Mock API] Added project ${project.id} (${project.name}) to timeline`);

      // Get tasks for this project
      const projectTasks = tasks.filter(t => t.project_id === project.id);
      console.log(`[Mock API] Found ${projectTasks.length} tasks for project ${project.id}`);

      // Add tasks to timeline
      for (const task of projectTasks) {
        // Skip tasks without due dates
        if (!task.due_date) {
          console.log(`[Mock API] Skipping task ${task.id} (${task.title}) - no due date`);
          continue;
        }

        // Update dates to be more current
        const updatedDueDate = updateDate(task.due_date);

        // Calculate start date (1 week before due date if not specified)
        let taskStartDate;
        if (task.start_date) {
          taskStartDate = updateDate(task.start_date);
        } else {
          const dueDate = new Date(updatedDueDate);
          const startDate = new Date(dueDate);
          startDate.setDate(startDate.getDate() - 7); // 1 week before due date
          taskStartDate = startDate.toISOString().split('T')[0];
        }

        console.log(`[Mock API] Task ${task.id} (${task.title}) dates: start=${taskStartDate}, due=${updatedDueDate}`);

        // Skip tasks that don't match the status filter
        if (status && task.status) {
          const taskStatus = task.status === 'completed' ? 'completed' :
                            task.status === 'in_progress' ? 'in-progress' : 'not-started';
          if (status !== taskStatus) {
            console.log(`[Mock API] Skipping task ${task.id} (${task.title}) - status filter mismatch`);
            continue;
          }
        }

        // Get task assignee
        const assignee = users.find(u => u.id === task.assigned_to);

        // Add task to timeline
        timelineItems.push({
          id: `task-${task.id}`,
          type: 'task',
          title: task.title,
          start_date: taskStartDate,
          end_date: updatedDueDate,
          status: task.status === 'completed' ? 'completed' :
                 task.status === 'in_progress' ? 'in-progress' : 'not-started',
          priority: task.priority,
          assignee: assignee ? {
            id: assignee.id,
            name: `${assignee.first_name} ${assignee.last_name}`,
            avatar: assignee.profile_picture
          } : undefined,
          color: task.priority === 'high' ? '#ef4444' :
                task.priority === 'medium' ? '#f59e0b' : '#10b981',
          project_id: project.id,
          task_id: task.id
        });

        console.log(`[Mock API] Added task ${task.id} (${task.title}) to timeline`);
      }
    }

    // If no timeline items were generated, create some mock data
    if (timelineItems.length === 0) {
      console.log('[Mock API] No timeline items generated, creating mock data');

      // Create mock project timeline items
      const mockProjects = [
        {
          id: 'project-mock-1',
          type: 'project',
          title: 'Website Redesign',
          start_date: '2025-05-01',
          end_date: '2025-06-15',
          status: 'in-progress',
          priority: 'high',
          color: '#4f46e5',
          project_id: 1
        },
        {
          id: 'project-mock-2',
          type: 'project',
          title: 'Mobile App Development',
          start_date: '2025-05-10',
          end_date: '2025-07-20',
          status: 'not-started',
          priority: 'medium',
          color: '#4f46e5',
          project_id: 2
        },
        {
          id: 'project-mock-3',
          type: 'project',
          title: 'E-commerce Platform',
          start_date: '2025-04-15',
          end_date: '2025-06-30',
          status: 'completed',
          priority: 'low',
          color: '#4f46e5',
          project_id: 5
        }
      ];

      // Add mock tasks
      const mockTasks = [
        {
          id: 'task-mock-1',
          type: 'task',
          title: 'Design Homepage',
          start_date: '2025-05-01',
          end_date: '2025-05-10',
          status: 'completed',
          priority: 'high',
          color: '#ef4444',
          project_id: 1,
          task_id: 1
        },
        {
          id: 'task-mock-2',
          type: 'task',
          title: 'Implement User Authentication',
          start_date: '2025-05-11',
          end_date: '2025-05-20',
          status: 'in-progress',
          priority: 'medium',
          color: '#f59e0b',
          project_id: 1,
          task_id: 2
        },
        {
          id: 'task-mock-3',
          type: 'task',
          title: 'Create API Endpoints',
          start_date: '2025-05-15',
          end_date: '2025-05-30',
          status: 'not-started',
          priority: 'low',
          color: '#10b981',
          project_id: 2,
          task_id: 3
        }
      ];

      // Add mock data to timeline items
      timelineItems.push(...mockProjects, ...mockTasks);
      console.log(`[Mock API] Added ${mockProjects.length} mock projects and ${mockTasks.length} mock tasks`);
    }

    console.log(`[Mock API] Generated ${timelineItems.length} timeline items`);

    // Create the final result
    const result = {
      timeline_items: timelineItems,
      date_range: {
        start_date: startDate || new Date().toISOString().split('T')[0],
        end_date: endDate || new Date().toISOString().split('T')[0]
      }
    };

    console.log('[Mock API] Final timeline data:', result);

    return createResponse(result);
  },

  getProjectTimeline: async (projectId: number, startDate?: string, endDate?: string, status?: string, assigneeId?: number) => {
    await delay(400);

    console.log(`[Mock API] Getting project timeline for project ${projectId}`);
    console.log(`[Mock API] Timeline params: startDate=${startDate}, endDate=${endDate}, status=${status}, assigneeId=${assigneeId}`);

    // Get project
    const project = projects.find(p => p.id === projectId);

    if (!project) {
      console.log(`[Mock API] Project ${projectId} not found`);
      return createResponse({
        timeline_items: [],
        date_range: {
          start_date: startDate || new Date().toISOString().split('T')[0],
          end_date: endDate || new Date().toISOString().split('T')[0]
        }
      });
    }

    console.log(`[Mock API] Found project: ${project.id} (${project.name}), workspace_id: ${project.workspace_id}`);

    // Generate timeline items
    const timelineItems = [];

    // Update project dates to be more current (2023 -> 2025)
    const updateDate = (dateStr) => {
      if (!dateStr) return null;
      const date = new Date(dateStr);
      date.setFullYear(date.getFullYear() + 2); // Add 2 years to make dates current
      return date.toISOString().split('T')[0];
    };

    // Add project to timeline
    if (project.start_date && project.end_date) {
      // Update dates to be more current
      const updatedStartDate = updateDate(project.start_date);
      const updatedEndDate = updateDate(project.end_date);

      console.log(`[Mock API] Project ${project.id} (${project.name}) dates: ${project.start_date} -> ${updatedStartDate}, ${project.end_date} -> ${updatedEndDate}`);

      // Get project owner
      const owner = users.find(u => u.id === project.user_id);

      // Add project to timeline
      timelineItems.push({
        id: `project-${project.id}`,
        type: 'project',
        title: project.name,
        start_date: updatedStartDate,
        end_date: updatedEndDate,
        status: project.progress === 3 ? 'completed' :
                project.progress > 0 ? 'in-progress' : 'not-started',
        priority: project.priority === 3 ? 'high' :
                 project.priority === 2 ? 'medium' : 'low',
        owner: owner ? {
          id: owner.id,
          name: `${owner.first_name} ${owner.last_name}`,
          avatar: owner.profile_picture
        } : undefined,
        color: '#4f46e5', // Default color for projects
        project_id: project.id
      });

      console.log(`[Mock API] Added project ${project.id} (${project.name}) to timeline`);
    } else {
      console.log(`[Mock API] Project ${project.id} (${project.name}) has no start or end date`);
    }

    // Get tasks for this project
    const projectTasks = tasks.filter(t => t.project_id === projectId);
    console.log(`[Mock API] Found ${projectTasks.length} tasks for project ${projectId}`);

    // Add tasks to timeline
    for (const task of projectTasks) {
      // Skip tasks without due dates
      if (!task.due_date) {
        console.log(`[Mock API] Skipping task ${task.id} (${task.title}) - no due date`);
        continue;
      }

      // Update dates to be more current
      const updatedDueDate = updateDate(task.due_date);

      // Calculate start date (1 week before due date if not specified)
      let taskStartDate;
      if (task.start_date) {
        taskStartDate = updateDate(task.start_date);
      } else {
        const dueDate = new Date(updatedDueDate);
        const startDate = new Date(dueDate);
        startDate.setDate(startDate.getDate() - 7); // 1 week before due date
        taskStartDate = startDate.toISOString().split('T')[0];
      }

      console.log(`[Mock API] Task ${task.id} (${task.title}) dates: start=${taskStartDate}, due=${updatedDueDate}`);

      // Skip tasks that don't match the status filter
      if (status && task.status) {
        const taskStatus = task.status === 'completed' ? 'completed' :
                          task.status === 'in_progress' ? 'in-progress' : 'not-started';
        if (status !== taskStatus) {
          console.log(`[Mock API] Skipping task ${task.id} (${task.title}) - status filter mismatch`);
          continue;
        }
      }

      // Skip tasks that don't match the assignee filter
      if (assigneeId && task.assigned_to !== assigneeId) {
        console.log(`[Mock API] Skipping task ${task.id} (${task.title}) - assignee filter mismatch`);
        continue;
      }

      // Get task assignee
      const assignee = users.find(u => u.id === task.assigned_to);

      // Add task to timeline
      timelineItems.push({
        id: `task-${task.id}`,
        type: 'task',
        title: task.title,
        start_date: taskStartDate,
        end_date: updatedDueDate,
        status: task.status === 'completed' ? 'completed' :
               task.status === 'in_progress' ? 'in-progress' : 'not-started',
        priority: task.priority,
        assignee: assignee ? {
          id: assignee.id,
          name: `${assignee.first_name} ${assignee.last_name}`,
          avatar: assignee.profile_picture
        } : undefined,
        color: task.priority === 'high' ? '#ef4444' :
              task.priority === 'medium' ? '#f59e0b' : '#10b981',
        project_id: project.id,
        task_id: task.id
      });

      console.log(`[Mock API] Added task ${task.id} (${task.title}) to timeline`);
    }

    // Get team members for this project
    const projectUserEntries = projectUsers.filter(pu => pu.project_id === projectId);
    const teamMembers = projectUserEntries.map(pu => {
      const user = users.find(u => u.id === pu.user_id);
      if (!user) return null;

      return {
        id: user.id,
        name: `${user.first_name} ${user.last_name}`,
        avatar: user.profile_picture
      };
    }).filter(Boolean);

    console.log(`[Mock API] Found ${teamMembers.length} team members for project ${projectId}`);

    console.log(`[Mock API] Generated ${timelineItems.length} timeline items for project ${projectId}`);

    // Create the final result
    const result = {
      timeline_items: timelineItems,
      date_range: {
        start_date: startDate || new Date().toISOString().split('T')[0],
        end_date: endDate || new Date().toISOString().split('T')[0]
      },
      team_members: teamMembers
    };

    console.log('[Mock API] Final project timeline data:', result);

    return createResponse(result);
  },

  updateTimelineItem: async (itemId: string, startDate: string, endDate: string) => {
    await delay(300);

    console.log(`[Mock API] Updating timeline item ${itemId}`);
    console.log(`[Mock API] New dates: startDate=${startDate}, endDate=${endDate}`);

    // Parse the item ID to get the type and actual ID
    const [type, id] = itemId.split('-');
    const numericId = parseInt(id);

    if (type === 'project') {
      // Update project dates
      const projectIndex = projects.findIndex(p => p.id === numericId);

      if (projectIndex !== -1) {
        projects[projectIndex] = {
          ...projects[projectIndex],
          start_date: startDate,
          end_date: endDate,
          updated_at: new Date().toISOString()
        };
      }
    } else if (type === 'task') {
      // Update task dates
      const taskIndex = tasks.findIndex(t => t.id === numericId);

      if (taskIndex !== -1) {
        tasks[taskIndex] = {
          ...tasks[taskIndex],
          start_date: startDate,
          due_date: endDate,
          updated_at: new Date().toISOString()
        };
      }
    }

    return createResponse({
      item: {
        id: itemId,
        start_date: startDate,
        end_date: endDate
      }
    });
  },

  createMilestone: async (projectId: number, milestoneData: any) => {
    await delay(400);

    console.log(`[Mock API] Creating milestone for project ${projectId}`);
    console.log(`[Mock API] Milestone data:`, milestoneData);

    // Generate a new ID for the milestone
    const milestoneId = `milestone-${Date.now()}`;

    // Create the milestone
    const milestone = {
      id: milestoneId,
      type: 'milestone',
      title: milestoneData.title,
      start_date: milestoneData.date,
      end_date: milestoneData.date,
      status: 'not-started',
      description: milestoneData.description || '',
      color: '#8b5cf6', // Purple color for milestones
      project_id: projectId,
      list_id: milestoneData.list_id
    };

    return createResponse({
      milestone
    });
  },

  // Calendar endpoints
  getAllEvents: async (params: any) => {
    await delay(400);

    console.log('[Mock API] Getting all calendar events with params:', params);

    // Generate mock calendar events from tasks and projects
    const calendarEvents = [];

    // Add tasks as calendar events
    for (const task of tasks) {
      if (!task.due_date) continue;

      // Update dates to be more current (2023 -> 2025)
      const updateDate = (dateStr: string) => {
        if (!dateStr) return null;
        const date = new Date(dateStr);
        date.setFullYear(2025); // Force year to be 2025
        return date.toISOString().split('T')[0];
      };

      const dueDate = updateDate(task.due_date);
      const startDate = task.start_date ? updateDate(task.start_date) : null;

      // Get project for this task
      const project = projects.find(p => p.id === task.project_id);

      // Get assignee for this task
      const assignee = users.find(u => u.id === task.assigned_to);
      const assignees = assignee ? [{
        id: assignee.id,
        name: `${assignee.first_name} ${assignee.last_name}`,
        avatar: assignee.profile_picture
      }] : [];

      calendarEvents.push({
        id: `task_${task.id}`,
        title: task.title,
        description: task.description,
        start_date: startDate || dueDate, // Use start_date if available, otherwise use due_date
        end_date: dueDate,
        type: 'task',
        status: task.status || 'not-started',
        priority: task.priority || 'medium',
        project_id: task.project_id,
        project: project ? {
          id: project.id,
          name: project.name,
          slug: project.slug
        } : null,
        assignees,
        is_task: true
      });
    }

    // Add projects as calendar events
    for (const project of projects) {
      if (!project.start_date || !project.end_date) continue;

      // Update dates to be more current (2023 -> 2025)
      const updateDate = (dateStr: string) => {
        if (!dateStr) return null;
        const date = new Date(dateStr);
        date.setFullYear(2025); // Force year to be 2025
        return date.toISOString().split('T')[0];
      };

      const startDate = updateDate(project.start_date);
      const endDate = updateDate(project.end_date);

      // Get project owner
      const owner = users.find(u => u.id === project.user_id);
      const assignees = owner ? [{
        id: owner.id,
        name: `${owner.first_name} ${owner.last_name}`,
        avatar: owner.profile_picture
      }] : [];

      calendarEvents.push({
        id: project.id,
        title: project.name,
        description: project.description,
        start_date: startDate,
        end_date: endDate,
        type: 'milestone',
        status: project.progress === 3 ? 'completed' :
                project.progress > 0 ? 'in-progress' : 'not-started',
        priority: project.priority === 3 ? 'high' :
                 project.priority === 2 ? 'medium' : 'low',
        project_id: project.id,
        project: {
          id: project.id,
          name: project.name,
          slug: project.slug
        },
        assignees,
        is_task: false
      });
    }

    // Add some mock meetings
    const meetings = [
      {
        id: 'meeting_1',
        title: 'Team Standup',
        description: 'Daily team standup meeting',
        start_date: '2025-05-01T09:00:00',
        end_date: '2025-05-01T09:30:00',
        type: 'meeting',
        status: 'not-started',
        priority: 'medium',
        project_id: 1,
        project: projects.find(p => p.id === 1) ? {
          id: 1,
          name: projects.find(p => p.id === 1)?.name,
          slug: projects.find(p => p.id === 1)?.slug
        } : null,
        assignees: users.slice(0, 3).map(u => ({
          id: u.id,
          name: `${u.first_name} ${u.last_name}`,
          avatar: u.profile_picture
        })),
        is_task: false
      },
      {
        id: 'meeting_2',
        title: 'Project Review',
        description: 'Review project progress and next steps',
        start_date: '2025-05-05T14:00:00',
        end_date: '2025-05-05T15:30:00',
        type: 'meeting',
        status: 'not-started',
        priority: 'high',
        project_id: 2,
        project: projects.find(p => p.id === 2) ? {
          id: 2,
          name: projects.find(p => p.id === 2)?.name,
          slug: projects.find(p => p.id === 2)?.slug
        } : null,
        assignees: users.slice(0, 4).map(u => ({
          id: u.id,
          name: `${u.first_name} ${u.last_name}`,
          avatar: u.profile_picture
        })),
        is_task: false
      }
    ];

    calendarEvents.push(...meetings);

    // Add some mock deadlines
    const deadlines = [
      {
        id: 'deadline_1',
        title: 'Project Proposal Deadline',
        description: 'Submit project proposal to client',
        start_date: '2025-05-15T23:59:59',
        end_date: '2025-05-15T23:59:59',
        type: 'deadline',
        status: 'not-started',
        priority: 'high',
        project_id: 1,
        project: projects.find(p => p.id === 1) ? {
          id: 1,
          name: projects.find(p => p.id === 1)?.name,
          slug: projects.find(p => p.id === 1)?.slug
        } : null,
        assignees: [users[0]].map(u => ({
          id: u.id,
          name: `${u.first_name} ${u.last_name}`,
          avatar: u.profile_picture
        })),
        is_task: false
      },
      {
        id: 'deadline_2',
        title: 'Final Deliverable',
        description: 'Submit final deliverable to client',
        start_date: '2025-05-30T23:59:59',
        end_date: '2025-05-30T23:59:59',
        type: 'deadline',
        status: 'not-started',
        priority: 'high',
        project_id: 2,
        project: projects.find(p => p.id === 2) ? {
          id: 2,
          name: projects.find(p => p.id === 2)?.name,
          slug: projects.find(p => p.id === 2)?.slug
        } : null,
        assignees: users.slice(0, 2).map(u => ({
          id: u.id,
          name: `${u.first_name} ${u.last_name}`,
          avatar: u.profile_picture
        })),
        is_task: false
      }
    ];

    calendarEvents.push(...deadlines);

    // Filter events based on params
    let filteredEvents = [...calendarEvents];

    // Filter by date range
    if (params?.start_date && params?.end_date) {
      const startDate = new Date(params.start_date);
      const endDate = new Date(params.end_date);

      filteredEvents = filteredEvents.filter(event => {
        const eventStartDate = new Date(event.start_date);
        const eventEndDate = event.end_date ? new Date(event.end_date) : eventStartDate;

        // Event starts or ends within the date range, or spans the entire range
        return (eventStartDate >= startDate && eventStartDate <= endDate) ||
               (eventEndDate >= startDate && eventEndDate <= endDate) ||
               (eventStartDate <= startDate && eventEndDate >= endDate);
      });
    }

    // Filter by project
    if (params?.project_id) {
      filteredEvents = filteredEvents.filter(event =>
        event.project_id === parseInt(params.project_id)
      );
    }

    console.log(`[Mock API] Returning ${filteredEvents.length} calendar events`);
    return createResponse(filteredEvents);
  },

  getEvent: async (eventId: string | number) => {
    await delay(300);

    console.log(`[Mock API] Getting calendar event with ID: ${eventId}`);

    // Check if this is a task event (ID starts with 'task_')
    const isTaskEvent = typeof eventId === 'string' && eventId.startsWith('task_');

    if (isTaskEvent) {
      // Extract the task ID from the event ID
      const taskId = parseInt(eventId.replace('task_', ''));

      // Find the task
      const task = tasks.find(t => t.id === taskId);

      if (!task) {
        console.log(`[Mock API] Task with ID ${taskId} not found`);
        throw {
          response: {
            status: 404,
            data: {
              message: 'Event not found',
              type: 1
            }
          }
        };
      }

      // Update dates to be more current (2023 -> 2025)
      const updateDate = (dateStr: string) => {
        if (!dateStr) return null;
        const date = new Date(dateStr);
        date.setFullYear(2025); // Force year to be 2025
        return date.toISOString().split('T')[0];
      };

      const dueDate = updateDate(task.due_date);
      const startDate = task.start_date ? updateDate(task.start_date) : null;

      // Get project for this task
      const project = projects.find(p => p.id === task.project_id);

      // Get assignee for this task
      const assignee = users.find(u => u.id === task.assigned_to);
      const assignees = assignee ? [{
        id: assignee.id,
        name: `${assignee.first_name} ${assignee.last_name}`,
        avatar: assignee.profile_picture
      }] : [];

      // Create event from task
      const event = {
        id: eventId,
        title: task.title,
        description: task.description,
        start_date: startDate || dueDate, // Use start_date if available, otherwise use due_date
        end_date: dueDate,
        type: 'task',
        status: task.status || 'not-started',
        priority: task.priority || 'medium',
        project_id: task.project_id,
        project: project ? {
          id: project.id,
          name: project.name,
          slug: project.slug
        } : null,
        assignees,
        is_task: true
      };

      return createResponse(event);
    } else if (eventId === 'meeting_1' || eventId === 'meeting_2') {
      // Return mock meeting
      const isMeeting1 = eventId === 'meeting_1';

      const meeting = {
        id: eventId,
        title: isMeeting1 ? 'Team Standup' : 'Project Review',
        description: isMeeting1 ? 'Daily team standup meeting' : 'Review project progress and next steps',
        start_date: isMeeting1 ? '2025-05-01T09:00:00' : '2025-05-05T14:00:00',
        end_date: isMeeting1 ? '2025-05-01T09:30:00' : '2025-05-05T15:30:00',
        type: 'meeting',
        status: 'not-started',
        priority: isMeeting1 ? 'medium' : 'high',
        project_id: isMeeting1 ? 1 : 2,
        project: projects.find(p => p.id === (isMeeting1 ? 1 : 2)) ? {
          id: isMeeting1 ? 1 : 2,
          name: projects.find(p => p.id === (isMeeting1 ? 1 : 2))?.name,
          slug: projects.find(p => p.id === (isMeeting1 ? 1 : 2))?.slug
        } : null,
        assignees: users.slice(0, isMeeting1 ? 3 : 4).map(u => ({
          id: u.id,
          name: `${u.first_name} ${u.last_name}`,
          avatar: u.profile_picture
        })),
        is_task: false
      };

      return createResponse(meeting);
    } else if (eventId === 'deadline_1' || eventId === 'deadline_2') {
      // Return mock deadline
      const isDeadline1 = eventId === 'deadline_1';

      const deadline = {
        id: eventId,
        title: isDeadline1 ? 'Project Proposal Deadline' : 'Final Deliverable',
        description: isDeadline1 ? 'Submit project proposal to client' : 'Submit final deliverable to client',
        start_date: isDeadline1 ? '2025-05-15T23:59:59' : '2025-05-30T23:59:59',
        end_date: isDeadline1 ? '2025-05-15T23:59:59' : '2025-05-30T23:59:59',
        type: 'deadline',
        status: 'not-started',
        priority: 'high',
        project_id: isDeadline1 ? 1 : 2,
        project: projects.find(p => p.id === (isDeadline1 ? 1 : 2)) ? {
          id: isDeadline1 ? 1 : 2,
          name: projects.find(p => p.id === (isDeadline1 ? 1 : 2))?.name,
          slug: projects.find(p => p.id === (isDeadline1 ? 1 : 2))?.slug
        } : null,
        assignees: isDeadline1 ?
          [users[0]].map(u => ({
            id: u.id,
            name: `${u.first_name} ${u.last_name}`,
            avatar: u.profile_picture
          })) :
          users.slice(0, 2).map(u => ({
            id: u.id,
            name: `${u.first_name} ${u.last_name}`,
            avatar: u.profile_picture
          })),
        is_task: false
      };

      return createResponse(deadline);
    } else {
      // Try to find a project with this ID
      const projectId = typeof eventId === 'string' ? parseInt(eventId) : eventId;
      const project = projects.find(p => p.id === projectId);

      if (!project) {
        console.log(`[Mock API] Event with ID ${eventId} not found`);
        throw {
          response: {
            status: 404,
            data: {
              message: 'Event not found',
              type: 1
            }
          }
        };
      }

      // Update dates to be more current (2023 -> 2025)
      const updateDate = (dateStr: string) => {
        if (!dateStr) return null;
        const date = new Date(dateStr);
        date.setFullYear(2025); // Force year to be 2025
        return date.toISOString().split('T')[0];
      };

      const startDate = updateDate(project.start_date);
      const endDate = updateDate(project.end_date);

      // Get project owner
      const owner = users.find(u => u.id === project.user_id);
      const assignees = owner ? [{
        id: owner.id,
        name: `${owner.first_name} ${owner.last_name}`,
        avatar: owner.profile_picture
      }] : [];

      // Create event from project
      const event = {
        id: project.id,
        title: project.name,
        description: project.description,
        start_date: startDate,
        end_date: endDate,
        type: 'milestone',
        status: project.progress === 3 ? 'completed' :
                project.progress > 0 ? 'in-progress' : 'not-started',
        priority: project.priority === 3 ? 'high' :
                 project.priority === 2 ? 'medium' : 'low',
        project_id: project.id,
        project: {
          id: project.id,
          name: project.name,
          slug: project.slug
        },
        assignees,
        is_task: false
      };

      return createResponse(event);
    }
  },

  createEvent: async (eventData: any) => {
    await delay(500);

    console.log('[Mock API] Creating calendar event:', eventData);

    // Generate a new ID for the event
    const eventId = `event_${Date.now()}`;

    // Create the event
    const event = {
      id: eventId,
      ...eventData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return createResponse({
      event,
      message: 'Event created successfully'
    });
  },

  updateEvent: async (eventId: string | number, eventData: any) => {
    await delay(400);

    console.log(`[Mock API] Updating calendar event ${eventId}:`, eventData);

    // Create the updated event
    const event = {
      id: eventId,
      ...eventData,
      updated_at: new Date().toISOString()
    };

    return createResponse({
      event,
      message: 'Event updated successfully'
    });
  },

  // Chat endpoints
  getTaskChats: async (taskId: number) => {
    await delay(300);

    console.log(`[Mock API] Getting chats for task with ID: ${taskId}`);

    // Filter chats for the task
    const taskChats = chats.filter(chat => chat.task_id === taskId);

    return createResponse(taskChats);
  },

  createChat: async (chatData: { task_id: number; message: string; image?: File; description?: string }) => {
    await delay(400);

    console.log(`[Mock API] Creating chat for task ${chatData.task_id}:`, chatData);

    const currentUser = getLocalStorageData('user');

    if (!currentUser) {
      throw {
        response: {
          status: 401,
          data: {
            message: 'Unauthenticated',
            type: 1
          }
        }
      };
    }

    // Generate a new ID
    const newId = Math.max(...chats.map(c => c.id), 0) + 1;

    // Create the new chat
    const newChat = {
      id: newId,
      user_id: currentUser.id,
      task_id: chatData.task_id,
      message: chatData.message,
      description: chatData.description || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      user: {
        id: currentUser.id,
        first_name: currentUser.first_name,
        last_name: currentUser.last_name,
        email: currentUser.email,
        profile_picture: currentUser.profile_picture
      }
    };

    // Add to chats array (in a real implementation, this would be persisted to a database)
    chats.push(newChat as any);

    return createResponse(newChat);
  },

  deleteChat: async (chatId: number) => {
    await delay(300);

    console.log(`[Mock API] Deleting chat with ID: ${chatId}`);

    const currentUser = getLocalStorageData('user');

    if (!currentUser) {
      throw {
        response: {
          status: 401,
          data: {
            message: 'Unauthenticated',
            type: 1
          }
        }
      };
    }

    // Find the chat
    const chatIndex = chats.findIndex(c => c.id === chatId);

    if (chatIndex === -1) {
      throw {
        response: {
          status: 404,
          data: {
            message: 'Chat not found',
            type: 1
          }
        }
      };
    }

    // Check if the user is the owner of the chat
    if (chats[chatIndex].user_id !== currentUser.id) {
      throw {
        response: {
          status: 403,
          data: {
            message: 'Unauthorized to delete this chat',
            type: 1
          }
        }
      };
    }

    // Remove from chats array (in a real implementation, this would update the database)
    chats.splice(chatIndex, 1);

    return createResponse({ message: 'Chat deleted successfully' });
  },

  // Activity endpoints
  getRecentActivities: async (filters: {
    limit?: number;
    type?: string;
    from_date?: string;
    to_date?: string;
    sort_order?: 'asc' | 'desc';
  } = {}) => {
    await delay(300);

    console.log(`[Mock API] Getting recent activities with filters:`, filters);

    // Start with all activities
    let filteredActivities = [...activities];

    // Apply type filter if specified
    if (filters.type) {
      filteredActivities = filteredActivities.filter(activity => activity.type === filters.type);
    }

    // Apply date range filters if specified
    if (filters.from_date) {
      const fromDate = new Date(filters.from_date);
      filteredActivities = filteredActivities.filter(activity =>
        new Date(activity.created_at) >= fromDate
      );
    }

    if (filters.to_date) {
      const toDate = new Date(filters.to_date);
      // Add one day to include the end date fully
      toDate.setDate(toDate.getDate() + 1);
      filteredActivities = filteredActivities.filter(activity =>
        new Date(activity.created_at) < toDate
      );
    }

    // Apply sort order
    const sortOrder = filters.sort_order || 'desc';
    filteredActivities.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });

    // Apply limit
    if (filters.limit) {
      filteredActivities = filteredActivities.slice(0, filters.limit);
    }

    console.log(`[Mock API] Returning ${filteredActivities.length} activities`);
    return createResponse(filteredActivities);
  },

  // Team members endpoints
  getTeamMembers: async () => {
    await delay(400);

    console.log(`[Mock API] Getting team members`);

    const currentUser = getLocalStorageData('user');

    if (!currentUser) {
      throw {
        response: {
          status: 401,
          data: {
            message: 'Unauthenticated',
            type: 1
          }
        }
      };
    }

    // Get all projects the current user is in
    const userProjectIds = projectUsers
      .filter(pu => pu.user_id === currentUser.id)
      .map(pu => pu.project_id);

    console.log(`[Mock API] User is in ${userProjectIds.length} projects`);

    // Create a map to store users by project
    const teamMembersByProject: {[projectId: string]: {projectName: string; members: any[]}} = {};

    // For each project, get its members
    for (const projectId of userProjectIds) {
      const project = projects.find(p => p.id === projectId);

      if (project) {
        // Get all users in this project
        const projectUserEntries = projectUsers.filter(pu => pu.project_id === projectId);

        // Get full user data for each project user
        const members = projectUserEntries.map(pu => {
          const user = users.find(u => u.id === pu.user_id);
          const role = roles.find(r => r.id === pu.role_id);

          if (!user) return null;

          return {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            name: `${user.first_name} ${user.last_name}`,
            email: user.email,
            profile_picture: user.profile_picture,
            job_title: user.job_title,
            pivot: {
              role_id: pu.role_id,
              role_name: role ? role.name : 'Unknown'
            }
          };
        }).filter(Boolean);

        if (members.length > 0) {
          teamMembersByProject[projectId.toString()] = {
            projectName: project.name,
            members
          };
        }
      }
    }

    console.log(`[Mock API] Returning team members for ${Object.keys(teamMembersByProject).length} projects`);
    return createResponse(teamMembersByProject);
  },

  deleteEvent: async (eventId: string | number) => {
    await delay(300);

    console.log(`[Mock API] Deleting calendar event ${eventId}`);

    return createResponse({
      message: 'Event deleted successfully'
    });
  },

  rescheduleEvent: async (eventId: string | number, newStartDate: string, newEndDate?: string) => {
    await delay(300);

    console.log(`[Mock API] Rescheduling calendar event ${eventId}`);
    console.log(`[Mock API] New dates: startDate=${newStartDate}, endDate=${newEndDate || 'none'}`);

    return createResponse({
      id: eventId,
      start_date: newStartDate,
      end_date: newEndDate,
      message: 'Event rescheduled successfully'
    });
  }
};

export default mockApi;
