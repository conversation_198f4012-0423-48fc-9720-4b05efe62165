import React, { useState } from "react";
import { Calendar } from "lucide-react";
import { Project } from "@/entities/Project";
import { DateHelper } from "@/utils/dateUtils";
import CalendarView from "./CalendarView";
import { useTranslation } from "react-i18next";

interface WorkspaceUpcomingDeadlinesProps {
  deadlines: Project[];
}

const WorkspaceUpcomingDeadlines: React.FC<WorkspaceUpcomingDeadlinesProps> = ({ deadlines }) => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const { t } = useTranslation();

  return (
    <>
      <div className="bg-card border border-border rounded-xl shadow-subtle overflow-hidden mb-6">
        <div className="p-5 border-b border-border flex justify-between items-center">
          <h2 className="text-xl font-semibold">{t('workspace.upcomingDeadlines')}</h2>
          <button
            className="text-primary hover:text-primary/80 text-sm font-medium transition-colors flex items-center"
            onClick={() => setIsCalendarOpen(true)}
          >
            {t('workspace.calendarView')}
            <Calendar size={16} className="ml-1" />
          </button>
        </div>
        <div className="p-5">
          <div className="space-y-3">
            {deadlines.length > 0 ? (
              deadlines.map((deadline) => (
                <div
                  key={deadline.id}
                  className="p-3 border border-border rounded-lg hover:border-primary/30 hover:bg-accent/30 transition-colors"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{deadline.name}</h4>
                      <p className="text-xs text-muted-foreground mt-0.5">
                        {deadline.description && deadline.description.length > 60
                          ? `${deadline.description.substring(0, 60)}...`
                          : deadline.description}
                      </p>
                    </div>
                    <div className="bg-primary/10 text-primary text-xs font-medium px-2 py-1 rounded-md whitespace-nowrap">
                      {DateHelper.getMonthNameWithDay(deadline.end_date)}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-muted-foreground text-sm">{t('workspace.noUpcomingDeadlines')}</p>
            )}
          </div>
        </div>
      </div>

      <CalendarView
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
        projects={deadlines}
      />
    </>
  );
};

export default WorkspaceUpcomingDeadlines;
