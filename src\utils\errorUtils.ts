/**
 * Extract a human-readable error message from various error types
 * 
 * @param error The error object
 * @returns A human-readable error message
 */
export const getErrorMessage = (error: any): string => {
  if (!error) {
    return 'An unknown error occurred';
  }

  // Handle Axios errors
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    const data = error.response.data;
    
    // Check for Laravel validation errors
    if (data && data.errors) {
      // Join all validation errors into a single message
      return Object.values(data.errors)
        .flat()
        .join(', ');
    }
    
    // Check for a message in the response data
    if (data && data.message) {
      return data.message;
    }
    
    // Use the status text if available
    if (error.response.statusText) {
      return `${error.response.status}: ${error.response.statusText}`;
    }
    
    // Fallback to a generic error with status code
    return `Server error (${error.response.status})`;
  }
  
  // The request was made but no response was received
  if (error.request) {
    return 'No response received from server. Please check your connection.';
  }
  
  // Handle error message as a string
  if (typeof error === 'string') {
    return error;
  }
  
  // Handle error message in error object
  if (error.message) {
    return error.message;
  }
  
  // Fallback for any other type of error
  return 'An unexpected error occurred';
};
