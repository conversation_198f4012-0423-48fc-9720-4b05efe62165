import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';
import { User } from '@/entities/User';
import { Project } from '@/entities/Project';

// Search for a user by email
export const searchUserByEmail = async (email: string): Promise<User> => {
  try {
    const response = await api.get(`/users/search`, { params: { email } });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to find user",
      "Could not find a user with that email"
    );
  }
};

// Get all users
export const getAllUsers = async (): Promise<User[]> => {
  try {
    const response = await api.get('/users');
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch users",
      "Could not load users"
    );
  }
};

// Search for users by name or email
export const searchUsers = async (query: string): Promise<User[]> => {
  try {
    // Since the backend only supports searching by email, we'll use the getAllUsers endpoint
    // and filter the results on the client side
    const response = await api.get('/users');

    // Filter users based on the query
    const filteredUsers = response.data.filter((user: User) => {
      const fullName = `${user.first_name || ''} ${user.last_name || ''}`.toLowerCase();
      const email = (user.email || '').toLowerCase();
      const searchTerm = query.toLowerCase();

      return fullName.includes(searchTerm) || email.includes(searchTerm);
    });

    return filteredUsers;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to search users",
      "Could not search for users"
    );
  }
};

// Search for project members for @mentions
export const searchProjectMembers = async (projectId: number, query: string): Promise<User[]> => {
  try {
    const response = await api.get('/users/project-members', {
      params: {
        project_id: projectId,
        query
      }
    });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to search project members",
      "Could not search for project members"
    );
  }
};

// Interface for project members
export interface ProjectMembers {
  projectName: string;
  members: User[];
}

// Get team members (users from projects and workspaces the current user is in)
export const getTeamMembers = async (): Promise<{[projectId: string]: ProjectMembers}> => {
  try {
    const response = await api.get('/team-members');
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch team members",
      "Could not load team members"
    );
  }
};
