
import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { CalendarC<PERSON>, Clock, User, Ka<PERSON>ban, CheckCircle } from 'lucide-react';
import ProjectCardHeader from './project/ProjectCardHeader';
import ProjectStatus from './project/ProjectStatus';
import ProjectMembers from './project/ProjectMembers';
import ProjectTypeIndicator from './project/ProjectTypeIndicator';
import CircularProgress from './ui/circular-progress';
import { Project } from '@/entities/Project';
import { TurkishDateHelper } from '@/utils/dateUtils';
import { starProject, unstarProject } from '@/api/projectsApi';
import { useToast } from './ui/use-toast';
import { Badge } from './ui/badge';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { calculateProjectProgress, getProgressColor } from '@/utils/projectUtils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export interface ProjectMember {
  id: string;
  name: string;
  avatar?: string;
}

export interface ProjectCardProps {
  id: string;
  title: string;
  description: string;
  progress: number;
  dueDate: string;
  priority: 'low' | 'medium' | 'high';
  status: 'completed' | 'in-progress' | 'not-started' | 'at-risk';
  members: ProjectMember[];
  isStarred: boolean;
  is_group_project: boolean; // Added this property
  onStar: (id: string) => void;
  onViewKanban?: (id: string) => void;
  onViewTimeline?: (id: string) => void;
}


const ProjectCard: React.FC<Project | ProjectCardProps> = (props) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  // Local state to immediately reflect star status changes
  const [isStarred, setIsStarred] = useState('isStarred' in props ? props.isStarred : props.is_starred);

  // Calculate the project progress percentage
  const progressPercentage = useMemo(() => {
    return calculateProjectProgress(props as Project);
  }, [props]);

  const handleCardClick = () => {
    // Check if any project modal is open (like copy or transfer modals)
    // @ts-ignore - isProjectModalOpen is added to window object dynamically
    if (window.isProjectModalOpen) {
      console.log('Modal is open, preventing navigation');
      return;
    }

    if (props.id) {
      // Add error handling and logging
      try {
        console.log(`Navigating to project ${props.id}`);

        // First check if the user has access to this project
        if (props.members && props.members.some(member => member.id === props.user_id)) {
          navigate(`/projects/${props.id}/board`);
        } else {
          console.log('User may not have access to this project, but navigating anyway');
          navigate(`/projects/${props.id}/board`);
        }
      } catch (error) {
        console.error('Error navigating to project:', error);
      }
    } else {
      console.error('Project ID is undefined or null');
    }
  };

  const handleTimelineClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event

    // Check if any project modal is open (like copy or transfer modals)
    // @ts-ignore - isProjectModalOpen is added to window object dynamically
    if (window.isProjectModalOpen) {
      console.log('Modal is open, preventing navigation');
      return;
    }

    if (props.id) {
      try {
        console.log(`Navigating to project timeline ${props.id}`);

        // First check if the user has access to this project
        if (props.members && props.members.some(member => member.id === props.user_id)) {
          navigate(`/projects/${props.id}/timeline`);
        } else {
          console.log('User may not have access to this project, but navigating anyway');
          navigate(`/projects/${props.id}/timeline`);
        }
      } catch (error) {
        console.error('Error navigating to project timeline:', error);
      }
    } else {
      console.error('Project ID is undefined or null');
    }
  };

  const { toast } = useToast();

  const onStar = async (id: number) => {
    try {
      // Check if we're using ProjectCardProps or Project interface
      if ('onStar' in props && typeof props.onStar === 'function') {
        // If using ProjectCardProps, call the provided onStar function
        // This will be handled by the parent component (ProjectsPage)
        props.onStar(id.toString());

        // Update local state to reflect the change
        setIsStarred(!isStarred);
      } else {
        // Otherwise use the API directly
        if (isStarred) {
          // Make the API call to unstar the project
          await unstarProject(id);

          // Update local state
          setIsStarred(false);

          toast({
            title: "Project removed from favorites"
          });

          // Check if we're in a view that might be filtered by starred status
          const currentPath = window.location.pathname;
          const searchParams = new URLSearchParams(window.location.search);
          const filter = searchParams.get('filter');
          const isStarredView = filter === 'starred' ||
                               currentPath.includes('/starred') ||
                               document.querySelector('button[aria-selected="true"]')?.textContent?.includes('Starred');

          // If we're in the starred filter view, hide this card and refresh the data
          if (isStarredView) {
            // Hide this card immediately
            const card = document.querySelector(`[data-project-id="${id}"]`);
            if (card) {
              card.style.display = 'none';
            }

            // Force immediate invalidation and refetch of the starred projects query
            await queryClient.invalidateQueries({ queryKey: ['starredProjects'] });
            await queryClient.refetchQueries({ queryKey: ['starredProjects'] });

            // Also invalidate and refetch the projects query
            await queryClient.invalidateQueries({ queryKey: ['projects'] });
            await queryClient.refetchQueries({ queryKey: ['projects'] });

            // Force a page reload if we're in the starred view
            // This is a last resort to ensure the UI is updated
            if (isStarredView) {
              // Get the current URL
              const currentUrl = window.location.href;

              // Check if the URL already has the filter parameter
              if (currentUrl.includes('filter=starred')) {
                // If it does, just reload the page
                window.location.reload();
              } else {
                // If it doesn't, add the filter parameter before reloading
                const separator = currentUrl.includes('?') ? '&' : '?';
                window.location.href = `${currentUrl}${separator}filter=starred`;
              }
            }
          }
        } else {
          // Make the API call to star the project
          await starProject(id);

          // Update local state
          setIsStarred(true);

          toast({
            title: "Project added to favorites"
          });

          // Invalidate and refetch the relevant queries
          await queryClient.invalidateQueries({ queryKey: ['starredProjects'] });
          await queryClient.refetchQueries({ queryKey: ['starredProjects'] });
        }

        // Always invalidate the project-specific query if we're on a project page
        queryClient.invalidateQueries({ queryKey: ['project', id] });
      }
    } catch (error) {
      console.error("Error toggling star status:", error);
      toast({
        title: "An error occurred",
        description: "Could not update project favorites.",
        variant: "destructive",
      });
    }
  }

  // Normalize priority value
  const getNormalizedPriority = () => {
    // Handle numeric priority values (from backend)
    if (typeof props.priority === 'number') {
      switch (props.priority) {
        case 0: return 'low';
        case 1: return 'medium';
        case 2: return 'high';
        default: return 'medium';
      }
    }

    // Handle string priority values
    if (typeof props.priority === 'string') {
      const lowerPriority = props.priority.toLowerCase();
      if (lowerPriority === 'low' || lowerPriority === '0') return 'low';
      if (lowerPriority === 'medium' || lowerPriority === '1') return 'medium';
      if (lowerPriority === 'high' || lowerPriority === '2') return 'high';
    }

    // Default to medium if priority is undefined or invalid
    return 'medium';
  };

  const normalizedPriority = getNormalizedPriority();

  return (
    <div
      data-project-id={props.id}
      className={cn(
        "border rounded-xl hover:shadow-md transition-all duration-200 overflow-hidden cursor-pointer group transform hover:translate-y-[-2px]",
        normalizedPriority === 'high' ? 'bg-priority-high/5 border-priority-high dark:bg-priority-high/10 dark:border-priority-high/70 dark:shadow-sm dark:shadow-priority-high/10' :
        normalizedPriority === 'medium' ? 'bg-priority-medium/5 border-priority-medium dark:bg-priority-medium/10 dark:border-priority-medium/70 dark:shadow-sm dark:shadow-priority-medium/10' :
        normalizedPriority === 'low' ? 'bg-priority-low/5 border-priority-low dark:bg-priority-low/10 dark:border-priority-low/70 dark:shadow-sm dark:shadow-priority-low/10' :
        'bg-card border-border dark:shadow-sm dark:shadow-primary/5'
      )}
      onClick={handleCardClick}
    >
      {/* Card Header with Priority Indicator */}
      <div className="relative">
        {/* Priority indicator bar at the top */}
        <div className={`h-2 w-full ${
          normalizedPriority === 'high' ? 'bg-priority-high' :
          normalizedPriority === 'medium' ? 'bg-priority-medium' :
          'bg-priority-low'
        }`}></div>

        <div className="p-5">
          <ProjectCardHeader
            title={'title' in props ? props.title : props.name}
            priority={normalizedPriority}
            id={props.id}
            description={props.description}
            isStarred={isStarred}
            isGroupProject={'is_group_project' in props ? props.is_group_project : false}
            ownerId={'user_id' in props ? props.user_id : undefined}
            // Pass the complete project data to ensure role information is available
            project={'id' in props && typeof props.id === 'number' ? props : null}
            onStar={onStar}
          />
        </div>
      </div>

      {/* Card Body */}
      <div className="px-5 pb-5">

        {/* Task Progress Circular Bar - Moved to the top */}
        <div className="mt-4 flex justify-between items-center">
          <div className="flex items-center">
            <div className="relative" style={{ width: '44px', height: '44px' }}>
              <CircularProgress
                value={progressPercentage}
                size="sm"
                showPercentage={true}
                color={getProgressColor(progressPercentage)}
                className={cn(
                  "dark:shadow-sm dark:shadow-primary/20",
                  progressPercentage === 100 && "text-success dark:text-success"
                )}
                trackColor="rgba(0, 0, 0, 0.1)"
              />
            </div>
          </div>
        </div>

        {/* Project Type and Members */}
        <div className="mt-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <ProjectTypeIndicator project={props} size="md" showLabel={true} />
            {/* Show members for group projects */}
            {(('is_group_project' in props && props.is_group_project) ||
              (!('is_group_project' in props) && props.members && props.members.length > 0)) &&
              <ProjectMembers members={props.members || []} />}
          </div>

          {/* Dropdown menu for project views */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button
                onClick={(e) => e.stopPropagation()}
                className="flex items-center text-xs text-primary hover:bg-primary/10 dark:text-primary-foreground dark:hover:bg-primary/20 p-1.5 rounded-md transition-colors"
                aria-label={t('project.viewOptions')}
              >
                <CalendarClock size={14} className="mr-1 dark:text-primary-foreground/90" />
                <span className="group-hover:underline">{t('project.views')}</span>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
              <DropdownMenuItem onClick={handleTimelineClick}>
                <CalendarClock className="mr-2 h-4 w-4 dark:text-primary/90" />
                {t('project.timeline')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                navigate(`/projects/${props.id}/board`);
              }}>
                <Kanban className="mr-2 h-4 w-4 dark:text-info/90" />
                {t('project.kanban')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Status and Due Date */}
        <div className="mt-4 flex justify-between items-center">
          <div className="flex justify-between items-center w-full">
            <ProjectStatus progress={props.progress} priority={normalizedPriority} />

            <div className="flex items-center text-xs text-info-foreground bg-info px-2.5 py-1.5 rounded-md dark:shadow-sm dark:shadow-info/20">
              <Clock size={14} className="mr-1.5 dark:text-info-foreground/90" />
              {'dueDate' in props ? props.dueDate : (props.end_date ? TurkishDateHelper.formatDate(props.end_date) : t('project.noDueDate'))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
