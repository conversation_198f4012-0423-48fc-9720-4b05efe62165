
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths, isSameMonth, isSameDay, isToday, parseISO, isAfter, isBefore, isValid } from 'date-fns';
import { ChevronLeft, ChevronRight, Plus, Filter, User, Calendar as CalendarIcon, Edit, LayoutGrid, Clock, ArrowLeft } from 'lucide-react';
import { getAllEvents, CalendarEvent as ApiCalendarEvent, rescheduleEvent } from '@/api/calendarApi';
import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import Loader from '@/components/Loader';
import { useToast } from '@/components/ui/use-toast';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import echo from '@/utils/echo';
import { getFullImageUrl } from '@/utils/imageUtils';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import DayView from '@/components/calendar/DayView';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Local event type for display
type CalendarEvent = {
  id: string | number;
  title: string;
  date: Date;
  endDate?: Date;
  type: 'meeting' | 'deadline' | 'milestone' | 'task';
  assignees: Array<{
    id: string | number;
    name: string;
    avatar?: string;
  }>;
  status?: 'completed' | 'in-progress' | 'not-started';
  priority?: 'high' | 'medium' | 'low';
  project?: any;
  is_task?: boolean;
};

// Type color schemes
const typeColors = {
  meeting: 'bg-purple-100 text-purple-800 border-purple-300',
  deadline: 'bg-red-100 text-red-800 border-red-300',
  milestone: 'bg-blue-100 text-blue-800 border-blue-300',
  task: 'bg-green-100 text-green-800 border-green-300',
};

// Status color schemes
const statusColors = {
  completed: 'bg-green-500',
  'in-progress': 'bg-primary',
  'not-started': 'bg-orange-400',
};

// Event component for the day view
const EventItem = ({
  event,
  onClick,
  index,
  dayKey,
  day
}: {
  event: CalendarEvent,
  onClick?: () => void,
  index: number,
  dayKey: string,
  day: Date
}) => {
  // Ensure we have a clean ID for the API
  const getEventIdForUrl = () => {
    // If it's a string with a task_ prefix, keep it as is
    // The backend expects the task_ prefix for task events
    return event.id;
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClick) {
      onClick();
    } else {
      // Default navigation if no onClick provided
      // Use React Router's navigate instead of window.location for better SPA experience
      const eventId = getEventIdForUrl();
      window.location.href = `/calendar/events/${eventId}`;
    }
  };

  // Create a unique draggable ID
  const draggableId = `event-${event.id}-${dayKey}`;

  // Determine if this is a multi-day event
  const isMultiDay = event.endDate && !isSameDay(event.date, event.endDate);

  // Determine if this is the start, end, or middle of a multi-day event
  const isStart = isSameDay(event.date, day);
  const isEnd = event.endDate && isSameDay(event.endDate, day);
  const isContinuation = isMultiDay && !isStart && !isEnd;

  // Determine the appropriate border radius based on whether this is the start, end, or middle of a multi-day event
  const borderRadiusClass =
    !isMultiDay ? "rounded-md" :
    isStart ? "rounded-l-md rounded-r-none" :
    isEnd ? "rounded-r-md rounded-l-none" :
    "rounded-none";

  // Add a visual indicator for multi-day events
  const multiDayClass = isMultiDay ? "border-l-4" : "";

  // Add a label for multi-day events
  const eventLabel =
    !isMultiDay || isStart ? event.title :
    isContinuation ? "..." : // Show ellipsis for continuation days
    isEnd ? "End" : event.title;

  return (
    <Draggable draggableId={draggableId} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={cn(
            "px-1 py-0.5 text-xs font-medium border mb-0.5 truncate hover:shadow-sm transition-shadow cursor-pointer",
            borderRadiusClass,
            typeColors[event.type],
            multiDayClass,
            snapshot.isDragging ? "shadow-lg opacity-70" : "",
            isContinuation && "opacity-80"
          )}
          onClick={handleClick}
        >
          <div className="flex items-center space-x-1">
            {event.priority === 'high' && !isContinuation && <span className="inline-block w-1.5 h-1.5 bg-red-500 rounded-full"></span>}
            <span className="truncate text-[10px]">{eventLabel}</span>
            {isStart && isMultiDay && <span className="text-[8px] opacity-70">→</span>}
            {isEnd && <span className="text-[8px] opacity-70">←</span>}
          </div>
        </div>
      )}
    </Draggable>
  );
};

const CalendarPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();

  // Parse date from URL query parameter if present
  const getInitialDate = () => {
    const searchParams = new URLSearchParams(location.search);
    const dateParam = searchParams.get('date');
    if (dateParam) {
      try {
        const parsedDate = parseISO(dateParam);
        if (isValid(parsedDate)) {
          return parsedDate;
        }
      } catch (error) {
        console.error('Error parsing date from URL:', error);
      }
    }
    return new Date();
  };

  const initialDate = getInitialDate();
  const [currentMonth, setCurrentMonth] = useState<Date>(startOfMonth(initialDate));
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(initialDate);
  const [filterType, setFilterType] = useState<string | null>(null);
  const [filterAssignee, setFilterAssignee] = useState<string | null>(null);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'month' | 'day'>('month');

  // Generate days for calendar view
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Convert API events to local format
  const formatApiEvents = (data: ApiCalendarEvent[]): CalendarEvent[] => {
    return data.map((event: ApiCalendarEvent) => ({
      id: event.id,
      title: event.title || 'Untitled Event',
      date: new Date(event.start_date),
      endDate: event.end_date ? new Date(event.end_date) : undefined,
      type: event.type || 'task',
      status: event.status || 'not-started',
      priority: event.priority || 'medium',
      project: event.project,
      assignees: event.assignees || [],
      is_task: event.is_task || false
    }));
  };

  // Fetch events from API
  useEffect(() => {
    const fetchEvents = async () => {
      setIsLoading(true);
      try {
        const data = await getAllEvents({
          start_date: format(monthStart, 'yyyy-MM-dd'),
          end_date: format(monthEnd, 'yyyy-MM-dd')
        });

        if (!data || !Array.isArray(data)) {
          console.error('Invalid data format received from API:', data);
          setEvents([]);
          return;
        }

        // Convert API events to local format
        const formattedEvents = formatApiEvents(data);
        setEvents(formattedEvents);
      } catch (error) {
        console.error('Error fetching events:', error);
        toast({
          title: "Error",
          description: "Failed to load calendar events",
          variant: "destructive"
        });
        setEvents([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, [currentMonth, toast]);

  // Set up real-time event listeners
  useEffect(() => {
    if (!user) return;

    try {
      // Check if echo has the private method
      if (typeof echo.private !== 'function') {
        console.warn('Real-time updates are disabled. Echo is not properly configured.');
        return () => {}; // Return empty cleanup function
      }

      // Listen for event creation
      const createdChannel = echo.private(`user.${user.id}`);

      // Check if the channel has the required methods
      if (!createdChannel || typeof createdChannel.listen !== 'function') {
        console.warn('Real-time channel is not properly configured.');
        return () => {}; // Return empty cleanup function
      }

      createdChannel.listen('.calendar.event.created', (e: { id: number | string, title: string, start_date: string, end_date?: string, type: string, status: string, priority: string, project: any, assignees: any[], is_task?: boolean }) => {
        // Check if the event is within the current month
        const eventDate = new Date(e.start_date);
        if (isSameMonth(eventDate, currentMonth)) {
          // Add the new event to the list
          setEvents(prev => [
            ...prev,
            {
              id: e.id,
              title: e.title,
              date: eventDate,
              endDate: e.end_date ? new Date(e.end_date) : undefined,
              type: e.type || 'task',
              status: e.status || 'not-started',
              priority: e.priority || 'medium',
              project: e.project,
              assignees: e.assignees || [],
              is_task: e.is_task || false
            }
          ]);

          toast({
            title: "New Event",
            description: `"${e.title}" has been added to the calendar`,
          });
        }
      });

      // Listen for event updates
      createdChannel.listen('.calendar.event.updated', (e: { id: number | string, title: string, start_date: string, end_date?: string, type: string, status: string, priority: string, project: any, assignees: any[], is_task?: boolean }) => {
        // Update the event in the list
        setEvents(prev => prev.map(event => {
          if (event.id === e.id) {
            return {
              id: e.id,
              title: e.title,
              date: new Date(e.start_date),
              endDate: e.end_date ? new Date(e.end_date) : undefined,
              type: e.type || 'task',
              status: e.status || 'not-started',
              priority: e.priority || 'medium',
              project: e.project,
              assignees: e.assignees || [],
              is_task: e.is_task || false
            };
          }
          return event;
        }));
      });

      // Listen for event deletions
      createdChannel.listen('.calendar.event.deleted', (e: { id: number | string }) => {
        // Remove the event from the list
        setEvents(prev => prev.filter(event => event.id !== e.id));
      });

      // Clean up listeners when component unmounts
      return () => {
        // Check if stopListening method exists before calling it
        if (createdChannel && typeof createdChannel.stopListening === 'function') {
          createdChannel.stopListening('.calendar.event.created');
          createdChannel.stopListening('.calendar.event.updated');
          createdChannel.stopListening('.calendar.event.deleted');
        }
      };
    } catch (error) {
      console.error('Error setting up real-time listeners:', error);
      // Continue without real-time updates
      return () => {};
    }
  }, [user, currentMonth, toast]);

  // Handle month navigation
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Go to today
  const goToToday = () => {
    const today = new Date();
    setCurrentMonth(today);
    updateSelectedDate(today);
  };

  // Update selected date and URL
  const updateSelectedDate = (date: Date) => {
    setSelectedDate(date);

    // Update URL with the selected date without full page refresh
    const searchParams = new URLSearchParams(location.search);
    searchParams.set('date', format(date, 'yyyy-MM-dd'));
    navigate(`${location.pathname}?${searchParams.toString()}`, { replace: true });
  };

  // Get events for a specific day
  const getEventsForDay = (day: Date) => {
    return events.filter(event =>
      isSameDay(event.date, day) &&
      (filterType ? event.type === filterType : true) &&
      (filterAssignee ? event.assignees.some(assignee => assignee.id.toString() === filterAssignee) : true)
    );
  };

  // Get all events for the selected month, filtered
  const filteredEvents = events.filter(event =>
    isSameMonth(event.date, currentMonth) &&
    (filterType ? event.type === filterType : true) &&
    (filterAssignee ? event.assignees.some(assignee => assignee.id.toString() === filterAssignee) : true)
  );

  // Get events for the selected date
  const selectedDateEvents = selectedDate ? getEventsForDay(selectedDate) : [];

  // Get unique assignees from all events
  const allAssignees = events
    .flatMap(event => event.assignees)
    .reduce((unique: any[], assignee) => {
      // Check if this assignee ID already exists in our unique array
      const exists = unique.some(item => item.id === assignee.id);
      if (!exists && assignee.id) {
        unique.push(assignee);
      }
      return unique;
    }, []);

  // Handle drag end event
  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If there's no destination or the item was dropped back in its original position
    if (!destination ||
        (destination.droppableId === source.droppableId &&
         destination.index === source.index)) {
      return;
    }

    // Handle differently based on the view mode and droppable ID format
    if (viewMode === 'month') {
      // Month view: draggableId format is event-{id}-{dayKey}, droppableId format is day-{yyyy-MM-dd}

      // Extract the event ID from the draggableId
      const eventId = draggableId.split('-')[1];

      // Extract the date from the destination droppableId
      const newDateStr = destination.droppableId.split('-').slice(1).join('-');

      // Find the event that was dragged
      const draggedEvent = events.find(event => event.id.toString() === eventId);

      if (!draggedEvent) {
        console.error('Could not find the dragged event');
        return;
      }

      // Optimistically update the UI
      setEvents(prevEvents =>
        prevEvents.map(event => {
          if (event.id.toString() === eventId) {
            // Create a new date object with the same time
            const oldDate = new Date(event.date);
            const [year, month, day] = newDateStr.split('-').map(Number);
            const newDate = new Date(year, month - 1, day);
            newDate.setHours(oldDate.getHours());
            newDate.setMinutes(oldDate.getMinutes());

            // If the event has an end date, adjust it by the same amount
            let newEndDate = undefined;
            if (event.endDate) {
              const timeDiff = newDate.getTime() - oldDate.getTime();
              newEndDate = new Date(event.endDate.getTime() + timeDiff);
            }

            return {
              ...event,
              date: newDate,
              endDate: newEndDate
            };
          }
          return event;
        })
      );

      // Format the dates for the API
      const newDate = new Date(newDateStr);
      const oldDate = new Date(draggedEvent.date);
      newDate.setHours(oldDate.getHours(), oldDate.getMinutes(), 0, 0);

      // Calculate new end date if needed
      let newEndDateFormatted = undefined;
      if (draggedEvent.endDate) {
        const timeDiff = newDate.getTime() - oldDate.getTime();
        const newEndDate = new Date(draggedEvent.endDate.getTime() + timeDiff);
        newEndDateFormatted = format(newEndDate, 'yyyy-MM-dd HH:mm:ss');
      }

      // Call the API to update the event date
      rescheduleEvent(eventId, format(newDate, 'yyyy-MM-dd HH:mm:ss'), newEndDateFormatted)
        .then(() => {
          toast({
            title: "Event rescheduled",
            description: `"${draggedEvent.title}" has been moved to ${format(newDate, 'MMMM d, yyyy')}`,
          });
        })
        .catch(error => {
          // Revert the UI update on error
          setEvents(prevEvents =>
            prevEvents.map(event => {
              if (event.id.toString() === eventId) {
                return draggedEvent;
              }
              return event;
            })
          );

          toast({
            title: "Error",
            description: "Failed to reschedule the event. Please try again.",
            variant: "destructive"
          });
        });
    } else {
      // Day view: draggableId format is event-{id}, droppableId format is slot-{index}
      // The DayView component handles this internally
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Loader />
        </div>
      )}

      <main className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="flex flex-col space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-1 px-2 h-8"
                  onClick={() => {
                    // Navigate to the workspace page if we have a current workspace
                    if (user?.current_workspace_id) {
                      navigate(`/workspace/${user.current_workspace_id}`);
                    } else {
                      // Fallback to dashboard if no workspace is available
                      navigate('/dashboard');
                    }
                  }}
                >
                  <ArrowLeft size={16} />
                  Back to Workspace
                </Button>
              </div>
              <h1 className="text-3xl font-bold">Calendar</h1>
              <p className="text-muted-foreground mt-1">
                View and manage all your events and deadlines across projects
              </p>
            </div>

            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm" onClick={prevMonth}>
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="font-normal">
                    {format(currentMonth, 'MMMM yyyy')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="center">
                  <Calendar
                    mode="single"
                    defaultMonth={currentMonth}
                    onMonthChange={setCurrentMonth}
                    className="p-1 pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>

              <Button variant="outline" size="sm" onClick={nextMonth}>
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>

              <Button variant="outline" size="sm" onClick={goToToday}>
                Today
              </Button>

              {/* View toggle */}
              <div className="flex items-center border rounded-md overflow-hidden">
                <Button
                  variant={viewMode === 'month' ? 'default' : 'ghost'}
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode('month')}
                >
                  <LayoutGrid className="h-4 w-4 mr-1" />
                  Month
                </Button>
                <Button
                  variant={viewMode === 'day' ? 'default' : 'ghost'}
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode('day')}
                >
                  <Clock className="h-4 w-4 mr-1" />
                  Day
                </Button>
              </div>

              <Button
                className="ml-2"
                size="sm"
                onClick={() => navigate('/calendar/create')}
              >
                <Plus className="h-4 w-4 mr-1" />
                New Event
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 bg-background/60 backdrop-blur-sm border rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters:</span>
            </div>

            <div className="flex items-center space-x-3 flex-wrap gap-y-2">
              <Select value={filterType ?? "all"} onValueChange={(value) => setFilterType(value === "all" ? null : value)}>
                <SelectTrigger className="h-8 w-[150px]">
                  <SelectValue placeholder="Event Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="meeting">Meetings</SelectItem>
                  <SelectItem value="deadline">Deadlines</SelectItem>
                  <SelectItem value="milestone">Milestones</SelectItem>
                  <SelectItem value="task">Tasks</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterAssignee ?? "all"} onValueChange={(value) => setFilterAssignee(value === "all" ? null : value)}>
                <SelectTrigger className="h-8 w-[170px]">
                  <SelectValue placeholder="Team Member" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Members</SelectItem>
                  {allAssignees.map((assignee) => (
                    <SelectItem key={`assignee-${assignee.id}`} value={assignee.id.toString()}>
                      <div className="flex items-center">
                        <Avatar className="h-5 w-5 mr-2">
                          <AvatarImage src={getFullImageUrl(assignee.avatar)} alt={assignee.name} />
                          <AvatarFallback>{assignee.name ? assignee.name.charAt(0) : '?'}</AvatarFallback>
                        </Avatar>
                        {assignee.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Calendar views */}
          {viewMode === 'month' ? (
            // Month view
            <div className="grid grid-cols-1 md:grid-cols-7 gap-2">
              {/* Day headers (only on md and up) */}
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="hidden md:flex justify-center items-center h-8 font-medium text-xs text-muted-foreground">
                  {day}
                </div>
              ))}

              {/* Calendar days */}
              {monthDays.map((day) => {
                const dayEvents = getEventsForDay(day);
                const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;
                const dayKey = format(day, 'yyyy-MM-dd');
                const droppableId = `day-${dayKey}`;

                return (
                  <div
                    key={day.toString()}
                    className={cn(
                      "min-h-24 p-1 border rounded-lg transition-colors",
                      isToday(day) ? "bg-primary/5 border-primary/20" : "bg-card hover:bg-accent/5",
                      isSelected ? "ring-1 ring-primary" : "",
                      !isSameMonth(day, currentMonth) ? "opacity-50" : ""
                    )}
                    onClick={() => updateSelectedDate(day)}
                  >
                    {/* Day header with date */}
                    <div className="flex justify-between items-center mb-1">
                      <span className={cn(
                        "text-xs font-medium",
                        isToday(day) ? "text-primary" : ""
                      )}>
                        {format(day, 'd')}
                      </span>

                      {/* Show day of week on mobile */}
                      <span className="md:hidden text-xs text-muted-foreground">
                        {format(day, 'EEE')}
                      </span>

                      {/* Event counter */}
                      {dayEvents.length > 0 && (
                        <Badge variant="secondary" className="text-xs h-4 px-1">
                          {dayEvents.length}
                        </Badge>
                      )}
                    </div>

                    {/* Day events - Droppable area */}
                    <Droppable droppableId={droppableId}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className={cn(
                            "space-y-0.5 min-h-[40px]",
                            snapshot.isDraggingOver && "bg-primary/5 rounded-md p-1"
                          )}
                        >
                          {dayEvents.slice(0, 2).map((event, index) => (
                            <EventItem
                              key={event.id}
                              event={event}
                              index={index}
                              dayKey={dayKey}
                              day={day}
                              onClick={() => {
                                // Make sure to preserve the task_ prefix if it exists
                                navigate(`/calendar/events/${event.id}`);
                              }}
                            />
                          ))}

                          {dayEvents.length > 2 && (
                            <div className="text-xs text-muted-foreground text-center">
                              +{dayEvents.length - 2} more
                            </div>
                          )}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                );
              })}
            </div>
          ) : (
            // Day view
            <DayView
              date={selectedDate || new Date()}
              events={events}
              onDateChange={updateSelectedDate}
              onEventClick={(eventId) => navigate(`/calendar/events/${eventId}`)}
            />
          )}

          {/* Selected date details */}
          {selectedDate && (
            <div className="bg-card border rounded-lg p-3 mt-3">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-base font-semibold">
                  {format(selectedDate, 'EEEE, MMMM d, yyyy')}
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8"
                  onClick={() => navigate(`/calendar/create/${format(selectedDate, 'yyyy-MM-dd')}`)}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Event
                </Button>
              </div>

              {selectedDateEvents.length > 0 ? (
                <div className="space-y-3">
                  {selectedDateEvents.map((event) => (
                    <div
                      key={event.id}
                      className="flex items-start gap-3 p-3 border rounded-md hover:bg-accent/5 transition-colors cursor-pointer"
                      onClick={() => {
                        // Keep the task_ prefix intact for task IDs
                        navigate(`/calendar/events/${event.id}`);
                      }}
                    >
                      <div className={cn(
                        "w-1 self-stretch rounded-full",
                        event.status ? statusColors[event.status] : "bg-gray-300"
                      )} />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <Badge className={cn("px-2 py-0.5", typeColors[event.type])}>
                            {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                          </Badge>

                          {event.priority && (
                            <Badge variant="outline" className={cn(
                              "px-2 py-0.5 text-xs",
                              event.priority === 'high' ? "border-red-300 text-red-600" :
                              event.priority === 'medium' ? "border-yellow-300 text-yellow-600" :
                              "border-green-300 text-green-600"
                            )}>
                              {event.priority.charAt(0).toUpperCase() + event.priority.slice(1)}
                            </Badge>
                          )}
                        </div>

                        <h3 className="text-base font-medium mt-1">{event.title}</h3>

                        <div className="flex items-center mt-2 text-sm text-muted-foreground">
                          <span>{format(event.date, 'MMM d, yyyy h:mm a')}</span>
                          {event.endDate && (
                            <>
                              <span className="mx-1">-</span>
                              <span>{format(event.endDate, isSameDay(event.date, event.endDate) ? 'h:mm a' : 'MMM d, yyyy h:mm a')}</span>
                              {!isSameDay(event.date, event.endDate) && (
                                <Badge variant="outline" className="ml-2 text-xs">Multi-day</Badge>
                              )}
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <div className="flex -space-x-2">
                          {event.assignees.slice(0, 3).map((assignee) => (
                            <Avatar key={`event-assignee-${assignee.id}`} className="h-8 w-8 border-2 border-background">
                              <AvatarImage src={getFullImageUrl(assignee.avatar)} alt={assignee.name} />
                              <AvatarFallback>{assignee.name ? assignee.name.charAt(0) : '?'}</AvatarFallback>
                            </Avatar>
                          ))}

                          {event.assignees.length > 3 && (
                            <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium border-2 border-background">
                              +{event.assignees.length - 3}
                            </div>
                          )}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Keep the task_ prefix intact for task IDs
                            navigate(`/calendar/edit/${event.id}`);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div>
                  <div className="text-center py-4 text-muted-foreground">
                    <p>No events scheduled for this day</p>
                  </div>

                  <div className="flex justify-center">
                    <Button
                      variant="link"
                      size="sm"
                      className="text-primary"
                      onClick={() => navigate(`/calendar/create/${format(selectedDate, 'yyyy-MM-dd')}`)}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Create an event
                    </Button>
                  </div>

                  {/* Month mini-calendar */}
                  <div className="mt-3">
                    <div className="text-center mb-1 text-xs font-medium">
                      {format(currentMonth, 'MMMM yyyy')}
                    </div>

                    <div className="grid grid-cols-7 text-center mb-1">
                      {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                        <div key={day} className="text-[10px] text-muted-foreground">
                          {day}
                        </div>
                      ))}
                    </div>

                    <div className="grid grid-cols-7 gap-0.5">
                      {monthDays.map((day) => {
                        const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;
                        const hasEvents = getEventsForDay(day).length > 0;

                        return (
                          <div
                            key={day.toString()}
                            className={cn(
                              "h-6 w-6 flex items-center justify-center text-[10px] rounded-full cursor-pointer",
                              isToday(day) ? "font-bold" : "",
                              isSelected ? "bg-primary text-primary-foreground" :
                                hasEvents ? "bg-primary/10" : "",
                              !isSameMonth(day, currentMonth) ? "text-muted-foreground/50" : ""
                            )}
                            onClick={() => updateSelectedDate(day)}
                          >
                            {format(day, 'd')}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        </DragDropContext>
      </main>
    </div>
  );
};

export default CalendarPage;
