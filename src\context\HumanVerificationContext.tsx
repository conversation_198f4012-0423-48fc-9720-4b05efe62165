import React, { createContext, useContext, useState, useEffect } from 'react';
import HumanVerificationModal from '@/components/HumanVerificationModal';
import { useToast } from '@/components/ui/use-toast';

interface HumanVerificationContextType {
  showVerification: () => void;
  verificationToken: string | null;
}

const HumanVerificationContext = createContext<HumanVerificationContextType | undefined>(undefined);

export const HumanVerificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [verificationToken, setVerificationToken] = useState<string | null>(
    localStorage.getItem('human_verification_token')
  );
  const { toast } = useToast();

  // Store the token in localStorage when it changes
  useEffect(() => {
    if (verificationToken) {
      localStorage.setItem('human_verification_token', verificationToken);
    }
  }, [verificationToken]);

  // Listen for the custom event from the API interceptor
  useEffect(() => {
    const handleVerificationRequired = () => {
      toast({
        title: "Verification Required",
        description: "Please complete the human verification to continue.",
        variant: "default",
      });
      setIsModalOpen(true);
    };

    window.addEventListener('human-verification-required', handleVerificationRequired);

    return () => {
      window.removeEventListener('human-verification-required', handleVerificationRequired);
    };
  }, [toast]);

  const showVerification = () => {
    setIsModalOpen(true);
  };

  const handleVerificationSuccess = (token: string) => {
    setVerificationToken(token);

    // Refresh the page to retry the failed requests with the new token
    setTimeout(() => {
      window.location.reload();
    }, 2000);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <HumanVerificationContext.Provider value={{ showVerification, verificationToken }}>
      {children}
      <HumanVerificationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onVerificationSuccess={handleVerificationSuccess}
      />
    </HumanVerificationContext.Provider>
  );
};

export const useHumanVerification = (): HumanVerificationContextType => {
  const context = useContext(HumanVerificationContext);
  if (context === undefined) {
    throw new Error('useHumanVerification must be used within a HumanVerificationProvider');
  }
  return context;
};
