
import React, { useState, useEffect } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Info, MailCheck, ArrowRight, PartyPopper, EyeOff, Eye, Mail, Home, LogIn, UserPlus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import logo from "/assets/logo.png";

import { useTheme } from "@/context/ThemeContext";

const Login = () => {
  const { login, requestForAccount, isLoading } = useAuth();
  const navigate = useNavigate();
  const { isDarkMode } = useTheme();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const redirectUrl = searchParams.get('redirect');

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [requestMode, setRequestMode] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [data, setData] = useState({ title: 'Bir Hata Oluştu!', message: 'İstenmeyen bir hata oluştu lütfen daha sonra tekrar deneyin.' });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setErrors({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: ''
      });

      let newErrors = {
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: ''
      };

      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        newErrors.email = 'Geçersiz e-posta formatı';
      }

      if (requestMode) {
        if (!/^[A-Za-zğüşıöçĞÜŞİÖÇ\s]{2,}$/.test(firstName)) {
          newErrors.firstName = 'En az 2 harf içermeli ve özel karakterler kullanılamaz';
        }

        if (!/^[A-Za-zğüşıöçĞÜŞİÖÇ\s]{2,}$/.test(lastName)) {
          newErrors.lastName = 'En az 2 harf içermeli ve özel karakterler kullanılamaz';
        }

        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}/.test(password)) {
          newErrors.password = 'En az 8 karakter, büyük/küçük harf, sayı ve özel karakter içermeli';


        } else if (!confirmPassword) {
          newErrors.confirmPassword = 'Lütfen şifrenizi onaylayın';

        } else if (password !== confirmPassword) {
          newErrors.confirmPassword = 'Şifreler eşleşmiyor';
        }


      } else {
        // Login validations
        if (password.length < 8) {
          newErrors.password = 'Şifre en az 8 karakter olmalı';
        }
      }

      if (Object.values(newErrors).some(error => error !== '')) {
        setErrors(newErrors);
        return;
      }


      if (requestMode) {
        let data = await requestForAccount(firstName, lastName, email, password);

        setData({ title: `Welcome ${firstName} ${lastName}`, message: data['message'] });
        setEmailSent(true);
      } else {
        // Pass the redirect URL to the login function if it exists
        await login(email, password, redirectUrl);
      }


    } catch (error) {
      console.error("Authentication error");
    }
  };



  const resetForm = () => {
    setRequestMode(!requestMode);
    setEmail('');
    setPassword('');
    setConfirmPassword('');
    setShowPassword(false);
    setFirstName('');
    setLastName('');
    setErrors({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: ''
    });
  }

  return emailSent ? (
    <div className={`min-h-screen flex items-center justify-center p-4 ${isDarkMode ? 'bg-background' : 'bg-background'}`}>
      <Card className="w-full max-w-md shadow-lg border border-border">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-50 w-50 items-center justify-center rounded-full mb-5">
            <img
              src={logo}
              alt="Email illustration"
              className="w-32 h-32 object-contain"
            />
          </div>

          <CardTitle className="text-2xl font-bold text-foreground">
            {data.title}
          </CardTitle>
          <CardDescription className="text-lg text-muted-foreground">
            {data.message}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center">

          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mb-5">
            <MailCheck className="h-8 w-8 text-primary" />
          </div>

          <div className="text-center space-y-2">
            <p className="text-foreground">
              Please check your inbox <span className="font-semibold text-primary">{email}</span>
            </p>
            <p className="text-muted-foreground text-sm">
              Click the verification link to complete your account registration.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <Button
            onClick={() => {
              resetForm();
              setEmailSent(false);
              setRequestMode(false);
            }}
            className="w-full"
            variant="default"
          >
            Return to Login
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <div className="flex items-center text-sm text-muted-foreground">
            <PartyPopper className="h-4 w-4 mr-1 text-primary" />
            <span>Welcome to Suite!</span>
          </div>
        </CardFooter>
      </Card>
    </div>
  ) : (
    <div className={`min-h-screen flex items-center justify-center p-4 ${isDarkMode ? 'bg-background' : 'bg-background'}`}>
      <Card className="w-full max-w-md shadow-lg border border-border">
        <CardHeader className="text-center space-y-4">
          <img
            src={logo}
            alt="Logo"
            className="mx-auto h-24 w-24 object-contain"
          />
          <div>
            <CardTitle className="text-2xl font-bold text-foreground">
              {requestMode ? "Create Account" : "Sign In"}
            </CardTitle>
            <CardDescription className="text-muted-foreground mt-2">
              {requestMode
                ? "Enter your information to get started with Suite"
                : "Enter your credentials to access your account"}
            </CardDescription>
          </div>
        </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-5">
              {requestMode && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="first_name"
                      placeholder="Enter your first name"
                      value={firstName}
                      onChange={(e) => {
                        setFirstName(e.target.value);
                        setErrors(prev => ({ ...prev, firstName: '' }));
                      }}
                      className={errors.firstName ? 'border-destructive focus-visible:ring-destructive' : ''}
                    />
                    {errors.firstName && (
                      <span className="text-destructive text-xs mt-1 flex items-center gap-1">
                        <Info size={12} />
                        {errors.firstName}
                      </span>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="last_name"
                      placeholder="Enter your last name"
                      value={lastName}
                      onChange={(e) => {
                        setLastName(e.target.value);
                        setErrors(prev => ({ ...prev, lastName: '' }));
                      }}
                      className={errors.lastName ? 'border-destructive focus-visible:ring-destructive' : ''}
                    />
                    {errors.lastName && (
                      <span className="text-destructive text-xs mt-1 flex items-center gap-1">
                        <Info size={12} />
                        {errors.lastName}
                      </span>
                    )}
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    setErrors(prev => ({ ...prev, email: '' }));
                  }}
                  className={errors.email ? 'border-destructive focus-visible:ring-destructive' : ''}
                />
                {errors.email && (
                  <span className="text-destructive text-xs mt-1 flex items-center gap-1">
                    <Info size={12} />
                    {errors.email}
                  </span>
                )}
              </div>

              {(
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="password">Password</Label>
                      {!requestMode && (
                        <Button
                          variant="link"
                          className="px-0 text-xs text-primary h-auto"
                          onClick={(e) => {
                            e.preventDefault();
                            // Handle forgot password
                          }}
                        >
                          Forgot Password?
                        </Button>
                      )}
                    </div>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        value={password}
                        onChange={(e) => {
                          setPassword(e.target.value);
                          setErrors(prev => ({ ...prev, password: '' }));
                        }}
                        className={errors.password ? 'border-destructive focus-visible:ring-destructive' : ''}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                    {errors.password && (
                      <span className="text-destructive text-xs mt-1 flex items-center gap-1">
                        <Info size={12} />
                        {errors.password}
                      </span>
                    )}
                  </div>

                  {requestMode && (
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showPassword ? "text" : "password"}
                          placeholder="Confirm your password"
                          value={confirmPassword}
                          onChange={(e) => {
                            setConfirmPassword(e.target.value);
                            setErrors(prev => ({ ...prev, confirmPassword: '' }));
                          }}
                          className={errors.confirmPassword ? 'border-destructive focus-visible:ring-destructive' : ''}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                      {errors.confirmPassword && (
                        <span className="text-destructive text-xs mt-1 flex items-center gap-1">
                          <Info size={12} />
                          {errors.confirmPassword}
                        </span>
                      )}
                    </div>
                  )}
                </>
              )}



              <Button
                type="submit"
                className="w-full"
                variant="default"
                disabled={
                  isLoading ||
                  (requestMode
                    ? !firstName || !lastName || !email || password.length < 8
                    : (!email || password.length < 8))
                }
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {requestMode ? "Creating Account..." : "Please Wait..."}
                  </>
                ) : requestMode ? (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Create Account
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign In
                  </>
                )}
              </Button>
            </form>


          </CardContent>

          <CardFooter className="flex flex-col space-y-3">
            <Button
              variant="link"
              className="px-0 text-sm text-primary hover:text-primary/80"
              onClick={() => {
                resetForm();
              }}
            >
              {requestMode
                ? "Already have an account? Sign in"
                : "Don't have an account? Sign up"}
            </Button>

            <Link
              to="/"
              className="text-sm text-muted-foreground hover:text-foreground flex items-center justify-center gap-1 transition-colors"
            >
              <Home size={16} /> Return to home page
            </Link>
          </CardFooter>
        </Card>
      </div>
    );
};

export default Login;
