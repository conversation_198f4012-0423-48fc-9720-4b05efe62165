export interface UserPivot {
    role_id?: number; // Role ID
}

export interface User {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    job_title?: string;
    bio?: string;
    profile_picture?: string;
    birth_date?: string;
    name?: string; // Added for compatibility with Navbar and Dashboard
    status?: number; // Added for compatibility with Navbar
    role?: string; // Added for compatibility with Navbar
    pivot?: UserPivot; // For pivot data in many-to-many relationships
    language?: string; // User's preferred language
}


export function normalizeUser(user: any): User {
    return {
        id: user.id || 0,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        phone: user.phone || '',
        job_title: user.job_title || '',
        bio: user.bio || '',
        profile_picture: user.profile_picture || '',
        birth_date: user.birth_date || '',
        name: user.name || `${user.first_name || ''} ${user.last_name || ''}`,
        status: user.status,
        role: user.role || '',
        language: user.language || 'en',
        pivot: user.pivot ? {
            role_id: user.pivot.role_id
        } : undefined,
    };
}
