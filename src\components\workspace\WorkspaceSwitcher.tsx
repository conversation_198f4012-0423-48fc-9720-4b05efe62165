import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Check, ChevronDown, Plus, Settings, Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useWorkspace } from '@/context/WorkspaceContext';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import CreateWorkspaceModal from './CreateWorkspaceModal';

const WorkspaceSwitcher: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { workspaces, currentWorkspace, fetchWorkspaces } = useWorkspace();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);

  // Fetch workspaces when component mounts or dropdown is opened
  useEffect(() => {
    // Only fetch workspaces if we don't already have them
    if (!workspaces || workspaces.length === 0) {
      const loadWorkspaces = async () => {
        setIsLoading(true);
        try {
          await fetchWorkspaces();
        } catch (error) {
          console.error('Error fetching workspaces:', error);
          toast({
            title: t('error.title', 'Error'),
            description: t('error.fetchWorkspaces', 'Failed to load workspaces'),
            variant: 'destructive',
          });
        } finally {
          setIsLoading(false);
        }
      };

      loadWorkspaces();
    } else {
      setIsLoading(false);
    }
  }, [fetchWorkspaces, toast, t, workspaces]);

  // Refresh workspaces when dropdown is opened
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen) {
      fetchWorkspaces().catch(error => {
        console.error('Error refreshing workspaces:', error);
      });
    }
  };

  const handleWorkspaceSelect = (workspaceId: number) => {
    console.log('WorkspaceSwitcher - Selected workspace ID:', workspaceId);
    navigate(`/workspace/${workspaceId}`);
    setOpen(false);
  };

  const handleCreateWorkspace = () => {
    setIsCreateModalOpen(true);
    setOpen(false);
  };

  const handleWorkspaceSettings = () => {
    if (currentWorkspace) {
      navigate(`/workspace/${currentWorkspace.id}/settings`);
      setOpen(false);
    }
  };

  // Get workspace initials for avatar fallback
  const getWorkspaceInitials = (name: string) => {
    if (!name) return '??';
    const words = name.split(' ');
    if (words.length === 1) {
      return name.substring(0, 2).toUpperCase();
    }
    return (words[0][0] + (words[1]?.[0] || '')).toUpperCase();
  };

  // Get workspace color based on name
  const getWorkspaceColor = (name: string) => {
    if (!name) return 'bg-primary dark:bg-primary';
    const colors = [
      'bg-red-500 dark:bg-red-600',
      'bg-blue-500 dark:bg-blue-600',
      'bg-green-500 dark:bg-green-600',
      'bg-yellow-500 dark:bg-yellow-600',
      'bg-purple-500 dark:bg-purple-600',
      'bg-pink-500 dark:bg-pink-600',
      'bg-indigo-500 dark:bg-indigo-600',
      'bg-teal-500 dark:bg-teal-600',
    ];
    const index = name.length % colors.length;
    return colors[index];
  };

  return (
    <>
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            aria-label={t('workspace.select', 'Select workspace')}
            className={cn(
              "flex items-center gap-2 w-full sm:w-[200px] justify-between",
              !currentWorkspace && "text-muted-foreground"
            )}
          >
            {isLoading ? (
              <Skeleton className="h-5 w-[150px]" />
            ) : currentWorkspace ? (
              <div className="flex items-center gap-2 truncate">
                <Avatar className="h-5 w-5">
                  <AvatarImage src={currentWorkspace.logo || ''} alt={currentWorkspace.name} />
                  <AvatarFallback className={cn("text-xs text-white", getWorkspaceColor(currentWorkspace.name))}>
                    {getWorkspaceInitials(currentWorkspace.name)}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate">{currentWorkspace.name}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                <span>{t('workspace.selectWorkspace', 'Select Workspace')}</span>
              </div>
            )}
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[220px] p-0" align="start">
          <Command>
            <CommandInput
              placeholder={t('workspace.search', 'Search workspaces...')}
              className="h-9"
            />
            <CommandList>
              <CommandEmpty>{t('workspace.noResults', 'No workspaces found')}</CommandEmpty>
              <CommandGroup heading={t('workspace.workspaces', 'Workspaces')}>
                {workspaces && workspaces.length > 0 ? (
                  workspaces.map((workspace) => (
                    <CommandItem
                      key={workspace.id}
                      onSelect={() => handleWorkspaceSelect(workspace.id)}
                      className="flex items-center gap-2 text-sm"
                    >
                      <Avatar className="h-5 w-5">
                        <AvatarImage src={workspace.logo || ''} alt={workspace.name} />
                        <AvatarFallback className={cn("text-xs text-white", getWorkspaceColor(workspace.name))}>
                          {getWorkspaceInitials(workspace.name)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="truncate flex-1">{workspace.name}</span>
                      {currentWorkspace?.id === workspace.id && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </CommandItem>
                  ))
                ) : (
                  <CommandItem disabled>{t('workspace.noWorkspaces', 'No workspaces found')}</CommandItem>
                )}
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem onSelect={handleCreateWorkspace} className="text-sm">
                  <Plus className="mr-2 h-4 w-4 text-primary" />
                  {t('workspace.createNew', 'Create New Workspace')}
                </CommandItem>
                {currentWorkspace && (
                  <CommandItem onSelect={handleWorkspaceSettings} className="text-sm">
                    <Settings className="mr-2 h-4 w-4" />
                    {t('workspace.settings', 'Workspace Settings')}
                  </CommandItem>
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <CreateWorkspaceModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />
    </>
  );
};

export default WorkspaceSwitcher;
