import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Task } from '@/api/tasksApi';
import { User } from '@/entities/User';
import { Project } from '@/entities/Project';
import { Loader2 } from 'lucide-react';
import { getProjectUsers } from '@/api/tasksApi';
import { removeAssigneeFromTask, addAssigneeToTask } from '@/api/tasksApi';
import { useToast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ReassignTasksModalProps {
  isOpen: boolean;
  onClose: () => void;
  formerMember: User | null;
  project: Project | null;
  tasks: Task[];
  onTasksReassigned: () => void;
}

const ReassignTasksModal: React.FC<ReassignTasksModalProps> = ({
  isOpen,
  onClose,
  formerMember,
  project,
  tasks,
  onTasksReassigned
}) => {
  const [activeMembers, setActiveMembers] = useState<User[]>([]);
  const [selectedMember, setSelectedMember] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Fetch active project members
  useEffect(() => {
    if (isOpen && project) {
      setIsLoading(true);
      getProjectUsers(project.id.toString())
        .then(users => {
          // Filter out the former member and get only active members
          const activeUsers = users.filter(user => 
            user.id !== formerMember?.id
          );
          setActiveMembers(activeUsers);
          setIsLoading(false);
        })
        .catch(error => {
          console.error('Error fetching project members:', error);
          toast({
            title: 'Error',
            description: 'Failed to load project members',
            variant: 'destructive',
          });
          setIsLoading(false);
        });
    }
  }, [isOpen, project, formerMember, toast]);

  const handleReassign = async () => {
    if (!selectedMember || !formerMember || !project) return;
    
    setIsProcessing(true);
    
    try {
      // Process each task
      for (const task of tasks) {
        // Remove the former member
        await removeAssigneeFromTask(task.id, [formerMember.id]);
        
        // Add the new assignee
        await addAssigneeToTask(task.id, [parseInt(selectedMember)]);
      }
      
      toast({
        title: 'Success',
        description: `${tasks.length} tasks reassigned successfully`,
      });
      
      // Notify parent component that tasks were reassigned
      onTasksReassigned();
      
      // Close the modal
      onClose();
    } catch (error) {
      console.error('Error reassigning tasks:', error);
      toast({
        title: 'Error',
        description: 'Failed to reassign tasks',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Reassign Tasks</DialogTitle>
          <DialogDescription>
            Reassign {tasks.length} task{tasks.length !== 1 ? 's' : ''} from former member {formerMember?.first_name} {formerMember?.last_name} to an active project member.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-center space-x-2 p-2 bg-amber-50 border border-amber-200 rounded-md">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={formerMember?.profile_picture}
                alt={`${formerMember?.first_name} ${formerMember?.last_name}`}
              />
              <AvatarFallback>
                {formerMember?.first_name?.[0]}{formerMember?.last_name?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">{formerMember?.first_name} {formerMember?.last_name}</p>
              <p className="text-xs text-amber-600">Former Member</p>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium block mb-2">
              Reassign to:
            </label>
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <Select
                value={selectedMember}
                onValueChange={setSelectedMember}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a team member" />
                </SelectTrigger>
                <SelectContent>
                  {activeMembers.map(member => (
                    <SelectItem key={member.id} value={member.id.toString()}>
                      <div className="flex items-center">
                        <Avatar className="h-5 w-5 mr-2">
                          <AvatarImage src={member.profile_picture} alt={`${member.first_name} ${member.last_name}`} />
                          <AvatarFallback>{member.first_name?.[0]}{member.last_name?.[0]}</AvatarFallback>
                        </Avatar>
                        {member.first_name} {member.last_name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          <div className="bg-muted/50 p-3 rounded-md">
            <h4 className="text-sm font-medium mb-2">Tasks to reassign:</h4>
            <ul className="text-sm space-y-1 max-h-32 overflow-y-auto">
              {tasks.map(task => (
                <li key={task.id} className="truncate">
                  • {task.title}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isProcessing}>
            Cancel
          </Button>
          <Button 
            onClick={handleReassign} 
            disabled={!selectedMember || isProcessing}
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Reassigning...
              </>
            ) : (
              'Reassign Tasks'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReassignTasksModal;
