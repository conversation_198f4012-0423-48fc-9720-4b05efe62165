import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DragDropContext, Droppable, DropResult } from 'react-beautiful-dnd';
import Navbar from '@/components/Navbar';
import KanbanColumn from '@/components/KanbanColumn';
import TeamMembersModal from '@/components/TeamMembersModal';
import TimelineView from '@/components/TimelineView';
import ViewsDropdown from '@/components/ViewsDropdown';
import UserMultiSelect from '@/components/UserMultiSelect';
import TaskDetailModal from '@/components/TaskDetailModal';
import TaskFilterPanel from '@/components/TaskFilterPanel';
import ConvertToGroupProjectButton from '@/components/ConvertToGroupProjectButton';
import FormerMembersManager from '@/components/project/FormerMembersManager';
import CreateTaskModal from '@/components/task/CreateTaskModal';
import PageBreadcrumbs from '@/components/navigation/PageBreadcrumbs';
import { List, getProjectLists, createList, updateList, deleteList } from '@/api/listsApi';
import { Task, TaskFilter, getTasks, createTask, updateTask, deleteTask, addTaskAssignees, removeTaskAssignees, getTask, reorderTasks, getProjectUsers } from '@/api/tasksApi';
import { getProject } from '@/api/projectsApi';
import { useCombinedQueriesWithTransform } from '@/hooks/useCombinedQueries';
import { startMark, endMark } from '@/utils/performanceMonitor';
import { useRenderTracking } from '@/utils/performanceMonitor.tsx';
import { Plus, Users, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import { User } from '@/entities/User';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

const KanbanBoard: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const taskIdFromUrl = searchParams.get('task');

  // Local state
  const [isListDialogOpen, setIsListDialogOpen] = useState(false);
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [isCreateTaskModalOpen, setIsCreateTaskModalOpen] = useState(false);
  const [isTaskDetailModalOpen, setIsTaskDetailModalOpen] = useState(false);
  const [isTeamModalOpen, setIsTeamModalOpen] = useState(false);
  const [isTimelineModalOpen, setIsTimelineModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleteListDialogOpen, setIsDeleteListDialogOpen] = useState(false);
  const [currentList, setCurrentList] = useState<List | null>(null);
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [listToDelete, setListToDelete] = useState<number | null>(null);
  const [newListTitle, setNewListTitle] = useState('');
  const [newListDescription, setNewListDescription] = useState('');
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [newTaskPriority, setNewTaskPriority] = useState('medium');
  const [newTaskStatus, setNewTaskStatus] = useState('not-started');
  const [newTaskDueDate, setNewTaskDueDate] = useState('');
  const [selectedAssignees, setSelectedAssignees] = useState<number[]>([]);
  const [localProject, setLocalProject] = useState<any>(null);

  // Filtering and sorting state
  const [taskFilters, setTaskFilters] = useState<TaskFilter>({ project_id: Number(projectId) });
  const [sortField, setSortField] = useState('position');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Debug counter to track re-renders
  const [renderCount, setRenderCount] = useState(0);

  // Debug effect to track component re-renders
  useEffect(() => {
    setRenderCount(prev => {
      const newCount = prev + 1;
      // Log only every 10 renders to avoid console spam
      if (newCount % 10 === 0) {
        console.log(`KanbanBoard re-rendered ${newCount} times`);
      }
      return newCount;
    });
  }, []);

  // Track component renders
  useRenderTracking('KanbanBoard');

  // Use our combined queries hook to reduce re-renders

  // Start performance measurement
  startMark('KanbanBoard-data-fetching');

  // Combine all queries into a single hook call
  const {
    data: combinedData,
    isLoading: isDataLoading,
    isError: isDataError,
    errors: dataErrors
  } = useCombinedQueriesWithTransform(
    [
      // Project query
      {
        queryKey: ['project', projectId],
        queryFn: () => getProject(projectId),
        enabled: !!projectId,
        retry: 1,
        staleTime: 60000,
        onSuccess: (data) => {
          // Update localProject when project data is loaded
          if (data && !localProject) {
            setLocalProject(data);
          }
        },
        onError: (error) => {
          console.error('Error in project query:', error);
        }
      },
      // Lists query
      {
        queryKey: ['lists', projectId],
        queryFn: () => getProjectLists(Number(projectId)),
        enabled: !!projectId,
        staleTime: 60000
      },
      // Tasks query
      {
        queryKey: ['tasks', projectId, taskFilters, sortField, sortDirection],
        queryFn: () => getTasks({
          ...taskFilters,
          sort_field: sortField,
          sort_direction: sortDirection
        }),
        enabled: !!projectId,
        staleTime: 30000,
        structuralSharing: (oldData, newData) => {
          if (JSON.stringify(oldData) === JSON.stringify(newData)) {
            return oldData;
          }
          return newData;
        }
      },
      // Project users query
      {
        queryKey: ['project-users', projectId],
        queryFn: () => getProjectUsers(Number(projectId)),
        enabled: !!projectId,
        staleTime: 60000
      }
    ],
    // Transform function to extract individual data items
    (data) => ({
      project: data[0],
      lists: data[1] || [],
      tasks: data[2] || [],
      projectUsers: data[3] || []
    })
  );

  // End performance measurement
  endMark('KanbanBoard-data-fetching');

  // Extract individual data items
  const project = combinedData?.project;
  const lists = combinedData?.lists || [];
  const tasks = combinedData?.tasks || [];
  const projectUsers = combinedData?.projectUsers || [];

  // Add debugging logs
  useEffect(() => {
    console.log(`Project ID: ${projectId}`);
    console.log('Project data:', project);
    console.log(`Lists count: ${lists.length}`, lists);
    console.log(`Tasks count: ${tasks.length}`, tasks);
    console.log(`Project users count: ${projectUsers.length}`, projectUsers);
    console.log('Data errors:', dataErrors);
  }, [project, lists, tasks, projectUsers, dataErrors, projectId]);

  // Combine loading states
  const isLoading = isDataLoading;

  // Extract error
  const projectError = dataErrors[0];

  // Fetch specific task from URL if taskId is present
  const { data: taskFromUrl } = useQuery({
    queryKey: ['task', taskIdFromUrl],
    queryFn: () => getTask(Number(taskIdFromUrl)),
    enabled: !!taskIdFromUrl,
    onSuccess: (data) => {
      if (data) {
        setCurrentTask(data);
        setIsTaskDetailModalOpen(true);
      }
    },
    onError: (error) => {
      console.error('Error loading task from URL:', error);
      toast({
        title: "Task not found",
        description: "The requested task could not be loaded",
        variant: "destructive"
      });
    }
  });

  // Mutations
  const addListMutation = useMutation({
    mutationFn: createList,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lists', projectId] });
      toast({ title: "List created successfully" });
      resetListForm();
    },
    onError: (error: any) => {
      // Check if this is a viewer permission error
      if (error.response?.data?.role === 'viewer' && error.response?.data?.can_view === true) {
        toast({
          title: "View-Only Access",
          description: "As a viewer, you can see the project but cannot add lists.",
          variant: "default"
        });
        return;
      }

      toast({
        title: "Failed to create list",
        variant: "destructive"
      });
    }
  });

  const updateListMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: Partial<List> }) =>
      updateList(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lists', projectId] });
      toast({ title: "List updated successfully" });
      resetListForm();
    },
    onError: () => {
      toast({
        title: "Failed to update list",
        variant: "destructive"
      });
    }
  });

  const deleteListMutation = useMutation({
    mutationFn: deleteList,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lists', projectId] });
      toast({ title: "List deleted successfully" });
    },
    onError: () => {
      toast({
        title: "Failed to delete list",
        variant: "destructive"
      });
    }
  });

  const addTaskMutation = useMutation({
    mutationFn: createTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
      toast({ title: "Task created successfully" });
      resetTaskForm();
    },
    onError: () => {
      toast({
        title: "Failed to create task",
        variant: "destructive"
      });
    }
  });

  const updateTaskMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: Partial<Task> }) =>
      updateTask(id, data),
    onSuccess: () => {
      // Invalidate both tasks and project data to ensure project cards are updated
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({ title: "Task updated successfully" });
      resetTaskForm();
    },
    onError: () => {
      toast({
        title: "Failed to update task",
        variant: "destructive"
      });
    }
  });

  const deleteTaskMutation = useMutation({
    mutationFn: deleteTask,
    onSuccess: () => {
      // Invalidate both tasks and project data to ensure project cards are updated
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({ title: "Task deleted successfully" });
    },
    onError: () => {
      toast({
        title: "Failed to delete task",
        variant: "destructive"
      });
    }
  });

  const reorderTasksMutation = useMutation({
    mutationFn: ({ listId, taskIds }: { listId: number, taskIds: number[] }) =>
      reorderTasks(listId, taskIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
    },
    onError: () => {
      toast({
        title: "Failed to reorder tasks",
        variant: "destructive"
      });
    }
  });

  // Helper functions
  const getTasksForList = (listId: number) => {
    // Fix the filter to safely check for list_id existence
    return tasks.filter(task => task.list_id === listId);
  };

  // Handle drag end event
  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If there's no destination or the item was dropped back to its original position
    if (!destination ||
        (destination.droppableId === source.droppableId &&
         destination.index === source.index)) {
      return;
    }

    // Check if user has edit_project permission (only admins and editors, not viewers)
    if (!hasProjectPermission(project, user, ProjectPermission.EDIT_PROJECT)) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to move tasks. Only admins and editors can edit tasks.",
        variant: "destructive"
      });
      return;
    }

    // Find the task that was dragged
    const taskId = parseInt(draggableId.replace('task-', ''));
    const task = tasks.find(t => t.id === taskId);

    if (!task) return;

    // Get the source and destination list IDs
    const sourceListId = parseInt(source.droppableId.replace('list-', ''));
    const destinationListId = parseInt(destination.droppableId.replace('list-', ''));

    // If the task was moved to a different list
    if (source.droppableId !== destination.droppableId) {
      // Update the task with the new list ID
      updateTaskMutation.mutate({
        id: taskId,
        data: {
          ...task,
          list_id: destinationListId
        }
      });

      // Reorder tasks in both source and destination lists
      const sourceTasks = getTasksForList(sourceListId);
      const destinationTasks = getTasksForList(destinationListId);

      // Create a new array of task IDs for the source list (excluding the moved task)
      const newSourceTaskIds = sourceTasks
        .filter(t => t.id !== taskId)
        .map(t => t.id);

      // Create a new array of task IDs for the destination list (including the moved task)
      const newDestinationTaskIds = [...destinationTasks.map(t => t.id)];
      // Insert the moved task at the destination index
      newDestinationTaskIds.splice(destination.index, 0, taskId);

      // Update the order in the source list
      if (newSourceTaskIds.length > 0) {
        reorderTasksMutation.mutate({
          listId: sourceListId,
          taskIds: newSourceTaskIds
        });
      }

      // Update the order in the destination list
      reorderTasksMutation.mutate({
        listId: destinationListId,
        taskIds: newDestinationTaskIds
      });
    } else {
      // Task was reordered within the same list
      const listTasks = getTasksForList(sourceListId);

      // Create a new array of task IDs for the list
      const newTaskIds = [...listTasks.map(t => t.id)];

      // Remove the task from its old position
      newTaskIds.splice(source.index, 1);
      // Insert the task at its new position
      newTaskIds.splice(destination.index, 0, taskId);

      // Update the order in the list
      reorderTasksMutation.mutate({
        listId: sourceListId,
        taskIds: newTaskIds
      });
    }
  };

  const handleAddList = () => {
    setCurrentList(null);
    setNewListTitle('');
    setNewListDescription('');
    setIsListDialogOpen(true);
  };

  const handleEditList = (list: List) => {
    setCurrentList(list);
    setNewListTitle(list.title);
    setNewListDescription(list.description || '');
    setIsListDialogOpen(true);
  };

  const handleDeleteList = (listId: number) => {
    setListToDelete(listId);
    setIsDeleteListDialogOpen(true);
  };

  const confirmDeleteList = () => {
    if (listToDelete !== null) {
      deleteListMutation.mutate(listToDelete);
      setIsDeleteListDialogOpen(false);
      setListToDelete(null);
    }
  };

  const handleSubmitList = () => {
    if (!newListTitle.trim()) {
      toast({
        title: "List title is required",
        variant: "destructive"
      });
      return;
    }

    if (currentList) {
      // Update existing list
      updateListMutation.mutate({
        id: currentList.id,
        data: {
          title: newListTitle,
          description: newListDescription
        }
      });
    } else {
      // Create new list
      addListMutation.mutate({
        project_id: Number(projectId),
        title: newListTitle,
        description: newListDescription,
        position: lists.length // Add as last list
      });
    }

    setIsListDialogOpen(false);
  };

  const handleAddTask = (listId: number) => {
    // Set the current list for new task
    const list = lists.find(l => l.id === listId);
    if (list) {
      console.log(`Setting current list for new task: ${list.id} - ${list.title}`);
      setCurrentList(list);
      // Use the new CreateTaskModal instead of the old dialog
      setIsCreateTaskModalOpen(true);
    } else {
      console.error(`List with ID ${listId} not found`);
      toast({
        title: "Error",
        description: "Could not find the selected list. Please try again or create a new list.",
        variant: "destructive"
      });
    }
  };

  const handleTaskClick = (task: Task) => {
    setCurrentTask(task);
    // Show the task detail modal instead of the edit dialog
    setIsTaskDetailModalOpen(true);
  };

  // Handle project conversion to group project
  const handleProjectConverted = (updatedProject) => {
    // Update the local project state immediately
    setLocalProject(updatedProject);

    // Force a refresh of the project data
    queryClient.invalidateQueries({ queryKey: ['project', projectId] });

    // Show a success message
    toast({
      title: "Project converted",
      description: "This project is now a group project. You can add team members to collaborate.",
    });
  };

  const handleEditTask = () => {
    if (!currentTask) return;

    // Check if user has edit_project permission (only admins and editors, not viewers)
    if (!hasProjectPermission(project, user, ProjectPermission.EDIT_PROJECT)) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to edit tasks. Only admins and editors can edit tasks.",
        variant: "destructive"
      });
      return;
    }

    // Double-check that viewers cannot edit tasks at all
    if (project && user && project.members) {
      const userRole = project.members.find(member => member.id === user.id)?.pivot?.role_id;
      if (userRole === 3) { // Viewer role
        toast({
          title: "Permission Denied",
          description: "As a viewer, you cannot edit tasks.",
          variant: "destructive"
        });
        return;
      }
    }

    // Set up form for editing
    setNewTaskTitle(currentTask.title);
    setNewTaskDescription(currentTask.description || '');
    setNewTaskPriority(currentTask.priority || 'medium');
    setNewTaskStatus(currentTask.status || 'not-started');
    setNewTaskDueDate(currentTask.end_date ? new Date(currentTask.end_date).toISOString().split('T')[0] : '');

    // Set selected assignees if the task has any
    if (currentTask.assignees && Array.isArray(currentTask.assignees)) {
      const assigneeIds = currentTask.assignees.map(assignee => assignee.id);
      setSelectedAssignees(assigneeIds);
    } else {
      setSelectedAssignees([]);
    }

    // Get the list for this task
    const list = lists.find(l => l.id === currentTask.list_id);
    if (list) {
      setCurrentList(list);
    }

    // Close the detail modal and open the edit dialog
    setIsTaskDetailModalOpen(false);
    setIsTaskDialogOpen(true);
  };

  const handleSubmitTask = async () => {
    if (!newTaskTitle.trim()) {
      toast({
        title: "Task title is required",
        variant: "destructive"
      });
      return;
    }

    const taskData = {
      project_id: Number(projectId),
      list_id: currentList?.id,
      title: newTaskTitle,
      description: newTaskDescription,
      priority: newTaskPriority,
      status: newTaskStatus,
      end_date: newTaskDueDate || null
    };

    try {
      if (currentTask) {
        // Update existing task
        await updateTaskMutation.mutateAsync({
          id: currentTask.id,
          data: taskData
        });

        // Ensure selectedAssignees is an array
        const validSelectedAssignees = Array.isArray(selectedAssignees) ? selectedAssignees : [];

        if (currentTask.assignees && Array.isArray(currentTask.assignees)) {
          const currentAssigneeIds = currentTask.assignees.map(a => a.id);

          // Find assignees to add and remove
          const assigneesToAdd = validSelectedAssignees.filter(id => !currentAssigneeIds.includes(id));
          const assigneesToRemove = currentAssigneeIds.filter(id => !validSelectedAssignees.includes(id));

          // Add new assignees if any
          if (assigneesToAdd.length > 0) {
            await addTaskAssignees(currentTask.id, assigneesToAdd);
          }

          // Remove assignees if any
          if (assigneesToRemove.length > 0) {
            await removeTaskAssignees(currentTask.id, assigneesToRemove);
          }
        } else if (validSelectedAssignees.length > 0) {
          // If task had no assignees but now has some
          await addTaskAssignees(currentTask.id, validSelectedAssignees);
        }
      } else {
        // Create new task
        const newTask = await addTaskMutation.mutateAsync(taskData);

        // Add assignees to the new task if any are selected
        // Ensure selectedAssignees is an array
        const validSelectedAssignees = Array.isArray(selectedAssignees) ? selectedAssignees : [];

        if (validSelectedAssignees.length > 0 && newTask && newTask.id) {
          await addTaskAssignees(newTask.id, validSelectedAssignees);
        }
      }

      // Refresh tasks data
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });

      setIsTaskDialogOpen(false);
    } catch (error) {
      console.error('Error handling task submission:', error);
      toast({
        title: "Error",
        description: "There was a problem saving the task",
        variant: "destructive"
      });
    }
  };

  const handleDeleteTask = () => {
    if (currentTask) {
      setIsDeleteDialogOpen(true);
    }
  };

  const confirmDeleteTask = () => {
    if (currentTask) {
      deleteTaskMutation.mutate(currentTask.id);
      setIsTaskDialogOpen(false);
      setIsDeleteDialogOpen(false);
    }
  };

  const resetListForm = () => {
    setCurrentList(null);
    setNewListTitle('');
    setNewListDescription('');
  };

  const resetTaskForm = () => {
    setCurrentTask(null);
    setNewTaskTitle('');
    setNewTaskDescription('');
    setNewTaskPriority('medium');
    setNewTaskStatus('not-started');
    setNewTaskDueDate('');
    setSelectedAssignees([]);
  };

  // Handle filter changes - memoized with useCallback to prevent infinite loops
  const handleFilterChange = useCallback((filters: TaskFilter) => {
    // Ensure we're not updating with the same values to prevent unnecessary re-renders
    setTaskFilters(prevFilters => {
      // Only update if the filters have actually changed
      if (JSON.stringify(prevFilters) !== JSON.stringify(filters)) {
        return filters;
      }
      return prevFilters;
    });
  }, []);

  // Handle sort changes - memoized with useCallback
  const handleSortChange = useCallback((field: string, direction: 'asc' | 'desc') => {
    setSortField(field);
    setSortDirection(direction);
  }, []);

  // Check if user has permission to view the project
  const canViewProject = hasProjectPermission(project, user, ProjectPermission.VIEW_PROJECT);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">Loading project data...</p>
          </div>
        </main>
      </div>
    );
  }

  // Handle project error
  if (projectError) {
    console.error('Project error details:', projectError);
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Project Not Found</h2>
              <p className="text-muted-foreground mb-4">
                The project you're looking for doesn't exist or you don't have permission to view it.
              </p>
              <div className="text-sm text-red-500 mb-4">
                Error: {projectError?.message || 'Unknown error'}
                {projectError?.response?.data?.message && (
                  <div>Server message: {projectError.response.data.message}</div>
                )}
              </div>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate('/dashboard')}
              >
                Go to Dashboard
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!canViewProject) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">You don't have permission to view this project.</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate('/dashboard')}
              >
                Go to Dashboard
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="max-w-full mx-auto px-4 py-6">
        {/* Breadcrumbs */}
        <div className="max-w-7xl mx-auto mb-4">
          <PageBreadcrumbs projectName={(localProject || project)?.name} />
        </div>

        {/* Header */}
        <div className="max-w-7xl mx-auto mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <h1 className="text-3xl font-bold text-foreground">
                  {(localProject || project) ? (localProject || project).name : 'Project Board'}
                </h1>
              </div>
              <div className="flex items-center gap-2 mt-1">
                <p className="text-muted-foreground">
                  {(localProject || project)?.description || 'Manage your tasks and track progress'}
                </p>
                {/* Use localProject if available, otherwise use project */}
                {(localProject || project) && !(localProject || project).is_group_project && (
                  <ConvertToGroupProjectButton
                    project={localProject || project}
                    user={user}
                    onProjectConverted={handleProjectConverted}
                  />
                )}
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => setIsTeamModalOpen(true)}
              >
                <Users size={16} className="mr-1.5" />
                Team
              </Button>
              <ViewsDropdown projectId={projectId} />
              {hasProjectPermission(project, user, ProjectPermission.EDIT_PROJECT) && (
                <Button onClick={handleAddList} className="flex items-center">
                  <Plus size={16} className="mr-1.5" />
                  Add List
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Former Members Manager */}
        {project && project.is_group_project && (
          <div className="max-w-7xl mx-auto mb-6">
            <FormerMembersManager project={project} />
          </div>
        )}

        {/* Task Filter Panel */}
        <div className="max-w-7xl mx-auto mb-6">
          <TaskFilterPanel
            projectId={Number(projectId)}
            users={projectUsers}
            onFilterChange={handleFilterChange}
            onSortChange={handleSortChange}
          />
        </div>

        {/* Kanban Board */}
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="overflow-x-auto pb-4">
            <div className="flex gap-4 min-h-[calc(100vh-230px)]">
              {Array.isArray(lists) && lists.length > 0 ? (
                lists.map(list => (
                  <Droppable droppableId={`list-${list.id}`} key={list.id}>
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="h-full"
                      >
                        <KanbanColumn
                          list={list}
                          tasks={getTasksForList(list.id)}
                          project={project}
                          onAddTask={handleAddTask}
                          onEditList={handleEditList}
                          onDeleteList={handleDeleteList}
                          onTaskClick={handleTaskClick}
                        />
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                ))
              ) : (
                <div className="w-full flex justify-center items-center">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-4">No lists found. Create your first list to get started.</p>
                    <Button onClick={handleAddList} className="flex items-center mx-auto">
                      <Plus size={16} className="mr-1.5" />
                      Add List
                    </Button>
                  </div>
                </div>
              )}

              {/* Add list button - shown at the end */}
              {Array.isArray(lists) && hasProjectPermission(project, user, ProjectPermission.CREATE_TASK) && (
                <div className="min-w-[280px] w-[280px] flex items-start">
                  <button
                    onClick={handleAddList}
                    className="w-full p-3 border border-dashed border-muted-foreground/30 rounded-lg text-muted-foreground hover:text-primary hover:border-primary/30 transition-colors flex items-center justify-center"
                  >
                    <Plus size={16} className="mr-1.5" />
                    Add List
                  </button>
                </div>
              )}
            </div>
          </div>
        </DragDropContext>
      </main>

      {/* List Dialog */}
      <Dialog open={isListDialogOpen} onOpenChange={setIsListDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{currentList ? 'Edit List' : 'Add New List'}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="list-title">List Title</Label>
              <Input
                id="list-title"
                value={newListTitle}
                onChange={(e) => setNewListTitle(e.target.value)}
                placeholder="Enter list title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="list-description">Description (optional)</Label>
              <Textarea
                id="list-description"
                value={newListDescription}
                onChange={(e) => setNewListDescription(e.target.value)}
                placeholder="Enter list description"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button onClick={handleSubmitList}>
              {currentList ? 'Update List' : 'Create List'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Task Dialog */}
      <Dialog open={isTaskDialogOpen} onOpenChange={setIsTaskDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{currentTask ? 'Edit Task' : 'Add New Task'}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="task-title">Task Title</Label>
              <Input
                id="task-title"
                value={newTaskTitle}
                onChange={(e) => setNewTaskTitle(e.target.value)}
                placeholder="Enter task title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="task-description">Description (optional)</Label>
              <Textarea
                id="task-description"
                value={newTaskDescription}
                onChange={(e) => setNewTaskDescription(e.target.value)}
                placeholder="Enter task description"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="task-status">Status</Label>
                <Select
                  value={newTaskStatus}
                  onValueChange={setNewTaskStatus}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="not-started">Not Started</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="at-risk">At Risk</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="task-priority">Priority</Label>
                <Select
                  value={newTaskPriority}
                  onValueChange={setNewTaskPriority}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="task-due-date">Due Date (optional)</Label>
              <Input
                id="task-due-date"
                type="date"
                value={newTaskDueDate}
                onChange={(e) => setNewTaskDueDate(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="task-assignees">Assignees</Label>
              {project && project.members ? (
                <UserMultiSelect
                  users={project.members}
                  selectedUserIds={selectedAssignees}
                  onChange={(ids) => {
                    setSelectedAssignees(ids);
                  }}
                  placeholder="Assign team members"
                />
              ) : (
                <div className="text-sm text-muted-foreground">
                  Loading team members...
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex items-center justify-between">
            <div>
              {currentTask && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDeleteTask}
                >
                  Delete Task
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button onClick={handleSubmitTask}>
                {currentTask ? 'Update Task' : 'Create Task'}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Team Members Modal - Only render when needed to prevent unnecessary renders */}
      {isTeamModalOpen && (
        <TeamMembersModal
          isOpen={isTeamModalOpen}
          onClose={() => setIsTeamModalOpen(false)}
          project={localProject || project}
          onMemberUpdated={() => {
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
          }}
        />
      )}

      {/* Timeline View Modal */}
      <TimelineView
        isOpen={isTimelineModalOpen}
        onClose={() => setIsTimelineModalOpen(false)}
        project={project}
        tasks={tasks}
      />

      {/* Task Detail Modal */}
      <TaskDetailModal
        isOpen={isTaskDetailModalOpen}
        onClose={() => setIsTaskDetailModalOpen(false)}
        task={currentTask}
        project={project}
        user={user}
        onEditTask={handleEditTask}
      />

      {/* New Create Task Modal */}
      {project && (
        <CreateTaskModal
          isOpen={isCreateTaskModalOpen}
          onClose={() => setIsCreateTaskModalOpen(false)}
          onSuccess={() => {
            queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
            toast({ title: "Task created successfully" });
          }}
          project={project}
          columnId={currentList?.id?.toString()}
        />
      )}

      {/* Debug info for lists - hidden in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className="hidden">
          <p>Available Lists: {JSON.stringify(lists.map(l => ({ id: l.id, title: l.title })))}</p>
          <p>Current List: {currentList ? `${currentList.id} - ${currentList.title}` : 'None'}</p>
        </div>
      )}

      {/* Delete Task Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this task?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the task
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTask}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete List Confirmation Dialog */}
      <AlertDialog open={isDeleteListDialogOpen} onOpenChange={setIsDeleteListDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this list?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the list
              and all tasks within it.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteList}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default KanbanBoard;
