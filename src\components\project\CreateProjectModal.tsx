import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useWorkspace } from '@/context/WorkspaceContext';
import { createProject } from '@/api/projectsApi';
import { getWorkspaces } from '@/api/workspacesApi';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { CalendarI<PERSON>, Loader2, CircleDash<PERSON>, Clock, CheckCircle } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { ProjectPriority, ProjectProgress } from '@/entities/Project';

interface CreateProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { workspaces, currentWorkspace, fetchWorkspaces } = useWorkspace();
  const { toast } = useToast();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingWorkspaces, setIsLoadingWorkspaces] = useState(false);
  const [availableWorkspaces, setAvailableWorkspaces] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    workspace_id: currentWorkspace?.id || '',
    start_date: null as Date | null,
    end_date: null as Date | null,
    priority: ProjectPriority.Medium,
    progress: ProjectProgress.NotStarted,
    is_group_project: false
  });

  // Fetch workspaces when modal opens
  useEffect(() => {
    if (isOpen) {
      // Always load fresh workspaces when the modal opens
      loadWorkspaces();

      // Also fetch workspaces through the context to ensure it's up to date
      fetchWorkspaces().catch(error => {
        console.error('Error fetching workspaces from context:', error);
      });
    }
  }, [isOpen]);

  // Load workspaces if not already available
  const loadWorkspaces = async () => {
    try {
      setIsLoadingWorkspaces(true);
      const data = await getWorkspaces();
      console.log('Available workspaces:', data);
      setAvailableWorkspaces(data);

      // If no current workspace is set but we have workspaces, use the first one
      if ((!currentWorkspace?.id || !formData.workspace_id) && data && data.length > 0) {
        console.log(`Setting workspace_id to first available workspace: ${data[0].id}`);
        setFormData(prev => ({
          ...prev,
          workspace_id: data[0].id
        }));
      } else if (currentWorkspace?.id) {
        // Make sure we're using the current workspace ID
        console.log(`Using current workspace ID: ${currentWorkspace.id}`);
        setFormData(prev => ({
          ...prev,
          workspace_id: currentWorkspace.id
        }));
      }

      // Clear any potentially invalid workspace ID from localStorage
      if (data && data.length > 0) {
        const validWorkspaceIds = data.map(w => w.id);
        const currentId = Number(currentWorkspace?.id);

        if (currentId && !validWorkspaceIds.includes(currentId)) {
          console.warn(`Current workspace ID ${currentId} is not in the list of valid workspaces. Will use first available workspace.`);
          // Use the first available workspace
          setFormData(prev => ({
            ...prev,
            workspace_id: data[0].id
          }));
        }
      }
    } catch (error) {
      console.error('Error loading workspaces:', error);
      toast({
        title: "Failed to load workspaces",
        description: "Could not load your workspaces. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingWorkspaces(false);
    }
  };

  // Update workspace_id when currentWorkspace changes
  useEffect(() => {
    if (currentWorkspace?.id) {
      setFormData(prev => ({
        ...prev,
        workspace_id: currentWorkspace.id
      }));
    }
  }, [currentWorkspace]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (name: string, date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      [name]: date
    }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      is_group_project: checked
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast({
        title: "Project name is required",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Force a refresh of workspaces to ensure we have the latest data
      if (!availableWorkspaces || availableWorkspaces.length === 0) {
        await loadWorkspaces();
      }

      // Determine which workspace ID to use
      let workspaceId = formData.workspace_id;

      // If no workspace is selected, check available workspaces
      if (!workspaceId) {
        // If we have available workspaces, use the first one
        if (availableWorkspaces && availableWorkspaces.length > 0) {
          workspaceId = availableWorkspaces[0].id;
          console.log(`No workspace selected, using first available workspace: ${workspaceId}`);
        }
        // If we have a current workspace, use that
        else if (currentWorkspace?.id) {
          workspaceId = currentWorkspace.id;
          console.log(`Using current workspace: ${workspaceId}`);
        }
        // If we still don't have a workspace, try to fetch workspaces
        else {
          toast({
            title: "No workspace available",
            description: "Please create a workspace before creating a project.",
            variant: "destructive"
          });
          setIsSubmitting(false);
          return;
        }
      }

      // Validate that the workspace ID is a number
      if (isNaN(Number(workspaceId))) {
        toast({
          title: "Invalid workspace",
          description: "The selected workspace is invalid. Please select a different workspace.",
          variant: "destructive"
        });
        setIsSubmitting(false);
        return;
      }

      // Validate that the workspace ID exists in available workspaces
      if (availableWorkspaces && availableWorkspaces.length > 0) {
        const validWorkspaceIds = availableWorkspaces.map(w => Number(w.id));
        if (!validWorkspaceIds.includes(Number(workspaceId))) {
          console.warn(`Workspace ID ${workspaceId} is not in the list of valid workspaces: ${validWorkspaceIds.join(', ')}`);

          // Use the first available workspace instead
          workspaceId = availableWorkspaces[0].id;
          console.log(`Using first available workspace instead: ${workspaceId}`);

          toast({
            title: "Workspace changed",
            description: "The selected workspace was invalid. Using your default workspace instead.",
            variant: "default"
          });
        }
      }

      const projectData = {
        ...formData,
        workspace_id: Number(workspaceId),
        priority: Number(formData.priority),
        progress: Number(formData.progress),
        start_date: formData.start_date ? format(formData.start_date, 'yyyy-MM-dd') : null,
        end_date: formData.end_date ? format(formData.end_date, 'yyyy-MM-dd') : null,
      };

      console.log('Creating project with data:', projectData);
      const response = await createProject(projectData);

      toast({
        title: "Project created successfully",
        description: `${formData.name} has been created.`
      });

      // Close the modal
      onClose();

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Navigate to the new project
      navigate(`/projects/${response.id}/board`);

    } catch (error: any) {
      console.error("Error creating project:", error);

      // Check for specific error types
      let errorMessage = "An error occurred while creating the project. Please try again.";
      let shouldRefreshWorkspaces = false;
      let shouldUseFirstWorkspace = false;

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error(`Error status: ${error.response.status}`);
        console.error('Error data:', error.response.data);

        // Check for specific error codes from the backend
        if (error.error_code) {
          console.log(`Handling specific error code: ${error.error_code}`);

          switch (error.error_code) {
            case 'workspace_not_found':
              errorMessage = "The selected workspace doesn't exist. Refreshing workspaces...";
              shouldRefreshWorkspaces = true;
              shouldUseFirstWorkspace = true;
              break;

            case 'workspace_access_denied':
              errorMessage = "You don't have access to the selected workspace. This is likely a system error as you should have access to your own workspaces.";
              shouldRefreshWorkspaces = true;
              shouldUseFirstWorkspace = true;

              // If the server provided available workspaces, use the first one
              if (error.available_workspaces && error.available_workspaces.length > 0) {
                console.log(`Using first available workspace from error response: ${error.available_workspaces[0]}`);
                setFormData(prev => ({
                  ...prev,
                  workspace_id: error.available_workspaces[0]
                }));
              }
              break;

            case 'workspace_verification_error':
              errorMessage = "There was an error verifying your workspace access. Refreshing workspaces...";
              shouldRefreshWorkspaces = true;
              break;

            default:
              // For unknown error codes, use the message from the response
              if (error.response.data && error.response.data.message) {
                errorMessage = error.response.data.message;
              }
          }
        }
        // Fall back to status code checking if no error_code is present
        else if (error.response.status === 404) {
          if (error.response.data && error.response.data.message && error.response.data.message.includes('Workspace')) {
            errorMessage = "The selected workspace doesn't exist or you don't have access to it. Refreshing workspaces...";
            shouldRefreshWorkspaces = true;
            shouldUseFirstWorkspace = true;
          }
        } else if (error.response.status === 403) {
          errorMessage = "You don't have permission to create a project in this workspace. This is likely a system error as you should have access to your own workspaces.";
          shouldRefreshWorkspaces = true;
          shouldUseFirstWorkspace = true;
        } else if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = "No response from server. Please check your internet connection and try again.";
      }

      toast({
        title: "Failed to create project",
        description: errorMessage,
        variant: "destructive"
      });

      // If we need to refresh workspaces, do it now
      if (shouldRefreshWorkspaces) {
        try {
          // Force refresh both local and context workspaces
          await loadWorkspaces();
          await fetchWorkspaces();

          // If we have workspaces now and should use the first one, do so
          if (availableWorkspaces && availableWorkspaces.length > 0) {
            if (shouldUseFirstWorkspace) {
              console.log(`Setting workspace_id to first available workspace after refresh: ${availableWorkspaces[0].id}`);
              setFormData(prev => ({
                ...prev,
                workspace_id: availableWorkspaces[0].id
              }));
            }

            toast({
              title: "Workspaces refreshed",
              description: "Please try creating your project again with the refreshed workspace data.",
              variant: "default"
            });
          }
        } catch (refreshError) {
          console.error("Error refreshing workspaces:", refreshError);
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[550px] p-6 md:p-8">
        <DialogHeader className="mb-2">
          <DialogTitle className="text-xl">Create New Project</DialogTitle>
          <DialogDescription>
            Fill in the details to create a new project.
          </DialogDescription>
        </DialogHeader>

        {isLoadingWorkspaces ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading workspaces...</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-5 mt-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">Project Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter project name"
                required
                className="h-10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter project description"
                rows={3}
                className="resize-none"
              />
            </div>

            {/* Workspace info - always show which workspace will be used */}
            <div className="p-3 bg-muted/50 rounded-md border border-border">
              <div className="flex items-center">
                <div className="mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                    <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                    <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4"></path>
                  </svg>
                </div>
                <div>
                  <div className="text-sm font-medium">Workspace</div>
                  <div className="text-sm text-muted-foreground">
                    {availableWorkspaces && availableWorkspaces.length > 0 ? (
                      <>
                        Project will be created in: <strong>{
                          availableWorkspaces.find(w => w.id === Number(formData.workspace_id))?.name ||
                          (currentWorkspace?.name || 'Default workspace')
                        }</strong>
                      </>
                    ) : (
                      <>Loading workspaces...</>
                    )}
                  </div>
                </div>
              </div>
            </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal h-10"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                    {formData.start_date ? (
                      format(formData.start_date, 'PPP')
                    ) : (
                      <span className="text-muted-foreground">Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={formData.start_date || undefined}
                    onSelect={(date) => handleDateChange('start_date', date)}
                    initialFocus
                    className="rounded-md border"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal h-10"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                    {formData.end_date ? (
                      format(formData.end_date, 'PPP')
                    ) : (
                      <span className="text-muted-foreground">Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={formData.end_date || undefined}
                    onSelect={(date) => handleDateChange('end_date', date)}
                    initialFocus
                    className="rounded-md border"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority" className="text-sm font-medium">Priority</Label>
              <Select
                value={formData.priority.toString()}
                onValueChange={(value) => handleSelectChange('priority', value)}
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ProjectPriority.Low.toString()}>
                    <div className="flex items-center">
                      <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                      Low
                    </div>
                  </SelectItem>
                  <SelectItem value={ProjectPriority.Medium.toString()}>
                    <div className="flex items-center">
                      <span className="h-2 w-2 rounded-full bg-yellow-500 mr-2"></span>
                      Medium
                    </div>
                  </SelectItem>
                  <SelectItem value={ProjectPriority.High.toString()}>
                    <div className="flex items-center">
                      <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                      High
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="progress" className="text-sm font-medium">Status</Label>
              <Select
                value={formData.progress.toString()}
                onValueChange={(value) => handleSelectChange('progress', value)}
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ProjectProgress.NotStarted.toString()}>
                    <div className="flex items-center">
                      <CircleDashed className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                      Not Started
                    </div>
                  </SelectItem>
                  <SelectItem value={ProjectProgress.InProgress.toString()}>
                    <div className="flex items-center">
                      <Clock className="h-3.5 w-3.5 mr-2 text-blue-500" />
                      In Progress
                    </div>
                  </SelectItem>
                  <SelectItem value={ProjectProgress.Completed.toString()}>
                    <div className="flex items-center">
                      <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-500" />
                      Completed
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2 bg-muted/30 p-3 rounded-md">
            <Switch
              id="is_group_project"
              checked={formData.is_group_project}
              onCheckedChange={handleSwitchChange}
            />
            <Label htmlFor="is_group_project" className="font-medium">Group Project (can be shared with team members)</Label>
          </div>

          <DialogFooter className="mt-6 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting} className="px-5">
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="px-5">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Project'
              )}
            </Button>
          </DialogFooter>
        </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateProjectModal;
