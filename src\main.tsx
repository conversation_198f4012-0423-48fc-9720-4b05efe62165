
// Import React shim first to ensure React is available globally
import './lib/react-shim.js';
import React from "react";
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import './i18n'; // Import i18n configuration

// Suppress react-beautiful-dnd warnings about defaultProps
const originalConsoleError = console.error;
console.error = function(msg) {
  if (typeof msg === 'string' &&
      (msg.includes('defaultProps will be removed') ||
       msg.includes('Missing `Description` or `aria-describedby`'))) {
    return;
  }
  originalConsoleError.apply(console, arguments);
};

// Only use StrictMode in development to avoid double-rendering in production
const isDevelopment = import.meta.env.MODE === 'development';

createRoot(document.getElementById('root')!).render(
  isDevelopment ? (
    <React.StrictMode>
      <App />
    </React.StrictMode>
  ) : (
    <App />
  )
);
