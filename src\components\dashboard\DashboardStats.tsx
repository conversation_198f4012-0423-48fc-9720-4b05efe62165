import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Clock, CheckCircle, AlertTriangle, Loader2 } from 'lucide-react';
import { Project, ProjectProgress } from '@/entities/Project';
import { getAllProjects } from '@/api/projectsApi';
import { useToast } from '@/components/ui/use-toast';
import Loader from '@/components/Loader';
import { useTranslation } from 'react-i18next';
import StatusCard from '@/components/ui/status-card';
import { motion } from 'framer-motion';

const DashboardStats: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    const fetchProjects = async () => {
      setIsLoading(true);
      try {
        const data = await getAllProjects();
        setProjects(data);
      } catch (error) {
        toast({
          title: "Failed to load statistics",
          description: "Could not load project statistics. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, [toast]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Calculate statistics
  const totalProjects = projects.length;

  const completedProjects = projects.filter(
    project => project.progress === ProjectProgress.Completed
  ).length;

  const inProgressProjects = projects.filter(
    project => project.progress === ProjectProgress.InProgress
  ).length;

  // Calculate upcoming deadlines (projects due in the next 7 days)
  const now = new Date();
  const nextWeek = new Date();
  nextWeek.setDate(now.getDate() + 7);

  const upcomingDeadlines = projects.filter(project => {
    if (!project.end_date) return false;
    const endDate = new Date(project.end_date);
    return endDate >= now && endDate <= nextWeek;
  }).length;

  // Calculate completion rate
  const completionRate = totalProjects > 0
    ? Math.round((completedProjects / totalProjects) * 100)
    : 0;

  // Calculate trends (mock data for now - in a real app, this would come from historical data)
  const projectTrend = {
    value: 5,
    type: 'increase' as const
  };

  const completionTrend = {
    value: 8,
    type: 'increase' as const
  };

  const deadlineTrend = {
    value: 2,
    type: 'decrease' as const
  };

  // Animation variants for staggered animation
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1, transition: { type: "spring", stiffness: 300, damping: 24 } }
  };

  return (
    <motion.div
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div variants={item}>
        <StatusCard
          title={t('dashboard.totalProjects', 'Total Projects')}
          value={totalProjects}
          icon={<BarChart className="h-5 w-5 text-primary" />}
          change={projectTrend}
        />
      </motion.div>
      <motion.div variants={item}>
        <StatusCard
          title={t('dashboard.inProgress', 'In Progress')}
          value={inProgressProjects}
          icon={<Clock className="h-5 w-5 text-info" />}
          description={t('dashboard.percentOfAllProjects', '{{percent}}% of all projects', {
            percent: inProgressProjects > 0 ? Math.round((inProgressProjects / totalProjects) * 100) : 0
          })}
        />
      </motion.div>
      <motion.div variants={item}>
        <StatusCard
          title={t('dashboard.completed', 'Completed')}
          value={completedProjects}
          icon={<CheckCircle className="h-5 w-5 text-success" />}
          description={t('dashboard.completionRate', '{{percent}}% completion rate', {
            percent: completionRate
          })}
          change={completionTrend}
        />
      </motion.div>
      <motion.div variants={item}>
        <StatusCard
          title={t('dashboard.upcomingDeadlines', 'Upcoming Deadlines')}
          value={upcomingDeadlines}
          icon={<AlertTriangle className="h-5 w-5 text-warning" />}
          description={t('dashboard.dueInNextWeek', 'Due in the next 7 days')}
          change={deadlineTrend}
        />
      </motion.div>
    </motion.div>
  );
};

export default DashboardStats;
