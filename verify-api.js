
#!/usr/bin/env node

/**
 * API Verification Script
 * 
 * This script verifies that all API endpoints used in the React frontend
 * are properly defined in the Laravel backend and returns the expected schema.
 */

const chalk = require('chalk');
const path = require('path');

// Import modules
const { findTsxFiles, extractApiCalls, inferSchemaNameFromUrl } = require('./verify-api/utils');
const { parseLaravelRoutes, endpointExistsInBackend } = require('./verify-api/laravel-routes');
const { generateJsonSchemas, validateSchema } = require('./verify-api/schema-validator');
const { testEndpoint } = require('./verify-api/api-tester');
const { generateReport, saveReport } = require('./verify-api/report-generator');

// Configuration
const config = {
  baseUrl: 'https://api.myapp.com/api',
  frontendDir: path.resolve(__dirname, 'src'),
  backendRoutesFile: path.resolve(__dirname, 'backend/routes/api.php'),
  tsConfigPath: path.resolve(__dirname, 'tsconfig.json'),
  typeFiles: [] // Will be filled with TypeScript interface files
};

/**
 * Main execution function
 */
async function main() {
  try {
    console.log(chalk.blue('Starting API verification...\n'));
    
    // Find all TSX files
    console.log(chalk.blue('Finding TypeScript files...'));
    const tsxFiles = findTsxFiles(config.frontendDir);
    console.log(chalk.green(`Found ${tsxFiles.length} TypeScript files\n`));
    
    // Collect type files
    config.typeFiles = tsxFiles.filter(file => 
      file.includes('Api.ts') || 
      file.includes('types.ts') || 
      file.includes('.d.ts')
    );
    
    // Extract API calls
    console.log(chalk.blue('Extracting API calls...'));
    let apiCalls = [];
    for (const file of tsxFiles) {
      const calls = extractApiCalls(file);
      apiCalls = apiCalls.concat(calls);
    }
    console.log(chalk.green(`Found ${apiCalls.length} API calls\n`));
    
    // Parse Laravel routes
    console.log(chalk.blue('Parsing Laravel routes...'));
    const laravelRoutes = parseLaravelRoutes(config.backendRoutesFile);
    console.log(chalk.green(`Found ${laravelRoutes.length} Laravel routes\n`));
    
    // Generate JSON schemas
    const schemas = generateJsonSchemas(config.typeFiles);
    
    // Verify each endpoint
    console.log(chalk.blue('Verifying endpoints...\n'));
    const results = [];
    
    for (const call of apiCalls) {
      const result = {
        url: call.url,
        method: call.method,
        existsInBackend: endpointExistsInBackend(call.method, call.url, laravelRoutes),
        response: await testEndpoint(call.method, call.url, call.payload, config.baseUrl)
      };
      
      // Check schema validation if we have a schema for this endpoint
      const schemaName = inferSchemaNameFromUrl(call.url);
      if (schemas[schemaName]) {
        const validation = validateSchema(result.response.data, schemas[schemaName]);
        result.schemaValid = validation.valid;
        result.expectedSchema = schemas[schemaName];
        
        if (!validation.valid) {
          result.issue = `Schema validation failed: ${validation.reason || JSON.stringify(validation.errors)}`;
          result.fixSuggestion = 'Update response to match TypeScript interface';
        }
      } else {
        result.schemaValid = true; // No schema to validate against
      }
      
      // Add issues and fix suggestions
      if (!result.existsInBackend) {
        result.issue = 'Endpoint not defined in Laravel routes';
        result.fixSuggestion = 'Add route definition to api.php';
      } else if (!result.response.success) {
        result.issue = `HTTP ${result.response.status}: ${result.response.data.message || 'Unknown error'}`;
        result.fixSuggestion = 'Check endpoint implementation and error handling';
      }
      
      results.push(result);
      
      // Log progress
      const status = result.response.success ? chalk.green('✓') : chalk.red('✗');
      console.log(`${status} ${result.method} ${result.url}`);
    }
    
    // Generate and save report
    console.log(chalk.blue('\nGenerating report...'));
    const report = generateReport(results);
    saveReport(report, 'api-verification-report.md');
    
    console.log(chalk.green('\nVerification complete! Report saved to api-verification-report.md'));
    
    // Display summary in console
    const totalIssues = results.filter(r => !r.existsInBackend || !r.response.success || !r.schemaValid).length;
    if (totalIssues > 0) {
      console.log(chalk.yellow(`\nFound ${totalIssues} issues that need attention.`));
    } else {
      console.log(chalk.green('\nAll endpoints are working correctly!'));
    }
    
  } catch (error) {
    console.error(chalk.red('\nError during verification:'), error);
    process.exit(1);
  }
}

// Run the script
main();
