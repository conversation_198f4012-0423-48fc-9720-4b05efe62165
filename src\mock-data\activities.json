[{"id": 1, "type": "project_created", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Website Redesign", "target_id": 1, "created_at": "2023-04-15T08:30:00Z"}, {"id": 2, "type": "task_completed", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Design wireframes", "target_id": 1, "created_at": "2023-04-25T14:20:00Z"}, {"id": 3, "type": "user_joined", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Website Redesign", "target_id": 1, "created_at": "2023-04-18T10:45:00Z"}, {"id": 4, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Design wireframes", "target_id": 1, "created_at": "2023-04-20T10:15:00Z"}, {"id": 5, "type": "project_created", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Mobile App Development", "target_id": 2, "created_at": "2023-05-01T09:15:00Z"}, {"id": 6, "type": "user_joined", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Mobile App Development", "target_id": 2, "created_at": "2023-05-02T11:30:00Z"}, {"id": 7, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Set up development environment", "target_id": 5, "created_at": "2023-05-08T14:30:00Z"}, {"id": 8, "type": "comment_added", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Research competitor apps", "target_id": 201, "created_at": "2023-05-03T14:20:00Z"}, {"id": 9, "type": "comment_added", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Define app architecture", "target_id": 202, "created_at": "2023-05-05T16:15:00Z"}, {"id": 10, "type": "project_created", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Marketing Campaign", "target_id": 3, "created_at": "2023-06-10T13:00:00Z"}, {"id": 11, "type": "user_joined", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Marketing Campaign", "target_id": 3, "created_at": "2023-06-11T09:15:00Z"}, {"id": 12, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Create marketing strategy", "target_id": 7, "created_at": "2023-06-24T16:15:00Z"}, {"id": 13, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Create marketing strategy", "target_id": 7, "created_at": "2023-06-21T09:15:00Z"}, {"id": 14, "type": "project_created", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Database Migration", "target_id": 4, "created_at": "2023-07-01T15:00:00Z"}, {"id": 15, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Database schema design", "target_id": 10, "created_at": "2023-07-14T16:45:00Z"}, {"id": 16, "type": "comment_added", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Data migration script", "target_id": 11, "created_at": "2023-07-20T13:15:00Z"}, {"id": 17, "type": "project_created", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "E-commerce Platform", "target_id": 5, "created_at": "2023-08-02T09:00:00Z"}, {"id": 18, "type": "user_joined", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "E-commerce Platform", "target_id": 5, "created_at": "2023-08-03T10:30:00Z"}, {"id": 19, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Requirements gathering", "target_id": 13, "created_at": "2023-08-03T09:45:00Z"}, {"id": 20, "type": "comment_added", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Architecture design", "target_id": 14, "created_at": "2023-08-04T11:15:00Z"}, {"id": 21, "type": "project_created", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Custom CMS", "target_id": 6, "created_at": "2023-07-16T10:00:00Z"}, {"id": 22, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "CMS feature list", "target_id": 16, "created_at": "2023-07-29T15:45:00Z"}, {"id": 23, "type": "user_joined", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Custom CMS", "target_id": 6, "created_at": "2023-07-20T09:30:00Z"}, {"id": 24, "type": "comment_added", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Database design", "target_id": 17, "created_at": "2023-08-01T11:20:00Z"}, {"id": 25, "type": "project_created", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Research Project", "target_id": 7, "created_at": "2023-06-02T10:00:00Z"}, {"id": 26, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Literature review", "target_id": 19, "created_at": "2023-06-28T16:30:00Z"}, {"id": 27, "type": "comment_added", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Data collection", "target_id": 20, "created_at": "2023-07-10T13:45:00Z"}, {"id": 28, "type": "project_created", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Mobile Game", "target_id": 8, "created_at": "2023-05-16T10:00:00Z"}, {"id": 29, "type": "user_joined", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Mobile Game", "target_id": 8, "created_at": "2023-05-20T11:30:00Z"}, {"id": 30, "type": "task_completed", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Game concept design", "target_id": 22, "created_at": "2023-05-30T15:30:00Z"}, {"id": 31, "type": "comment_added", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Game concept design", "target_id": 22, "created_at": "2023-05-31T09:15:00Z"}, {"id": 32, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Character design", "target_id": 23, "created_at": "2023-06-15T14:30:00Z"}, {"id": 33, "type": "project_created", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "UI Component Library", "target_id": 9, "created_at": "2023-08-10T10:00:00Z"}, {"id": 34, "type": "user_joined", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "UI Component Library", "target_id": 9, "created_at": "2023-08-11T09:30:00Z"}, {"id": 35, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Design system guidelines", "target_id": 25, "created_at": "2023-08-20T15:45:00Z"}, {"id": 36, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Design system guidelines", "target_id": 25, "created_at": "2023-08-21T10:30:00Z"}, {"id": 37, "type": "project_created", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "API Integration", "target_id": 10, "created_at": "2023-08-15T11:00:00Z"}, {"id": 38, "type": "user_joined", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "API Integration", "target_id": 10, "created_at": "2023-08-16T09:15:00Z"}, {"id": 39, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "API documentation", "target_id": 27, "created_at": "2023-08-25T16:30:00Z"}, {"id": 40, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "API documentation", "target_id": 27, "created_at": "2023-08-26T10:15:00Z"}, {"id": 41, "type": "task_completed", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Implement homepage", "target_id": 2, "created_at": "2023-05-15T16:30:00Z"}, {"id": 42, "type": "comment_added", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Implement homepage", "target_id": 2, "created_at": "2023-05-16T09:45:00Z"}, {"id": 43, "type": "task_completed", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Create responsive layouts", "target_id": 3, "created_at": "2023-05-25T14:30:00Z"}, {"id": 44, "type": "comment_added", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Create responsive layouts", "target_id": 3, "created_at": "2023-05-26T10:15:00Z"}, {"id": 45, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Design user onboarding flow", "target_id": 203, "created_at": "2023-05-10T15:30:00Z"}, {"id": 46, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Design user onboarding flow", "target_id": 203, "created_at": "2023-05-11T09:45:00Z"}, {"id": 47, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Create app wireframes", "target_id": 204, "created_at": "2023-05-20T16:30:00Z"}, {"id": 48, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Create app wireframes", "target_id": 204, "created_at": "2023-05-21T10:15:00Z"}, {"id": 49, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Set up development environment", "target_id": 205, "created_at": "2023-05-15T14:30:00Z"}, {"id": 50, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Set up development environment", "target_id": 205, "created_at": "2023-05-16T09:45:00Z"}, {"id": 51, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Create logo concepts", "target_id": 206, "created_at": "2023-05-25T15:30:00Z"}, {"id": 52, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Create logo concepts", "target_id": 206, "created_at": "2023-05-26T10:15:00Z"}, {"id": 53, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Implement authentication", "target_id": 207, "created_at": "2023-05-30T16:30:00Z"}, {"id": 54, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Implement authentication", "target_id": 207, "created_at": "2023-05-31T09:45:00Z"}, {"id": 55, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Create mockups", "target_id": 4, "created_at": "2023-06-10T15:30:00Z"}, {"id": 56, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Create mockups", "target_id": 4, "created_at": "2023-06-11T10:15:00Z"}, {"id": 57, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Set up development environment", "target_id": 5, "created_at": "2023-06-15T14:30:00Z"}, {"id": 58, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Set up development environment", "target_id": 5, "created_at": "2023-06-16T09:45:00Z"}, {"id": 59, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Implement user authentication", "target_id": 6, "created_at": "2023-06-25T16:30:00Z"}, {"id": 60, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Implement user authentication", "target_id": 6, "created_at": "2023-06-26T10:15:00Z"}, {"id": 61, "type": "project_created", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "New Feature Development", "target_id": 11, "created_at": "2023-08-28T09:30:00Z"}, {"id": 62, "type": "user_joined", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "New Feature Development", "target_id": 11, "created_at": "2023-08-28T10:15:00Z"}, {"id": 63, "type": "user_joined", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "New Feature Development", "target_id": 11, "created_at": "2023-08-28T10:30:00Z"}, {"id": 64, "type": "task_completed", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Feature specification", "target_id": 30, "created_at": "2023-08-29T14:30:00Z"}, {"id": 65, "type": "comment_added", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "Feature specification", "target_id": 30, "created_at": "2023-08-29T15:45:00Z"}, {"id": 66, "type": "task_completed", "user": {"id": 2, "name": "<PERSON>", "avatar": "/assets/profile-pictures/jane-smith.jpg"}, "target": "UI design for new feature", "target_id": 31, "created_at": "2023-08-30T16:30:00Z"}, {"id": 67, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "UI design for new feature", "target_id": 31, "created_at": "2023-08-30T17:15:00Z"}, {"id": 68, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Backend implementation", "target_id": 32, "created_at": "2023-08-31T15:30:00Z"}, {"id": 69, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Backend implementation", "target_id": 32, "created_at": "2023-08-31T16:45:00Z"}, {"id": 70, "type": "project_created", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Performance Optimization", "target_id": 12, "created_at": "2023-09-01T10:00:00Z"}, {"id": 71, "type": "user_joined", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Performance Optimization", "target_id": 12, "created_at": "2023-09-01T10:30:00Z"}, {"id": 72, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Performance audit", "target_id": 33, "created_at": "2023-09-02T14:30:00Z"}, {"id": 73, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Performance audit", "target_id": 33, "created_at": "2023-09-02T15:45:00Z"}, {"id": 74, "type": "task_completed", "user": {"id": 3, "name": "<PERSON>", "avatar": "/assets/profile-pictures/mike-johnson.jpg"}, "target": "Database optimization", "target_id": 34, "created_at": "2023-09-03T16:30:00Z"}, {"id": 75, "type": "comment_added", "user": {"id": 1, "name": "<PERSON>", "avatar": "/assets/profile-pictures/john-doe.jpg"}, "target": "Database optimization", "target_id": 34, "created_at": "2023-09-03T17:15:00Z"}]