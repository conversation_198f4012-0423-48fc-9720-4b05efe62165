import React, { useEffect } from "react";
import { motion } from "framer-motion";

const Loader: React.FC = () => {
    // Disable scrolling while the loader is active
    useEffect(() => {
        document.body.style.overflow = "hidden";
        return () => {
            document.body.style.overflow = "";
        };
    }, []);

    return (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-gray-900 bg-opacity-50 backdrop-blur-sm">
            <motion.div
                className="flex space-x-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
            >
                {[...Array(3)].map((_, index) => (
                    <motion.div
                        key={index}
                        className="w-5 h-5 md:w-6 md:h-6 bg-blue-500 rounded-full"
                        animate={{
                            y: [0, -20, 0],
                            opacity: [0.7, 1, 0.7],
                        }}
                        transition={{
                            duration: 0.6,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: index * 0.2,
                        }}
                    />
                ))}
            </motion.div>
        </div>
    );
};

export default Loader;
