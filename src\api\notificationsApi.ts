import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';
import { User } from '@/entities/User';

// Notification types
export type NotificationType =
  | 'task_assigned'
  | 'task_due_soon'
  | 'task_completed'
  | 'comment_added'
  | 'project_invitation'
  | 'project_update'
  | 'mention'
  | 'role_updated'
  | 'removed_from_project'
  | 'system';

export interface Notification {
  id: number;
  user_id: number;
  type: NotificationType;
  message: string;
  data: any;
  related_user_id: number | null;
  notifiable_type: string;
  notifiable_id: number;
  read: boolean;
  created_at: string;
  updated_at: string;
  relatedUser?: User;
}

// Get all notifications
export const getNotifications = async (read?: boolean): Promise<Notification[]> => {
  // Check if user is authenticated by checking for token
  const token = localStorage.getItem('token');
  if (!token) {
    return [];
  }

  try {
    const params: any = {};
    if (read !== undefined) {
      params.read = read;
    }

    const response = await api.get('/notifications', { params });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch notifications",
      "Could not load notifications"
    );
  }
};

// Get unread notification count
export const getUnreadCount = async (): Promise<number> => {
  // Check if user is authenticated by checking for token
  const token = localStorage.getItem('token');
  if (!token) {
    return 0;
  }

  try {
    const response = await api.get('/notifications/unread-count');
    return response.data.count;
  } catch (error) {
    console.error('Error fetching unread notification count:', error);
    return 0;
  }
};

// Mark a notification as read
export const markAsRead = async (id: number): Promise<Notification> => {
  // Check if user is authenticated by checking for token
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('User not authenticated');
  }

  try {
    const response = await api.post(`/notifications/${id}/read`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to mark notification as read",
      "Could not update notification"
    );
  }
};

// Mark a notification as unread
export const markAsUnread = async (id: number): Promise<Notification> => {
  // Check if user is authenticated by checking for token
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('User not authenticated');
  }

  try {
    const response = await api.post(`/notifications/${id}/unread`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to mark notification as unread",
      "Could not update notification"
    );
  }
};

// Mark all notifications as read
export const markAllAsRead = async (): Promise<void> => {
  // Check if user is authenticated by checking for token
  const token = localStorage.getItem('token');
  if (!token) {
    return;
  }

  try {
    await api.post('/notifications/read-all');
  } catch (error) {
    handleApiError(
      error,
      "Failed to mark all notifications as read",
      "Could not update notifications"
    );
  }
};

// Delete a notification
export const deleteNotification = async (id: number): Promise<void> => {
  // Check if user is authenticated by checking for token
  const token = localStorage.getItem('token');
  if (!token) {
    return;
  }

  try {
    await api.delete(`/notifications/${id}`);
  } catch (error) {
    handleApiError(
      error,
      "Failed to delete notification",
      "Could not delete notification"
    );
  }
};
