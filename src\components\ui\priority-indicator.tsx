import React from 'react';
import { ArrowUp, ArrowRight, ArrowDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

export type PriorityType = 'high' | 'medium' | 'low' | string;

export interface PriorityIndicatorProps {
  priority: PriorityType;
  showLabel?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
  variant?: 'default' | 'pill';
}

export const PriorityIndicator: React.FC<PriorityIndicatorProps> = ({
  priority,
  showLabel = false,
  size = 'md',
  className,
  variant = 'default',
}) => {
  const { t } = useTranslation();

  // Map priority values to standardized keys
  const normalizedPriority = (() => {
    if (!priority) return 'medium';

    switch(priority.toLowerCase()) {
      case 'high':
      case 'urgent':
      case 'critical':
        return 'high';
      case 'medium':
      case 'normal':
        return 'medium';
      case 'low':
      case 'minor':
        return 'low';
      default:
        return 'medium';
    }
  })();

  const priorityConfig = {
    high: {
      label: t('priority.high', 'High'),
      icon: ArrowUp,
      color: 'text-priority-high-foreground',
      bg: 'bg-priority-high',
    },
    medium: {
      label: t('priority.medium', 'Medium'),
      icon: ArrowRight,
      color: 'text-priority-medium-foreground',
      bg: 'bg-priority-medium',
    },
    low: {
      label: t('priority.low', 'Low'),
      icon: ArrowDown,
      color: 'text-priority-low-foreground',
      bg: 'bg-priority-low',
    },
  };

  const config = priorityConfig[normalizedPriority] || priorityConfig.medium;
  const Icon = config.icon;

  const iconSizes = {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
  };

  const sizeClasses = {
    xs: 'text-[10px] py-0 px-1',
    sm: 'text-xs py-0.5 px-1.5',
    md: 'text-xs py-1 px-2',
    lg: 'text-sm py-1 px-2.5',
  };

  if (variant === 'pill' && showLabel) {
    return (
      <div className={cn(
        'flex items-center gap-1 rounded-full font-medium',
        sizeClasses[size],
        config.bg,
        config.color,
        className
      )}>
        <Icon size={iconSizes[size]} />
        <span>{config.label}</span>
      </div>
    );
  }

  if (showLabel) {
    return (
      <div className={cn(
        'flex items-center gap-1 px-2 py-0.5 rounded',
        config.bg,
        config.color,
        className
      )}>
        <Icon size={iconSizes[size]} />
        <span className="text-xs font-medium">{config.label}</span>
      </div>
    );
  }

  return (
    <div className={cn(
      'flex items-center justify-center rounded-full p-1',
      config.bg,
      config.color,
      className
    )}>
      <Icon size={iconSizes[size]} />
    </div>
  );
};

export default PriorityIndicator;
