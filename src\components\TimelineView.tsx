import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Task } from '@/api/tasksApi';
import { Project } from '@/entities/Project';
import {
  ChevronLeft, ChevronRight, Calendar, Clock,
  ArrowUp, ArrowRight, ArrowDown, CheckCircle,
  AlertTriangle, CircleDashed, X
} from 'lucide-react';
import { DateHelper } from '@/utils/dateUtils';
import { cn } from '@/lib/utils';
import { StatusBadge } from './ui/status-badge';
import { PriorityIndicator } from './ui/priority-indicator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  format,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  addWeeks,
  subWeeks,
  startOfMonth,
  endOfMonth,
  addMonths,
  subMonths,
  startOfQuarter,
  endOfQuarter,
  addQuarters,
  subQuarters,
  isSameDay,
  isWithinInterval
} from 'date-fns';

interface TimelineViewProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | undefined;
  tasks: Task[];
}

const TimelineView: React.FC<TimelineViewProps> = ({
  isOpen,
  onClose,
  project,
  tasks
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'week' | 'month' | 'quarter'>('month');

  if (!project) return null;

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Date navigation functions
  const navigatePrev = () => {
    switch (viewMode) {
      case 'week':
        setCurrentDate(subWeeks(currentDate, 1));
        break;
      case 'month':
        setCurrentDate(subMonths(currentDate, 1));
        break;
      case 'quarter':
        setCurrentDate(subQuarters(currentDate, 1));
        break;
    }
  };

  const navigateNext = () => {
    switch (viewMode) {
      case 'week':
        setCurrentDate(addWeeks(currentDate, 1));
        break;
      case 'month':
        setCurrentDate(addMonths(currentDate, 1));
        break;
      case 'quarter':
        setCurrentDate(addQuarters(currentDate, 1));
        break;
    }
  };

  const navigateToday = () => {
    setCurrentDate(new Date());
  };

  // Calculate date ranges based on view mode
  const dateRange = useMemo(() => {
    switch (viewMode) {
      case 'week':
        return {
          start: startOfWeek(currentDate, { weekStartsOn: 0 }),
          end: endOfWeek(currentDate, { weekStartsOn: 0 })
        };
      case 'month':
        return {
          start: startOfMonth(currentDate),
          end: endOfMonth(currentDate)
        };
      case 'quarter':
        return {
          start: startOfQuarter(currentDate),
          end: endOfQuarter(currentDate)
        };
    }
  }, [currentDate, viewMode]);

  // Format the date range for display
  const dateRangeDisplay = useMemo(() => {
    switch (viewMode) {
      case 'week':
        return `${format(dateRange.start, 'MMM d')} - ${format(dateRange.end, 'MMM d, yyyy')}`;
      case 'month':
        return format(currentDate, 'MMMM yyyy');
      case 'quarter':
        const quarterNumber = Math.floor(dateRange.start.getMonth() / 3) + 1;
        return `Q${quarterNumber} ${dateRange.start.getFullYear()}`;
    }
  }, [dateRange, viewMode, currentDate]);

  // Filter tasks based on the current date range
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (!task.end_date) return false;
      const taskDate = new Date(task.end_date);
      return isWithinInterval(taskDate, {
        start: dateRange.start,
        end: dateRange.end
      });
    });
  }, [tasks, dateRange]);

  // Group tasks by day for the month view
  const tasksByDay = useMemo(() => {
    const result: Record<number, Task[]> = {};

    if (viewMode === 'month') {
      filteredTasks.forEach(task => {
        if (!task.end_date) return;
        const day = new Date(task.end_date).getDate();
        if (!result[day]) {
          result[day] = [];
        }
        result[day].push(task);
      });
    }

    return result;
  }, [filteredTasks, viewMode]);

  // Group tasks by day for the week view
  const tasksByWeekDay = useMemo(() => {
    const result: Record<string, Task[]> = {};

    if (viewMode === 'week') {
      const days = eachDayOfInterval({ start: dateRange.start, end: dateRange.end });

      // Initialize empty arrays for each day
      days.forEach(day => {
        result[format(day, 'yyyy-MM-dd')] = [];
      });

      // Add tasks to their respective days
      filteredTasks.forEach(task => {
        if (!task.end_date) return;
        const taskDate = new Date(task.end_date);
        const dateKey = format(taskDate, 'yyyy-MM-dd');

        if (result[dateKey]) {
          result[dateKey].push(task);
        }
      });
    }

    return result;
  }, [filteredTasks, dateRange, viewMode]);

  // Group tasks by month for the quarter view
  const tasksByMonth = useMemo(() => {
    const result: Record<number, Task[]> = {};

    if (viewMode === 'quarter') {
      // Initialize arrays for the three months in the quarter
      for (let i = 0; i < 3; i++) {
        result[dateRange.start.getMonth() + i] = [];
      }

      // Add tasks to their respective months
      filteredTasks.forEach(task => {
        if (!task.end_date) return;
        const taskDate = new Date(task.end_date);
        const month = taskDate.getMonth();

        if (result[month] !== undefined) {
          result[month].push(task);
        }
      });
    }

    return result;
  }, [filteredTasks, dateRange, viewMode]);

  // Sort tasks by priority
  const sortedTasks = useMemo(() => {
    return [...filteredTasks].sort((a, b) => {
      const priorityOrder: Record<string, number> = {
        'high': 0,
        'medium': 1,
        'low': 2
      };

      const priorityA = a.priority ? priorityOrder[a.priority] : 3;
      const priorityB = b.priority ? priorityOrder[b.priority] : 3;

      return priorityA - priorityB;
    });
  }, [filteredTasks]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-hidden flex flex-col" closeButton={false}>
        <DialogHeader className="pb-2 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl">Project Timeline</DialogTitle>
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
              <X size={16} />
            </Button>
          </div>
        </DialogHeader>

        <Tabs defaultValue="month" className="flex-1 overflow-hidden flex flex-col" onValueChange={(value) => setViewMode(value as 'week' | 'month' | 'quarter')}>
          <div className="flex justify-between items-center py-3">
            <TabsList>
              <TabsTrigger value="week" className="text-sm">Week</TabsTrigger>
              <TabsTrigger value="month" className="text-sm">Month</TabsTrigger>
              <TabsTrigger value="quarter" className="text-sm">Quarter</TabsTrigger>
              <TabsTrigger value="list" className="text-sm">List</TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={navigatePrev} className="h-8 w-8 p-0">
                <ChevronLeft size={16} />
              </Button>
              <h2 className="text-sm font-medium px-2 min-w-[160px] text-center">
                {dateRangeDisplay}
              </h2>
              <Button variant="outline" size="sm" onClick={navigateNext} className="h-8 w-8 p-0">
                <ChevronRight size={16} />
              </Button>
              <Button variant="outline" size="sm" onClick={navigateToday} className="h-7 text-xs">
                Today
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-auto">
            {/* Week View */}
            <TabsContent value="week" className="mt-0 h-full">
              <div className="p-2 space-y-2">
                <div className="grid grid-cols-7 gap-1">
                  {/* Week day headers */}
                  {eachDayOfInterval({ start: dateRange.start, end: dateRange.end }).map((day) => (
                    <div
                      key={format(day, 'yyyy-MM-dd')}
                      className={cn(
                        "text-center p-2 border-b border-border",
                        isSameDay(day, new Date()) ? "bg-primary/10 font-medium" : ""
                      )}
                    >
                      <div className="text-xs text-muted-foreground">{format(day, 'EEE')}</div>
                      <div className={cn(
                        "text-sm mt-1",
                        isSameDay(day, new Date()) ? "text-primary font-medium" : ""
                      )}>
                        {format(day, 'd')}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Week tasks */}
                <div className="grid grid-cols-7 gap-1">
                  {eachDayOfInterval({ start: dateRange.start, end: dateRange.end }).map((day) => {
                    const dateKey = format(day, 'yyyy-MM-dd');
                    const dayTasks = tasksByWeekDay[dateKey] || [];
                    const isToday = isSameDay(day, new Date());

                    return (
                      <div
                        key={`tasks-${dateKey}`}
                        className={cn(
                          "min-h-[200px] border border-border rounded-md p-2",
                          isToday ? "bg-primary/5 border-primary/30" : ""
                        )}
                      >
                        {dayTasks.length > 0 ? (
                          <div className="space-y-2">
                            {dayTasks.map(task => (
                              <div
                                key={task.id}
                                className={cn(
                                  "text-xs p-2 rounded border-l-2",
                                  task.priority === 'high' ? "border-priority-high bg-priority-high/5" :
                                  task.priority === 'medium' ? "border-priority-medium bg-priority-medium/5" :
                                  "border-priority-low bg-priority-low/5"
                                )}
                              >
                                <div className="font-medium mb-1 truncate">{task.title}</div>
                                {task.description && (
                                  <div className="text-muted-foreground line-clamp-2 text-[10px]">
                                    {task.description}
                                  </div>
                                )}
                                <div className="mt-1 flex items-center justify-between">
                                  <StatusBadge status={task.status || 'todo'} size="xs" />
                                  {task.assignees && task.assignees.length > 0 && (
                                    <span className="text-[10px] text-muted-foreground">
                                      {task.assignees[0].first_name}
                                      {task.assignees.length > 1 && ` +${task.assignees.length - 1}`}
                                    </span>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="h-full flex items-center justify-center text-[10px] text-muted-foreground">
                            No tasks
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>

            {/* Month View */}
            <TabsContent value="month" className="mt-0 h-full">
              <div className="grid grid-cols-7 gap-1 p-2">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                  <div key={day} className="text-center text-xs font-medium text-muted-foreground p-1">
                    {day}
                  </div>
                ))}

                {/* Generate calendar days */}
                {Array.from({ length: new Date(dateRange.end.getFullYear(), dateRange.end.getMonth() + 1, 0).getDate() + new Date(dateRange.start.getFullYear(), dateRange.start.getMonth(), 1).getDay() }, (_, i) => {
                  const dayIndex = i - new Date(dateRange.start.getFullYear(), dateRange.start.getMonth(), 1).getDay() + 1;
                  const isCurrentMonth = dayIndex > 0 && dayIndex <= new Date(dateRange.end.getFullYear(), dateRange.end.getMonth() + 1, 0).getDate();

                  if (!isCurrentMonth) {
                    return <div key={`empty-${i}`} className="h-24 p-1"></div>;
                  }

                  const date = new Date(dateRange.start.getFullYear(), dateRange.start.getMonth(), dayIndex);
                  const isToday = isSameDay(new Date(), date);
                  const tasksForDay = tasksByDay[dayIndex] || [];

                  return (
                    <div
                      key={`day-${dayIndex}`}
                      className={cn(
                        "h-24 border border-border rounded-md p-1 overflow-hidden",
                        isToday ? "bg-primary/5 border-primary/30" : ""
                      )}
                    >
                      <div className={cn(
                        "text-xs font-medium mb-1 p-0.5 rounded-sm",
                        isToday ? "bg-primary text-white text-center" : "text-muted-foreground"
                      )}>
                        {dayIndex}
                      </div>
                      <div className="space-y-1 overflow-y-auto max-h-[calc(100%-20px)]">
                        {tasksForDay.map(task => (
                          <div
                            key={task.id}
                            className={cn(
                              "text-xs p-1 rounded truncate border-l-2",
                              task.priority === 'high' ? "border-priority-high bg-priority-high/5" :
                              task.priority === 'medium' ? "border-priority-medium bg-priority-medium/5" :
                              "border-priority-low bg-priority-low/5"
                            )}
                          >
                            {task.title}
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            {/* Quarter View */}
            <TabsContent value="quarter" className="mt-0 h-full">
              <div className="p-4 space-y-6">
                {/* Quarter header */}
                <div className="text-center mb-4">
                  <h3 className="text-lg font-medium">
                    Q{Math.floor(dateRange.start.getMonth() / 3) + 1} {dateRange.start.getFullYear()}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {format(dateRange.start, 'MMMM')} - {format(dateRange.end, 'MMMM yyyy')}
                  </p>
                </div>

                {/* Months in quarter */}
                <div className="grid grid-cols-3 gap-4">
                  {[0, 1, 2].map(monthOffset => {
                    const monthDate = new Date(dateRange.start);
                    monthDate.setMonth(dateRange.start.getMonth() + monthOffset);
                    const monthIndex = monthDate.getMonth();
                    const monthName = monthNames[monthIndex];
                    const monthTasks = tasksByMonth[monthIndex] || [];

                    return (
                      <div key={`month-${monthIndex}`} className="border border-border rounded-lg overflow-hidden">
                        <div className="bg-muted/30 p-2 text-center border-b border-border">
                          <h4 className="font-medium">{monthName}</h4>
                        </div>
                        <div className="p-2">
                          {monthTasks.length > 0 ? (
                            <div className="space-y-2 max-h-[300px] overflow-y-auto">
                              {monthTasks.map(task => (
                                <div
                                  key={task.id}
                                  className={cn(
                                    "p-2 rounded-md border-l-2",
                                    task.priority === 'high' ? "border-priority-high bg-priority-high/5" :
                                    task.priority === 'medium' ? "border-priority-medium bg-priority-medium/5" :
                                    "border-priority-low bg-priority-low/5"
                                  )}
                                >
                                  <div className="flex items-center justify-between mb-1">
                                    <span className="font-medium text-sm">{task.title}</span>
                                    {task.end_date && (
                                      <span className="text-xs bg-muted/50 px-1.5 py-0.5 rounded">
                                        {format(new Date(task.end_date), 'MMM d')}
                                      </span>
                                    )}
                                  </div>
                                  {task.description && (
                                    <p className="text-xs text-muted-foreground line-clamp-2 mb-1">
                                      {task.description}
                                    </p>
                                  )}
                                  <div className="flex items-center justify-between mt-1">
                                    <StatusBadge status={task.status || 'todo'} size="xs" />
                                    <PriorityIndicator priority={task.priority || 'medium'} size="xs" />
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="py-8 text-center text-muted-foreground">
                              <p className="text-sm">No tasks</p>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>

            {/* List View */}
            <TabsContent value="list" className="mt-0 h-full">
              <div className="space-y-1 p-2">
                {sortedTasks.length > 0 ? (
                  sortedTasks.map(task => (
                    <div
                      key={task.id}
                      className="p-3 border border-border rounded-md hover:border-primary/30 hover:bg-accent/5 transition-colors"
                    >
                      <div className="flex justify-between items-start gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <StatusBadge status={task.status || 'todo'} size="sm" />
                            <h4 className="font-medium">{task.title}</h4>
                          </div>
                          {task.description && (
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {task.description}
                            </p>
                          )}
                          <div className="flex items-center gap-3 mt-2">
                            <PriorityIndicator priority={task.priority || 'medium'} showLabel size="sm" />

                            {task.assignees && task.assignees.length > 0 && (
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <span>Assigned to:</span>
                                <span className="font-medium">
                                  {task.assignees.map(a => a.first_name).join(', ')}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                        {task.end_date && (
                          <div className="flex items-center text-xs bg-muted/30 px-2 py-1 rounded">
                            <Clock size={12} className="mr-1 text-muted-foreground" />
                            <span className={cn(
                              "font-medium",
                              new Date(task.end_date) < new Date() ? "text-destructive" : ""
                            )}>
                              {DateHelper.formatDate(task.end_date)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12 text-muted-foreground bg-muted/10 rounded-md">
                    <Calendar className="h-12 w-12 mx-auto mb-3 text-muted-foreground/50" />
                    <p className="text-sm">No tasks found for this period.</p>
                    <p className="text-xs text-muted-foreground mt-1">Try selecting a different time period or adding new tasks.</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default TimelineView;
