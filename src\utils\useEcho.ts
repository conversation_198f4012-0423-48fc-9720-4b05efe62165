import { useEffect, useState } from 'react';
import echo from './echo';
import { useAuth } from '@/context/AuthContext';

/**
 * Hook to use Laravel Echo with authentication
 * Returns the Echo instance when the user is authenticated
 */
export function useEcho() {
  const { user, token } = useAuth();
  const [echoInstance, setEchoInstance] = useState<any>(null);

  useEffect(() => {
    // Only set up Echo if we have a user and token
    if (user && token) {
      try {
        // Check if echo is properly initialized
        if (echo && echo.connector) {
          // Set the auth header for Echo
          echo.connector.options.auth = {
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: 'application/json',
              'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
              'X-Requested-With': 'XMLHttpRequest',
              'Content-Type': 'application/json',
            },
          };

          // Set the CSRF token
          echo.connector.options.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

          // Set the auth endpoint
          echo.connector.options.authEndpoint = `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/broadcasting/auth`;

          setEchoInstance(echo);

          return () => {
            // Clean up any listeners when the component unmounts
            if (echo.connector && echo.connector.pusher) {
              echo.connector.pusher.disconnect();
            }
          };
        } else {
          console.warn('Echo instance not properly initialized');
          setEchoInstance(null);
        }
      } catch (error) {
        console.error('Error initializing Echo:', error);
        setEchoInstance(null);
      }
    } else {
      setEchoInstance(null);
    }
  }, [user, token]);

  return echoInstance;
}
