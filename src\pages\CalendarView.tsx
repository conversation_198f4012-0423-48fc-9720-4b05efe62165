import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import Navbar from '@/components/Navbar';
import { getProject } from '@/api/projectsApi';
import { getTasks } from '@/api/tasksApi';
import { List, Calendar, BarChart, Kanban, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import BackButton from '@/components/BackButton';

const CalendarViewPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [currentDate, setCurrentDate] = useState(new Date());
  const { user } = useAuth();

  // Fetch project
  const { data: project, isLoading: projectLoading } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => getProject(Number(projectId)),
    enabled: !!projectId
  });

  // Fetch tasks
  const { data: tasks = [], isLoading: tasksLoading } = useQuery({
    queryKey: ['tasks', projectId],
    queryFn: () => getTasks(Number(projectId)),
    enabled: !!projectId
  });

  const handleViewChange = (view: string) => {
    navigate(`/projects/${projectId}/${view}`);
  };

  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const daysInMonth = getDaysInMonth(currentYear, currentMonth);
  const firstDayOfMonth = getFirstDayOfMonth(currentYear, currentMonth);

  // Adjust for Sunday as first day (0) to Monday as first day (1)
  const adjustedFirstDay = firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1;

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  const prevMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
  };

  const nextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
  };

  // Get tasks with due dates in the current month
  const tasksInMonth = tasks.filter(task => {
    if (!task.end_date) return false;
    const endDate = new Date(task.end_date);
    return endDate.getMonth() === currentMonth && endDate.getFullYear() === currentYear;
  });

  // Create a map of day -> tasks for quick lookup
  const tasksByDay: Record<number, typeof tasks> = {};
  tasksInMonth.forEach(task => {
    if (!task.end_date) return;
    const day = new Date(task.end_date).getDate();
    if (!tasksByDay[day]) {
      tasksByDay[day] = [];
    }
    tasksByDay[day].push(task);
  });

  // Generate calendar days
  const calendarDays = [];

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < adjustedFirstDay; i++) {
    calendarDays.push(<div key={`empty-${i}`} className="h-24 border border-border bg-muted/20"></div>);
  }

  // Add cells for each day of the month
  for (let day = 1; day <= daysInMonth; day++) {
    const hasTasksForDay = tasksByDay[day] && tasksByDay[day].length > 0;

    calendarDays.push(
      <div
        key={`day-${day}`}
        className={`h-24 border border-border p-1 relative ${hasTasksForDay ? 'bg-primary/5' : ''}`}
      >
        <div className="text-sm font-medium mb-1">{day}</div>
        {hasTasksForDay && (
          <div className="overflow-y-auto max-h-[calc(100%-20px)]">
            {tasksByDay[day].map((task, index) => (
              <div
                key={`task-${day}-${index}`}
                className="text-xs p-1 mb-1 rounded bg-primary/10 truncate"
                title={task.title}
              >
                {task.title}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  if (projectLoading || tasksLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">Loading project data...</p>
          </div>
        </main>
      </div>
    );
  }

  // Check if user has permission to view the project
  const canViewProject = hasProjectPermission(project, user, ProjectPermission.VIEW_PROJECT);

  if (!canViewProject) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">You don't have permission to view this project.</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate('/dashboard')}
              >
                Go to Dashboard
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="max-w-7xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <BackButton />
                <h1 className="text-3xl font-bold text-foreground">
                  {project ? project.name : 'Project Calendar'}
                </h1>
              </div>
              <p className="text-muted-foreground mt-1">
                {project?.description || 'Manage your tasks and track progress'}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('board')}
              >
                <Kanban size={16} className="mr-1.5" />
                Kanban
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('list')}
              >
                <List size={16} className="mr-1.5" />
                List
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center"
              >
                <Calendar size={16} className="mr-1.5" />
                Calendar
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('reports')}
              >
                <BarChart size={16} className="mr-1.5" />
                Reports
              </Button>
            </div>
          </div>
        </div>

        {/* Calendar View */}
        <div className="bg-card border border-border rounded-lg shadow-sm overflow-hidden p-4">
          <div className="flex justify-between items-center mb-4">
            <Button variant="outline" size="sm" onClick={prevMonth}>
              <ChevronLeft size={16} />
            </Button>
            <h2 className="text-lg font-medium">
              {monthNames[currentMonth]} {currentYear}
            </h2>
            <Button variant="outline" size="sm" onClick={nextMonth}>
              <ChevronRight size={16} />
            </Button>
          </div>

          {/* Calendar grid */}
          <div className="grid grid-cols-7 gap-1">
            {/* Day names */}
            {dayNames.map(day => (
              <div key={day} className="text-center font-medium text-sm py-2 bg-muted/30">
                {day}
              </div>
            ))}

            {/* Calendar days */}
            {calendarDays}
          </div>

          {/* Tasks with deadlines this month */}
          {tasksInMonth.length > 0 && (
            <div className="mt-6">
              <h3 className="text-md font-medium mb-2">Tasks Due This Month</h3>
              <div className="space-y-2">
                {tasksInMonth.map(task => (
                  <div
                    key={task.id}
                    className="p-2 border border-border rounded-lg flex justify-between items-center"
                  >
                    <div>
                      <h4 className="font-medium">{task.title}</h4>
                      <p className="text-xs text-muted-foreground">{task.description}</p>
                    </div>
                    <div className="text-sm font-medium text-primary">
                      {task.end_date && new Date(task.end_date).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default CalendarViewPage;
