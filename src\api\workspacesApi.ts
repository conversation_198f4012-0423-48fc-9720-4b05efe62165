
import api from './api';
import { toast } from "@/hooks/use-toast";
import { normalizeWorkspace, Workspace } from '@/entities/Workspace';
import { normalizeProject, Project } from '@/entities/Project';
import { normalizeRole } from '@/entities/Role';
import { normalizeUser, User } from '@/entities/User';

// Workspace types


// Get all workspaces
export const getWorkspaces = async () => {
  try {
    const response = await api.get('/workspaces');
    return response.data;
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    toast({
      title: "Failed to fetch workspaces",
      description: "Could not load workspaces",
      variant: "destructive"
    });
    throw error;
  }
};

// Get a specific workspace
export const getWorkspace = async (id: number) => {
  try {
    console.log(`Fetching workspace with ID: ${id}`);
    const response = await api.get(`/workspace/${id}`);
    console.log('Workspace data received:', response.data);

    const normalizeProjects = (data: any[]): Project[] => {
      return data.map(normalizeProject);
    };

    const normalizeMembers = (data: any[]): User[] => {
      return data.map(normalizeUser);
    };

    return {
      workspace: normalizeWorkspace(response.data.workspace),
      _role: response.data.role ? normalizeRole(response.data.role) : null,
      statistics: response.data.statistics,
      _projects: normalizeProjects(response.data.projects || []),
      _deadLines: normalizeProjects(response.data.upcoming_deadlines || []),
      _members: normalizeMembers(response.data.members || [])
    };
  } catch (error) {
    console.error(`Error fetching workspace ${id}:`, error);

    // Check if it's a 404 error
    if (error.response && error.response.status === 404) {
      console.error('Workspace not found or no access rights');
      toast({
        title: "Workspace not found",
        description: "The workspace you're trying to access doesn't exist or is not owned by you. Workspaces are personal and only visible to their owners.",
        variant: "destructive"
      });
    } else {
      toast({
        title: "Error loading workspace",
        description: "An unexpected error occurred. Please try again later.",
        variant: "destructive"
      });
    }

    throw error;
  }
};




// Create a new workspace
export const createWorkspace = async (workspaceData: Partial<Workspace>) => {
  try {
    const response = await api.post('/workspaces', workspaceData);
    return response.data;
  } catch (error) {
    console.error('Error creating workspace:', error);
    toast({
      title: "Failed to create workspace",
      description: "Could not create the new workspace",
      variant: "destructive"
    });
    throw error;
  }
};

// Update a workspace
export const updateWorkspace = async (id: number, workspaceData: Partial<Workspace>) => {
  try {
    const response = await api.put(`/workspaces/${id}`, workspaceData);
    return response.data;
  } catch (error) {
    console.error('Error updating workspace:', error);
    toast({
      title: "Failed to update workspace",
      description: "Could not update the workspace",
      variant: "destructive"
    });
    throw error;
  }
};

// Delete a workspace
export const deleteWorkspace = async (id: number) => {
  try {
    const response = await api.delete(`/workspaces/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting workspace:', error);
    toast({
      title: "Failed to delete workspace",
      description: "Could not delete the workspace",
      variant: "destructive"
    });
    throw error;
  }
};

// Add a user to a workspace
export const addWorkspaceUser = async (workspaceId: number, userId: number, roleId: number) => {
  try {
    const response = await api.post(`/workspaces/${workspaceId}/users`, {
      user_id: userId,
      role_id: roleId
    });
    return response.data;
  } catch (error) {
    console.error('Error adding user to workspace:', error);
    toast({
      title: "Operation not allowed",
      description: "Workspaces are personal and cannot have additional members.",
      variant: "destructive"
    });
    throw error;
  }
};

// Remove a user from a workspace
export const removeWorkspaceUser = async (workspaceId: number, userId: number) => {
  try {
    const response = await api.delete(`/workspaces/${workspaceId}/users`, {
      data: { user_id: userId }
    });
    return response.data;
  } catch (error) {
    console.error('Error removing user from workspace:', error);
    toast({
      title: "Operation not allowed",
      description: "Workspaces are personal and cannot have additional members.",
      variant: "destructive"
    });
    throw error;
  }
};
