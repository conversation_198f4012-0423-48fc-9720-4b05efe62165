
export class DateHelper {
    /**
     * Formats date to English locale string
     * @param date Date object, string, timestamp, or null/undefined
     * @param options Intl.DateTimeFormatOptions
     * @returns Formatted English date string
     */
    static formatDate(
        date: Date | string | number | null | undefined,
        options: Intl.DateTimeFormatOptions = {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        }
    ): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', options);
    }

    /**
     * Gets day number with English month name (e.g., "5 April")
     */
    static getMonthNameWithDay(date: Date | string | number | null | undefined): string {
        const parsedDate = this.parseDate(date);
        const day = parsedDate.getDate();
        const monthName = parsedDate.toLocaleDateString('en-US', { month: 'long' });
        return `${day} ${monthName}`;
    }

    /**
     * Formats date to English short date (MM/dd/yyyy)
     */
    static formatShortDate(date: Date | string | number | null | undefined): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    /**
     * Formats date to English long date (MMMM dd, yyyy, EEEE)
     */
    static formatLongDate(date: Date | string | number | null | undefined): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            weekday: 'long'
        });
    }

    /**
     * Formats date to English datetime (MM/dd/yyyy, HH:mm)
     */
    static formatDateTime(date: Date | string | number | null | undefined): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Formats date to English time (HH:mm:ss)
     */
    static formatTime(date: Date | string | number | null | undefined, showSeconds = false): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: showSeconds ? '2-digit' : undefined
        });
    }

    /**
     * Converts any date input to Date object
     */
    private static parseDate(date: Date | string | number | null | undefined): Date {
        // Handle null or undefined values
        if (date === null || date === undefined) {
            return new Date(); // Return current date as fallback
        }

        if (date instanceof Date) return date;
        if (typeof date === 'number') return new Date(date);
        if (typeof date === 'string') {
            // Skip empty strings
            if (date.trim() === '') {
                return new Date();
            }

            // Handle ISO strings and locale strings
            const parsed = new Date(date);
            if (!isNaN(parsed.getTime())) return parsed;

            // Try parsing MM/dd/yyyy format
            const parts = date.split('/');
            if (parts.length === 3) {
                return new Date(Number(parts[2]), Number(parts[0]) - 1, Number(parts[1]));
            }
        }
        throw new Error('Invalid date format');
    }

    /**
     * Gets English day name
     */
    static getDayName(date: Date | string | number | null | undefined): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', { weekday: 'long' });
    }

    /**
     * Gets English month name
     */
    static getMonthName(date: Date | string | number | null | undefined): string {
        const parsedDate = this.parseDate(date);
        return parsedDate.toLocaleDateString('en-US', { month: 'long' });
    }

    /**
     * Gets relative time string (e.g., "3 days ago")
     */
    static getRelativeTime(date: Date | string | number | null | undefined): string {
        const parsedDate = this.parseDate(date);
        const now = new Date();
        const diffInSeconds = Math.floor((now.getTime() - parsedDate.getTime()) / 1000);

        const intervals = {
            year: 31536000,
            month: 2592000,
            week: 604800,
            day: 86400,
            hour: 3600,
            minute: 60
        };

        for (const [unit, seconds] of Object.entries(intervals)) {
            const interval = Math.floor(diffInSeconds / seconds);
            if (interval >= 1) {
                return `${interval} ${interval === 1 ? unit : unit + 's'} ago`;
            }
        }

        return 'just now';
    }
}

// For backward compatibility
// This alias is deprecated and kept only for backward compatibility
// Use DateHelper directly instead
export const TurkishDateHelper = DateHelper;