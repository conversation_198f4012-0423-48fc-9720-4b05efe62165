import React, {
  createContext,
  useContext,
  useState,
  use<PERSON><PERSON>back,
  use<PERSON>emo,
  ReactNode,
} from "react";

import { useToast } from "@/hooks/use-toast";
import { getWorkspaces, getWorkspace } from "@/api/workspacesApi";
import { Workspace } from "@/entities/Workspace";
import { Project } from "@/entities/Project";
import { Role } from "@/entities/Role";
import { User } from "@/entities/User";
import { setLocalStorageData, getLocalStorageData } from "@/utils/sessionLocalStorageUtil";
import { useAuth } from "@/context/AuthContext";

/**
 * Defines the shape of the Workspace context
 */
interface WorkspaceContextType {
  workspaces: Workspace[] | null;
  currentWorkspace: Workspace | null;
  workspaceStatistics: Record<string, any> | null;
  projects: Project[] | null;
  role: Role | null;
  members: User[];
  deadLines: Project[];
  isLoading: boolean;
  error: string | null;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  setDeadLines: React.Dispatch<React.SetStateAction<Project[]>>;
  setMembers: React.Dispatch<React.SetStateAction<User[]>>;
  setRole: React.Dispatch<React.SetStateAction<Role | null>>;
  setWorkspaces: React.Dispatch<React.SetStateAction<Workspace[] | null>>;
  setCurrentWorkspace: React.Dispatch<React.SetStateAction<Workspace | null>>;
  setWorkspaceStatistics: React.Dispatch<React.SetStateAction<Record<string, any> | null>>;
  setProjects: React.Dispatch<React.SetStateAction<Project[] | null>>;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  fetchWorkspaces: () => Promise<void>;
  fetchWorkspaceById: (id: string) => Promise<void>;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

/**
 * WorkspaceProvider wraps the app and provides workspace state to all children components.
 */
export const WorkspaceProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [workspaces, setWorkspaces] = useState<Workspace[] | null>(null);
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [workspaceStatistics, setWorkspaceStatistics] = useState<Record<string, any> | null>(null);
  const [projects, setProjects] = useState<Project[] | null>([]);
  const [role, setRole] = useState<Role | null>(null);
  const [members, setMembers] = useState<User[]>([]);
  const [deadLines, setDeadLines] = useState<Project[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  /**
   * Fetches all workspaces for the authenticated user
   */
  const fetchWorkspaces = useCallback(async () => {
    try {
      console.log('WorkspaceContext - Fetching workspaces');
      const data = await getWorkspaces();
      console.log(`WorkspaceContext - Fetched ${data.length} workspaces`);

      // Log workspace IDs for debugging
      if (data && data.length > 0) {
        const workspaceIds = data.map((w: Workspace) => w.id);
        console.log('WorkspaceContext - Available workspace IDs:', workspaceIds.join(', '));
      }

      setWorkspaces(data);

      // If we have workspaces but no current workspace is set, use the first one
      // Check both currentWorkspace and localStorage to avoid unnecessary updates
      const storedWorkspace = getLocalStorageData('workspace');
      const hasStoredWorkspace = storedWorkspace && storedWorkspace.id;

      if (data && data.length > 0 && !currentWorkspace && !hasStoredWorkspace) {
        console.log(`WorkspaceContext - No current workspace, setting to first available: ${data[0].id}`);
        setCurrentWorkspace(data[0]);

        // Save to localStorage with user-specific key if user is available
        if (user?.id) {
          const userWorkspaceKey = `workspace_${user.id}`;
          setLocalStorageData(userWorkspaceKey, data[0]);
        }

        // Also save to regular workspace key for backward compatibility
        setLocalStorageData('workspace', data[0]);
      }

      return data;
    } catch (error) {
      console.error('WorkspaceContext - Error fetching workspaces:', error);
      toast({
        title: "Failed to load workspaces",
        description: (error as Error).message || "An error occurred.",
        variant: "destructive",
      });
      return [];
    }
  }, [toast, currentWorkspace, user]);

  /**
   * Fetches a specific workspace by ID, including its stats, members, deadlines, and projects.
   */
  const fetchWorkspaceById = useCallback(async (id: string) => {
    try {
      // Reset error state before fetching
      setError(null);

      const {
        workspace,
        statistics,
        _projects,
        _role,
        _members,
        _deadLines,
      } = await getWorkspace(Number(id));

      // Save to localStorage with user-specific key if user is available
      if (user?.id) {
        const userWorkspaceKey = `workspace_${user.id}`;
        setLocalStorageData(userWorkspaceKey, workspace);
      }

      // Also save to regular workspace key for backward compatibility
      setLocalStorageData('workspace', workspace);

      // Log for debugging
      console.log('WorkspaceContext - Fetched workspace:', workspace.name);

      // Update all workspace-related state
      setCurrentWorkspace(workspace);
      setWorkspaceStatistics(statistics);
      setProjects(_projects);
      setRole(_role);
      setMembers(_members);
      setDeadLines(_deadLines);
    } catch (error) {
      // Set error state based on the error type
      if (error.response && error.response.status === 404) {
        setError("Workspace not found or you don't have access to it.");

        // Clear invalid workspace data from localStorage
        if (user?.id) {
          localStorage.removeItem(`workspace_${user.id}`);
        }
        localStorage.removeItem('workspace');
      } else {
        setError("Failed to load workspace data. Please try again.");
      }

      toast({
        title: "Failed to load workspace",
        description: (error as Error).message || "An error occurred.",
        variant: "destructive",
      });

      // Clear workspace data on error
      setCurrentWorkspace(null);
      setWorkspaceStatistics(null);
      setProjects([]);
      setRole(null);
      setMembers([]);
      setDeadLines([]);
    }
  }, [toast, setError, user]);

  /**
   * Memoized context value to prevent unnecessary re-renders
   */
  const contextValue = useMemo<WorkspaceContextType>(() => ({
    workspaces,
    currentWorkspace,
    workspaceStatistics,
    projects,
    role,
    members,
    deadLines,
    isLoading,
    error,
    setIsLoading,
    setWorkspaces,
    setCurrentWorkspace,
    setWorkspaceStatistics,
    setProjects,
    setRole,
    setMembers,
    setDeadLines,
    setError,
    fetchWorkspaces,
    fetchWorkspaceById,
  }), [
    workspaces,
    currentWorkspace,
    workspaceStatistics,
    projects,
    role,
    members,
    deadLines,
    isLoading,
    error,
    setIsLoading,
    setWorkspaces,
    setCurrentWorkspace,
    setWorkspaceStatistics,
    setProjects,
    setRole,
    setMembers,
    setDeadLines,
    setError,
    fetchWorkspaces,
    fetchWorkspaceById,
  ]);


  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  );
};

/**
 * Custom hook for accessing workspace context safely
 */
export const useWorkspace = (): WorkspaceContextType => {
  const context = useContext(WorkspaceContext);
  if (!context) {
    throw new Error("useWorkspace must be used within a WorkspaceProvider");
  }
  return context;
};
