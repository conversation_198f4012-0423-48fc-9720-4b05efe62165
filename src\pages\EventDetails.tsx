import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths, isSameMonth, isSameDay, isToday } from 'date-fns';
import { CalendarIcon, Clock, Edit, Trash2, AlertTriangle, ArrowLeft, ChevronLeft, ChevronRight } from 'lucide-react';

import Navbar from '@/components/Navbar';
import Loader from '@/components/Loader';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { getEvent, deleteEvent, CalendarEvent } from '@/api/calendarApi';
import { cn } from '@/lib/utils';
import { getFullImageUrl } from '@/utils/imageUtils';

// Type color schemes with dark mode support
const typeColors = {
  meeting: 'bg-purple-100 text-purple-800 border-purple-300 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700/50',
  deadline: 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700/50',
  milestone: 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700/50',
  task: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700/50',
};

// Status color schemes with dark mode support
const statusColors = {
  completed: 'bg-green-500 dark:bg-green-600',
  'in-progress': 'bg-primary dark:bg-primary/80',
  'not-started': 'bg-orange-400 dark:bg-orange-500',
};

const EventDetails = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { eventId } = useParams<{ eventId: string }>();

  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [event, setEvent] = useState<CalendarEvent | null>(null);

  // Mini-calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Calculate days for the mini-calendar
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Load event data
  useEffect(() => {
    const fetchEvent = async () => {
      if (!eventId) return;

      try {
        setIsLoading(true);

        // Make sure we're using the ID with the task_ prefix if it's a task
        const data = await getEvent(eventId);
        setEvent(data);
      } catch (error) {
        console.error('Error fetching event:', error);
        toast({
          title: 'Error',
          description: 'Failed to load event details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvent();
  }, [eventId, toast]);

  // Handle event deletion
  const handleDelete = async () => {
    if (!eventId || !event?.id) return;

    try {
      setIsDeleting(true);

      // Use the event.id from the loaded event data with the task_ prefix intact
      await deleteEvent(event.id);

      toast({
        title: 'Success',
        description: 'Event deleted successfully',
      });

      // Navigate back to calendar
      navigate('/calendar');
    } catch (error) {
      console.error('Error deleting event:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete event',
        variant: 'destructive',
      });
      setIsDeleting(false);
    }
  };

  // Mini-calendar navigation functions
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Handle date selection in mini-calendar
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    navigate(`/calendar?date=${format(date, 'yyyy-MM-dd')}`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="flex justify-center items-center h-64">
          <Loader />
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="container max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-destructive flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Event Not Found
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>The event you're trying to view doesn't exist or you don't have permission to access it.</p>
            </CardContent>
            <CardFooter>
              <Button onClick={() => navigate('/calendar')}>Back to Calendar</Button>
            </CardFooter>
          </Card>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="container max-w-4xl mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
        </div>

        <Card>
          <CardHeader className="pb-4">
            <div className="flex justify-between items-start">
              <div>
                <Badge className={cn("mb-2", typeColors[event.type as keyof typeof typeColors])}>
                  {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                </Badge>
                <CardTitle className="text-2xl">{event.title}</CardTitle>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/calendar/edit/${eventId}`)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the event.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDelete}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Status and Priority */}
            <div className="flex flex-wrap gap-2">
              {event.status && (
                <Badge variant="outline" className={cn(
                  "px-2 py-1",
                  event.status === 'completed' ? "bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700/50" :
                  event.status === 'in-progress' ? "bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700/50" :
                  "bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700/50"
                )}>
                  Status: {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                </Badge>
              )}

              {event.priority && (
                <Badge variant="outline" className={cn(
                  "px-2 py-1",
                  event.priority === 'high' ? "bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700/50" :
                  event.priority === 'medium' ? "bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700/50" :
                  "bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700/50"
                )}>
                  Priority: {event.priority.charAt(0).toUpperCase() + event.priority.slice(1)}
                </Badge>
              )}
            </div>

            {/* Date and Time */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground flex items-center">
                <CalendarIcon className="h-4 w-4 mr-1" />
                Date & Time
              </h3>
              <div className="bg-muted/50 dark:bg-muted/20 p-3 rounded-md border border-border/50">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                  <div>
                    <p className="font-medium">
                      {format(new Date(event.start_date), 'EEEE, MMMM d, yyyy')}
                      {event.end_date && ` - ${format(new Date(event.end_date), 'EEEE, MMMM d, yyyy')}`}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(event.start_date), 'h:mm a')}
                      {event.end_date && ` - ${format(new Date(event.end_date), 'h:mm a')}`}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            {event.description && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                <div className="bg-muted/50 dark:bg-muted/20 p-3 rounded-md border border-border/50">
                  <p className="whitespace-pre-wrap">{event.description}</p>
                </div>
              </div>
            )}

            {/* Project */}
            {event.project && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Project</h3>
                <div className="bg-muted/50 dark:bg-muted/20 p-3 rounded-md border border-border/50">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <p className="font-medium">{event.project.name}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Assignees */}
            {event.assignees && event.assignees.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Assignees</h3>
                <div className="bg-muted/50 dark:bg-muted/20 p-3 rounded-md border border-border/50">
                  <div className="flex flex-wrap gap-2">
                    {event.assignees.map((assignee) => (
                      <div key={`detail-assignee-${assignee.id}`} className="flex items-center bg-background dark:bg-card rounded-full px-3 py-1 border">
                        <Avatar className="h-6 w-6 mr-2">
                          <AvatarImage src={getFullImageUrl(assignee.avatar)} alt={assignee.name} />
                          <AvatarFallback>{assignee.name ? assignee.name.charAt(0) : '?'}</AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{assignee.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>

          <CardFooter className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={() => navigate(-1)}>
              Back
            </Button>
            <Button onClick={() => navigate(`/calendar/edit/${eventId}`)}>
              <Edit className="h-4 w-4 mr-1" />
              Edit Event
            </Button>
          </CardFooter>
        </Card>

        {/* Mini Calendar */}
        <div className="mt-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Calendar</CardTitle>
              <CardDescription>
                Select a date to view events
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Month navigation */}
              <div className="flex justify-between items-center mb-4">
                <Button variant="outline" size="sm" onClick={prevMonth}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <h3 className="text-sm font-medium">
                  {format(currentMonth, 'MMMM yyyy')}
                </h3>
                <Button variant="outline" size="sm" onClick={nextMonth}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              {/* Day headers */}
              <div className="grid grid-cols-7 text-center mb-1">
                {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                  <div key={day} className="text-xs text-muted-foreground">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar days */}
              <div className="grid grid-cols-7 gap-1">
                {monthDays.map((day) => {
                  const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;

                  return (
                    <div
                      key={day.toString()}
                      className={cn(
                        "h-8 w-8 flex items-center justify-center text-xs rounded-full cursor-pointer",
                        isToday(day) ? "font-bold" : "",
                        isSelected ? "bg-primary text-primary-foreground" : "",
                        !isSameMonth(day, currentMonth) ? "text-muted-foreground/50" : ""
                      )}
                      onClick={() => handleDateSelect(day)}
                    >
                      {format(day, 'd')}
                    </div>
                  );
                })}
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => navigate('/calendar')}
              >
                View Full Calendar
              </Button>
            </CardFooter>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default EventDetails;
