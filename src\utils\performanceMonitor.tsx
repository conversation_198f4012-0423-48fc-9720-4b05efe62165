import React, { useEffect, useLayoutEffect, useRef } from 'react';
import { startMark, endMark } from './performanceMonitor';

/**
 * Higher-order component to measure component render time
 * @param Component The component to measure
 * @param componentName The name of the component (optional, defaults to Component.displayName or Component.name)
 * @returns The wrapped component
 */
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
): React.FC<P> {
  // Only enable in development mode
  const isDevelopment = import.meta.env.MODE === 'development';
  if (!isDevelopment) return Component as React.FC<P>;
  
  const displayName = componentName || Component.displayName || Component.name || 'UnknownComponent';
  
  const WrappedComponent: React.FC<P> = (props) => {
    const renderMarkName = `${displayName} render`;
    startMark(renderMarkName);
    
    // Use React's useEffect to measure mount time
    useEffect(() => {
      endMark(renderMarkName);
      
      // Measure unmount time
      return () => {
        startMark(`${displayName} unmount`);
        setTimeout(() => {
          endMark(`${displayName} unmount`);
        }, 0);
      };
    }, []);
    
    return <Component {...props} />;
  };
  
  WrappedComponent.displayName = `withPerformanceTracking(${displayName})`;
  
  return WrappedComponent;
}

/**
 * Custom hook to measure component render time
 * @param componentName The name of the component
 */
export function useRenderTracking(componentName: string): void {
  // Only enable in development mode
  const isDevelopment = import.meta.env.MODE === 'development';
  if (!isDevelopment) return;
  
  // Track render count
  const renderCount = useRef(0);
  
  // Use layout effect to measure render time as early as possible
  useLayoutEffect(() => {
    const count = ++renderCount.current;
    endMark(`${componentName} render #${count}`);
  });
  
  // Start timing the next render
  startMark(`${componentName} render #${renderCount.current + 1}`);
}
