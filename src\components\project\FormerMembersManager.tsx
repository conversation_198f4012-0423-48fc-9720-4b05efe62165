import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AlertCircle, Users, ArrowRight } from 'lucide-react';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import { Task } from '@/api/tasksApi';
import { getTasks } from '@/api/tasksApi';
import { useToast } from '@/components/ui/use-toast';
import ReassignTasksModal from '@/components/task/ReassignTasksModal';
import { useQueryClient } from '@tanstack/react-query';

interface FormerMembersManagerProps {
  project: Project;
}

const FormerMembersManager: React.FC<FormerMembersManagerProps> = ({ project }) => {
  const [formerMembers, setFormerMembers] = useState<User[]>([]);
  const [tasksWithFormerMembers, setTasksWithFormerMembers] = useState<Record<number, Task[]>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMember, setSelectedMember] = useState<User | null>(null);
  const [isReassignModalOpen, setIsReassignModalOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Load tasks and identify former members
  useEffect(() => {
    const loadTasksAndIdentifyFormerMembers = async () => {
      if (!project) return;
      
      setIsLoading(true);
      try {
        // Get all tasks for the project
        const tasks = await getTasks(project.id);
        
        // Identify former members and their tasks
        const formerMembersMap: Record<number, User> = {};
        const tasksMap: Record<number, Task[]> = {};
        
        // Process each task to find assignees who are former members
        tasks.forEach((task: Task) => {
          if (task.assignees && task.assignees.length > 0) {
            task.assignees.forEach(assignee => {
              if (assignee.status === 'former_member') {
                // Add to former members map if not already there
                if (!formerMembersMap[assignee.id]) {
                  formerMembersMap[assignee.id] = assignee as User;
                  tasksMap[assignee.id] = [];
                }
                
                // Add task to this former member's tasks
                tasksMap[assignee.id].push(task);
              }
            });
          }
        });
        
        // Convert maps to arrays
        setFormerMembers(Object.values(formerMembersMap));
        setTasksWithFormerMembers(tasksMap);
      } catch (error) {
        console.error('Error loading tasks:', error);
        toast({
          title: 'Error',
          description: 'Failed to load tasks and identify former members',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadTasksAndIdentifyFormerMembers();
  }, [project, toast]);

  const handleReassignTasks = (member: User) => {
    setSelectedMember(member);
    setIsReassignModalOpen(true);
  };

  const handleTasksReassigned = () => {
    // Invalidate queries to refresh data
    queryClient.invalidateQueries({ queryKey: ['tasks'] });
    
    // Refresh the former members list
    const updatedFormerMembers = [...formerMembers];
    const updatedTasksMap = { ...tasksWithFormerMembers };
    
    // Remove the member from our local state if they have no more tasks
    if (selectedMember) {
      delete updatedTasksMap[selectedMember.id];
      setTasksWithFormerMembers(updatedTasksMap);
      
      // If member has no more tasks, remove them from the list
      setFormerMembers(updatedFormerMembers.filter(m => m.id !== selectedMember.id));
    }
  };

  // If there are no former members, don't render anything
  if (formerMembers.length === 0 && !isLoading) {
    return null;
  }

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-amber-500" />
          <CardTitle className="text-lg">Former Project Members</CardTitle>
        </div>
        <CardDescription>
          These members have left the project but still have assigned tasks
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-4 text-muted-foreground">
            Loading former members...
          </div>
        ) : (
          <div className="space-y-4">
            {formerMembers.map(member => (
              <div 
                key={member.id} 
                className="flex items-center justify-between p-3 bg-amber-50 border border-amber-200 rounded-md"
              >
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src={member.profile_picture}
                      alt={`${member.first_name} ${member.last_name}`}
                    />
                    <AvatarFallback>
                      {member.first_name?.[0]}{member.last_name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{member.first_name} {member.last_name}</p>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Users className="h-3.5 w-3.5 mr-1" />
                      <span>
                        {tasksWithFormerMembers[member.id]?.length || 0} assigned tasks
                      </span>
                    </div>
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleReassignTasks(member)}
                  className="gap-1"
                >
                  Reassign Tasks
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Reassign Tasks Modal */}
      {selectedMember && (
        <ReassignTasksModal
          isOpen={isReassignModalOpen}
          onClose={() => setIsReassignModalOpen(false)}
          formerMember={selectedMember}
          project={project}
          tasks={tasksWithFormerMembers[selectedMember.id] || []}
          onTasksReassigned={handleTasksReassigned}
        />
      )}
    </Card>
  );
};

export default FormerMembersManager;
