
const axios = require('axios');

/**
 * Send a test request to an endpoint
 * @param {string} method HTTP method
 * @param {string} url Endpoint URL
 * @param {Object} payload Request payload
 * @param {string} baseUrl Base URL for the API
 * @param {Object} options Additional options like headers
 * @returns {Promise<Object>} Response object
 */
async function testEndpoint(method, url, payload, baseUrl, options = {}) {
  try {
    // Get auth token if available
    let token = options.token || null;
    if (!token && options.useLocalToken) {
      try {
        // Simulate localStorage in Node environment
        token = 'mock-auth-token';
        console.log(`Using auth token: ${token.substring(0, 10)}...`);
      } catch (error) {
        console.log('No auth token found in localStorage');
      }
    }
    
    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(options.headers || {})
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    // Make request
    const fullUrl = `${baseUrl}${url.startsWith('/') ? url : `/${url}`}`;
    console.log(`Testing ${method.toUpperCase()} ${fullUrl}`);
    
    // Replace dynamic params with test values
    const testUrl = fullUrl.replace(/:(\w+)/g, (_, param) => {
      // Use provided param values if available
      if (options.params && options.params[param]) {
        return options.params[param];
      }
      // Default fallbacks
      if (param === 'id') return '1';
      return 'test';
    });
    
    // Log request details for debugging
    console.log('Request headers:', headers);
    if (payload) {
      console.log('Request payload:', payload);
    }
    
    // Send request
    const response = await axios({
      method: method.toLowerCase(),
      url: testUrl,
      data: payload ? payload : undefined,
      headers,
      validateStatus: () => true, // Don't throw on error status codes
      timeout: options.timeout || 10000 // Default timeout 10s
    });
    
    // Log response for debugging
    console.log(`Response status: ${response.status}`);
    console.log('Response data:', response.data);
    
    return {
      status: response.status,
      data: response.data,
      success: response.status >= 200 && response.status < 300,
      headers: response.headers
    };
  } catch (error) {
    console.error('Request error:', error.message);
    return {
      status: error.response?.status || 500,
      data: error.response?.data || { message: error.message },
      success: false,
      error: error.message
    };
  }
}

/**
 * Run a sequence of API tests to simulate a user journey
 * @param {Object[]} testSequence Array of test steps
 * @param {string} baseUrl Base URL for the API
 * @returns {Promise<Object[]>} Array of test results
 */
async function runTestSequence(testSequence, baseUrl) {
  const results = [];
  let token = null;
  
  for (const [index, test] of testSequence.entries()) {
    console.log(`\n[Step ${index + 1}] ${test.description || 'Testing endpoint'}`);
    
    // Use token from previous steps if available
    const options = {
      ...test.options,
      token: test.options?.token || token
    };
    
    const result = await testEndpoint(
      test.method,
      test.url,
      test.payload,
      baseUrl,
      options
    );
    
    // Save token if login step
    if (test.saveToken && result.success && result.data.token) {
      token = result.data.token;
      console.log('Saved token for subsequent requests');
    }
    
    results.push({
      step: index + 1,
      description: test.description,
      success: result.success,
      status: result.status,
      data: result.data
    });
    
    // Stop sequence if a critical step fails
    if (test.critical && !result.success) {
      console.error(`Critical step ${index + 1} failed. Stopping test sequence.`);
      break;
    }
  }
  
  return results;
}

/**
 * Create a complete E2E test journey
 * @param {string} baseUrl API base URL
 * @returns {Promise<Object>} Test results summary
 */
async function testUserJourney(baseUrl = 'http://127.0.0.1:8000/api') {
  const journey = [
    {
      description: '1. Login',
      method: 'POST',
      url: '/login',
      payload: { email: '<EMAIL>', password: 'password' },
      saveToken: true,
      critical: true,
      options: {}
    },
    {
      description: '2. Get user profile',
      method: 'GET',
      url: '/user',
      critical: true,
      options: {}
    },
    {
      description: '3. Get projects',
      method: 'GET',
      url: '/projects',
      options: {}
    },
    {
      description: '4. Get project details',
      method: 'GET',
      url: '/projects/1',
      options: {}
    },
    {
      description: '5. Get project lists',
      method: 'GET',
      url: '/projects/1/lists',
      options: {}
    },
    {
      description: '6. Get tasks',
      method: 'GET',
      url: '/tasks',
      options: {
        params: { project_id: 1 }
      }
    },
    {
      description: '7. Get team members',
      method: 'GET',
      url: '/workspaces/1',
      options: {}
    },
    {
      description: '8. Logout',
      method: 'POST',
      url: '/logout',
      options: {}
    }
  ];
  
  console.log(`Starting E2E test journey against ${baseUrl}`);
  const results = await runTestSequence(journey, baseUrl);
  
  // Create summary
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  
  console.log('\n=== E2E TEST SUMMARY ===');
  console.log(`Passed: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`);
  console.log('=======================\n');
  
  return {
    totalTests,
    passedTests,
    results,
    success: passedTests === totalTests
  };
}

module.exports = {
  testEndpoint,
  runTestSequence,
  testUserJourney
};
