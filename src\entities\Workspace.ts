import { normalizeUser, User } from "./User";

export interface Workspace {
    id: number;
    name: string;
    owner: User;
    background: string;
    description?: string;
    created_at?: string;
    updated_at?: string;
}

export function normalizeWorkspace(data: any): Workspace {
    return {
        id: Number(data.id),
        name: String(data.name),
        owner: normalizeUser(data.owner),
        background: String(data.background),
        description: data.description ?? undefined,
        created_at: data.created_at ?? undefined,
        updated_at: data.updated_at ?? undefined,
    };
}