import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';

// Send a workspace invitation
export const sendWorkspaceInvitation = async (workspaceId: number, email: string, roleId: number) => {
  try {
    const response = await api.post(`/workspaces/${workspaceId}/invitations`, { email, role_id: roleId });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to send invitation",
      "Could not send the workspace invitation"
    );
  }
};

// Get all pending invitations for a workspace
export const getWorkspaceInvitations = async (workspaceId: number) => {
  try {
    const response = await api.get(`/workspaces/${workspaceId}/invitations`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch invitations",
      "Could not load workspace invitations"
    );
  }
};

// Cancel a workspace invitation
export const cancelWorkspaceInvitation = async (workspaceId: number, invitationId: number) => {
  try {
    const response = await api.delete(`/workspaces/${workspaceId}/invitations/${invitationId}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to cancel invitation",
      "Could not cancel the invitation"
    );
  }
};

// Accept a workspace invitation
export const acceptWorkspaceInvitation = async (token: string) => {
  try {
    const response = await api.post(`/accept-workspace-invitation`, { token });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to accept invitation",
      "Could not accept the workspace invitation"
    );
  }
};
