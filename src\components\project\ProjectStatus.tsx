import React from 'react';
import { <PERSON>ertCircle, Check, Clock, PlayCircle, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProjectProgress, ProjectPriority } from '@/entities/Project';
import { useTranslation } from 'react-i18next';

interface ProjectStatusProps {
  progress: number | string; // Accept both string and number
  priority?: number | string;
}

// Helper to convert string enum names to enum values
const getProgressEnum = (value: number | string): ProjectProgress => {
  if (typeof value === 'number') return value;

  switch (value) {
    case 'NotStarted': return ProjectProgress.NotStarted;
    case 'InProgress': return ProjectProgress.InProgress;
    case 'UnderReview': return ProjectProgress.UnderReview;
    case 'Completed': return ProjectProgress.Completed;
    // Support both English and Turkish for backward compatibility
    // Turkish names below are deprecated and kept only for backward compatibility
    case 'Başlanmadı': return ProjectProgress.NotStarted; // Not Started
    case 'DevamEdiyor': return ProjectProgress.InProgress; // In Progress
    case 'İncelemeAltında': return ProjectProgress.UnderReview; // Under Review
    case 'Tamamlandı': return ProjectProgress.Completed; // Completed
    default: return ProjectProgress.NotStarted;
  }
};

const getPriorityEnum = (value: number | string): ProjectPriority => {
  if (typeof value === 'number') return value;

  // Handle string values that are numeric
  if (typeof value === 'string' && !isNaN(Number(value))) {
    const numValue = Number(value);
    if (numValue === 0) return ProjectPriority.Low;
    if (numValue === 1) return ProjectPriority.Medium;
    if (numValue === 2) return ProjectPriority.High;
  }

  // Handle string values
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase();

    if (lowerValue === 'low' || lowerValue === '0' || lowerValue === 'düşük') {
      return ProjectPriority.Low;
    }

    if (lowerValue === 'medium' || lowerValue === '1' || lowerValue === 'orta') {
      return ProjectPriority.Medium;
    }

    if (lowerValue === 'high' || lowerValue === '2' || lowerValue === 'yüksek') {
      return ProjectPriority.High;
    }
  }

  // Default to Medium if value is not recognized
  return ProjectPriority.Medium;
};

// Config using translation keys for labels
const getProgressConfig = (t: any) => ({
  [ProjectProgress.NotStarted]: {
    icon: Clock,
    label: t('project.status.notStarted', 'Not Started'),
    color: 'text-warning-foreground bg-warning dark:shadow-sm dark:shadow-warning/20'
  },
  [ProjectProgress.InProgress]: {
    icon: PlayCircle,
    label: t('project.status.inProgress', 'In Progress'),
    color: 'text-info-foreground bg-info dark:shadow-sm dark:shadow-info/20'
  },
  [ProjectProgress.UnderReview]: {
    icon: Eye,
    label: t('project.status.underReview', 'Under Review'),
    color: 'text-primary-foreground bg-primary dark:shadow-sm dark:shadow-primary/20'
  },
  [ProjectProgress.Completed]: {
    icon: Check,
    label: t('project.status.completed', 'Completed'),
    color: 'text-success-foreground bg-success dark:shadow-sm dark:shadow-success/20'
  },
});

const getPriorityConfig = (t: any) => ({
  [ProjectPriority.Low]: {
    label: t('project.priority.low', 'Low'),
    color: 'text-priority-low-foreground bg-priority-low dark:shadow-sm dark:shadow-priority-low/20'
  },
  [ProjectPriority.Medium]: {
    label: t('project.priority.medium', 'Medium'),
    color: 'text-priority-medium-foreground bg-priority-medium dark:shadow-sm dark:shadow-priority-medium/20'
  },
  [ProjectPriority.High]: {
    label: t('project.priority.high', 'High'),
    color: 'text-priority-high-foreground bg-priority-high dark:shadow-sm dark:shadow-priority-high/20'
  },
});

const ProjectStatus: React.FC<ProjectStatusProps> = ({ progress, priority }) => {
  const { t } = useTranslation();

  // Convert to enum values
  const progressEnum = getProgressEnum(progress);
  const priorityEnum = priority !== undefined ? getPriorityEnum(priority) : undefined;

  // Get config with translations
  const progressConfig = getProgressConfig(t);
  const priorityConfig = getPriorityConfig(t);

  // Get config
  const statusConfig = progressConfig[progressEnum] ?? {
    icon: AlertCircle,
    label: t('project.status.unknown', 'Unknown Status'),
    color: 'text-gray-500 bg-gray-50'
  };

  const priorityInfo = priorityEnum !== undefined ? priorityConfig[priorityEnum] : null;

  return (
    <div className="flex items-center text-xs">
      <div className={cn(
        "px-2 py-1 rounded-md font-medium flex items-center gap-1",
        statusConfig.color
      )}>
        <statusConfig.icon size={12} className="dark:text-foreground/90" />
        <span>{statusConfig.label}</span>
      </div>

      {priorityInfo && (
        <div className={cn(
          "ml-2 px-2 py-1 rounded-md font-medium",
          priorityInfo.color
        )}>
          <span>{priorityInfo.label}</span>
        </div>
      )}
    </div>
  );
};

export default ProjectStatus;