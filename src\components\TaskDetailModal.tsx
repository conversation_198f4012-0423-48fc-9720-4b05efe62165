import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Task } from '@/api/tasksApi';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import TaskChat from './TaskChat';
import { StatusBadge } from './ui/status-badge';
import { PriorityIndicator } from './ui/priority-indicator';
import { canEditTask, hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import { useAuth } from '@/context/AuthContext';
import { getFullImageUrl } from '@/utils/imageUtils';
import {
  Calendar,
  Clock,
  MessageSquare,
  Info,
  Users,
  X
} from 'lucide-react';
import { format } from 'date-fns';

interface TaskDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: Task | null;
  project: Project | undefined;
  user: User | null;
  onEditTask: () => void;
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({
  isOpen,
  onClose,
  task,
  project,
  user,
  onEditTask
}) => {
  const [activeTab, setActiveTab] = useState('details');
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const commentIdFromUrl = searchParams.get('comment');
  const { user: currentUser } = useAuth();
  // Comment input state
  const [message, setMessage] = useState('');

  // Check if user can add comments - all users including viewers should be able to comment
  const canAddComments = task ? hasProjectPermission(project, currentUser, ProjectPermission.ADD_COMMENT) : false;

  // Set active tab to comments if comment ID is in URL
  useEffect(() => {
    if (commentIdFromUrl) {
      setActiveTab('comments');
    }
  }, [commentIdFromUrl]);

  // Handle URL cleanup when modal closes
  const handleClose = () => {
    // Remove the task and comment parameters from the URL when closing the modal
    const url = new URL(window.location.href);
    url.searchParams.delete('task');
    url.searchParams.delete('comment');

    // Update the URL without reloading the page
    navigate(url.pathname + url.search, { replace: true });

    // Call the original onClose function
    onClose();
  };

  // We don't need to implement handleSendMessage here
  // The TaskChat component will handle sending messages internally
  // We just pass the message state and setMessage function

  if (!task) return null;

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  // We'll use the StatusBadge and PriorityIndicator components instead of these functions

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-hidden flex flex-col" closeButton={false}>
        <DialogHeader className="pb-4 border-b">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <StatusBadge status={task.status || 'todo'} size="sm" />
              <DialogTitle className="text-xl font-semibold">{task.title}</DialogTitle>
            </div>
            <Button variant="ghost" size="icon" onClick={handleClose} className="h-8 w-8 opacity-70 hover:opacity-100">
              <X size={18} />
            </Button>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="mb-4 w-full justify-start border-b rounded-none px-0 h-auto">
            <TabsTrigger
              value="details"
              className="flex items-center gap-1.5 rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:font-medium px-5 py-2.5"
            >
              <Info className="h-4 w-4" />
              Details
            </TabsTrigger>
            <TabsTrigger
              value="comments"
              className="flex items-center gap-1.5 rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:font-medium px-5 py-2.5"
            >
              <MessageSquare className="h-4 w-4" />
              Comments
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden">
            <TabsContent value="details" className="h-full overflow-y-auto mt-0">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2 space-y-6">
                  {/* Description */}
                  <div>
                    <h3 className="text-sm font-medium mb-2 flex items-center gap-1.5 text-foreground">
                      <Info className="h-4 w-4 text-muted-foreground" />
                      Description
                    </h3>
                    <div className="text-sm text-muted-foreground bg-muted/30 p-4 rounded-md min-h-[180px] border border-border/50">
                      {task.description || 'No description provided.'}
                    </div>
                  </div>
                </div>

                <div className="space-y-6 bg-muted/10 p-4 rounded-lg border border-border/50">
                  {/* Status */}
                  <div>
                    <h3 className="text-sm font-medium mb-2 text-foreground">Status</h3>
                    <StatusBadge status={task.status || 'todo'} showIcon size="md" />
                  </div>

                  {/* Priority */}
                  <div>
                    <h3 className="text-sm font-medium mb-2 text-foreground">Priority</h3>
                    <PriorityIndicator priority={task.priority || 'medium'} showLabel size="md" />
                  </div>

                  {/* Dates */}
                  <div>
                    <h3 className="text-sm font-medium mb-2 flex items-center gap-1.5 text-foreground">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      Start Date
                    </h3>
                    <div className="text-sm bg-background px-3 py-1.5 rounded border border-border/50">
                      {formatDate(task.start_date)}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2 flex items-center gap-1.5 text-foreground">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      Due Date
                    </h3>
                    <div className="text-sm bg-background px-3 py-1.5 rounded border border-border/50">
                      {formatDate(task.end_date)}
                    </div>
                  </div>

                  {/* Assignees */}
                  <div>
                    <h3 className="text-sm font-medium mb-2 flex items-center gap-1.5 text-foreground">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      Assignees
                    </h3>

                    {task.assignees && task.assignees.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {task.assignees.map(assignee => (
                          <div
                            key={assignee.id}
                            className={`flex items-center gap-2 px-3 py-1.5 rounded-md ${
                              assignee.status === 'former_member'
                                ? 'bg-amber-50 border border-amber-200'
                                : 'bg-background border border-border/50'
                            }`}
                          >
                            <Avatar className="h-6 w-6">
                              <AvatarImage
                                src={getFullImageUrl(assignee.profile_picture)}
                                alt={`${assignee.first_name} ${assignee.last_name}`}
                              />
                              <AvatarFallback className="text-xs">
                                {assignee.first_name?.[0]}{assignee.last_name?.[0]}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col">
                              <span className="text-sm">
                                {assignee.first_name} {assignee.last_name}
                              </span>
                              {assignee.status === 'former_member' && (
                                <span className="text-xs text-amber-600 font-medium">
                                  Former Member
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-sm bg-background px-3 py-2 rounded border border-border/50">
                        No assignees
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="comments" className="h-full overflow-hidden pt-1 mt-0">
              <TaskChat
                task={task}
                project={project}
                message={message}
                setMessage={setMessage}
                canAddComments={canAddComments}
                highlightCommentId={commentIdFromUrl}
              />
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter className="mt-0 pt-4 border-t flex justify-between">
          <div>
            {/* Only show Edit Task button for users with edit_project permission (admins and editors) */}
            {project && user && hasProjectPermission(project, user, ProjectPermission.EDIT_PROJECT) && (
              <Button variant="outline" onClick={onEditTask} size="sm" className="gap-1.5">
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                  <path d="M11.8536 1.14645C11.6583 0.951184 11.3417 0.951184 11.1465 1.14645L3.71455 8.57836C3.62459 8.66832 3.55263 8.77461 3.50251 8.89155L2.04044 12.303C1.9599 12.491 2.00189 12.709 2.14646 12.8536C2.29103 12.9981 2.50905 13.0401 2.69697 12.9596L6.10847 11.4975C6.2254 11.4474 6.33168 11.3754 6.42164 11.2855L13.8536 3.85355C14.0488 3.65829 14.0488 3.34171 13.8536 3.14645L11.8536 1.14645ZM4.42161 9.28547L11.5 2.20711L12.7929 3.5L5.71455 10.5784L4.21924 11.2192L3.78081 10.7808L4.42161 9.28547Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                </svg>
                Edit Task
              </Button>
            )}
          </div>
          <Button variant="secondary" onClick={handleClose} size="sm">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TaskDetailModal;
