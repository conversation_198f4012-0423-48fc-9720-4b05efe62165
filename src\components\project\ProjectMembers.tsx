
import React, { useState } from 'react';
import { User } from '@/entities/User';
import { User as RUser, Users } from 'lucide-react';
import { getFullImageUrl } from '@/utils/imageUtils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';

interface ProjectMembersProps {
  members: User[];
  dueDate?: string; // Making it optional since we can provide it in ProjectCard
  maxDisplay?: number;
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
}

const ProjectMembers: React.FC<ProjectMembersProps> = ({
  members,
  dueDate,
  maxDisplay = 3,
  size = 'sm',
  showCount = true
}) => {
  const { t } = useTranslation();
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  // Size classes for the avatar
  const sizeClasses = {
    sm: {
      container: "h-6 w-6",
      icon: 14,
      text: "text-xs",
    },
    md: {
      container: "h-8 w-8",
      icon: 16,
      text: "text-sm",
    },
    lg: {
      container: "h-10 w-10",
      icon: 20,
      text: "text-sm",
    },
  };

  // Get the appropriate size classes
  const { container, icon, text } = sizeClasses[size];

  // Random online status for demo purposes (in a real app, this would come from an API)
  const getRandomStatus = (userId: number) => {
    const statuses = ['online', 'offline', 'idle', 'busy'];
    // Use the user ID to deterministically select a status for demo purposes
    return statuses[userId % statuses.length];
  };

  // Status indicator colors
  const statusColors = {
    online: 'bg-success',
    offline: 'bg-muted-foreground',
    idle: 'bg-amber-400',
    busy: 'bg-destructive'
  };

  // Animation variants for the avatars
  const container_variants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item_variants = {
    hidden: { y: 10, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  return (
    <div className="flex items-center gap-2">
      <motion.div
        className="flex -space-x-2"
        variants={container_variants}
        initial="hidden"
        animate="show"
      >
        {members.slice(0, maxDisplay).map((member) => {
          const status = getRandomStatus(member.id);

          return (
            <TooltipProvider key={member.id} delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <motion.div
                    className={cn(
                      container,
                      "rounded-full ring-2 ring-background dark:ring-card overflow-hidden bg-primary/10 dark:bg-white/90 relative",
                      "transition-transform hover:scale-110 hover:z-10"
                    )}
                    variants={item_variants}
                  >
                    {member.profile_picture ? (
                      <img
                        src={getFullImageUrl(member.profile_picture)}
                        alt={member.name || `${member.first_name} ${member.last_name}`}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center">
                        <RUser size={icon} className="text-primary dark:text-primary" />
                      </div>
                    )}

                    {/* Status indicator */}
                    <div className={cn(
                      "absolute bottom-0 right-0 h-2 w-2 rounded-full border border-background",
                      statusColors[status]
                    )} />
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent side="bottom" className={text}>
                  <div className="flex flex-col">
                    <span className="font-medium">{member.name || `${member.first_name} ${member.last_name}`}</span>
                    <span className="text-xs text-muted-foreground capitalize">{status}</span>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}

        {members.length > maxDisplay && (
          <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
            <PopoverTrigger asChild>
              <motion.div
                className={cn(
                  container,
                  "rounded-full bg-muted flex items-center justify-center font-medium text-muted-foreground ring-2 ring-background cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors"
                )}
                variants={item_variants}
              >
                <span className={text}>+{members.length - maxDisplay}</span>
              </motion.div>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-0" align="start">
              <div className="p-2 border-b">
                <h4 className="font-medium">{t('project.teamMembers')}</h4>
              </div>
              <div className="max-h-60 overflow-y-auto p-2">
                {members.slice(maxDisplay).map((member) => {
                  const status = getRandomStatus(member.id);

                  return (
                    <div key={member.id} className="flex items-center gap-2 p-2 hover:bg-accent rounded-md">
                      <div className={cn(
                        "h-8 w-8 rounded-full overflow-hidden bg-primary/10 dark:bg-white/90 relative"
                      )}>
                        {member.profile_picture ? (
                          <img
                            src={getFullImageUrl(member.profile_picture)}
                            alt={member.name || `${member.first_name} ${member.last_name}`}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center">
                            <RUser size={16} className="text-primary dark:text-primary" />
                          </div>
                        )}

                        {/* Status indicator */}
                        <div className={cn(
                          "absolute bottom-0 right-0 h-2 w-2 rounded-full border border-background",
                          statusColors[status]
                        )} />
                      </div>
                      <div>
                        <div className="font-medium text-sm">{member.name || `${member.first_name} ${member.last_name}`}</div>
                        <div className="text-xs text-muted-foreground capitalize">{status}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </PopoverContent>
          </Popover>
        )}
      </motion.div>

      {showCount && members.length > 0 && (
        <span className={cn(text, "text-muted-foreground")}>
          {members.length} {members.length === 1 ? t('project.member') : t('project.members')}
        </span>
      )}
    </div>
  );
};

export default ProjectMembers;
