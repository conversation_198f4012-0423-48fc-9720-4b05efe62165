import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';

export interface NotificationPreferences {
  id: number;
  user_id: number;
  email_comments: boolean;
  email_projects: boolean;
  email_newsletter: boolean;
  in_app_task_assigned: boolean;
  in_app_task_due: boolean;
  in_app_project_activity: boolean;
  created_at: string;
  updated_at: string;
}

// Default notification preferences
const defaultPreferences: NotificationPreferences = {
  id: 0,
  user_id: 0,
  email_comments: true,
  email_projects: true,
  email_newsletter: false,
  in_app_task_assigned: true,
  in_app_task_due: true,
  in_app_project_activity: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

// Get notification preferences
export const getNotificationPreferences = async (): Promise<NotificationPreferences> => {
  try {
    const response = await api.get('/notification-preferences');
    return response.data;
  } catch (error) {
    console.error('Error fetching notification preferences:', error);

    // Return default preferences instead of throwing an error
    return defaultPreferences;
  }
};

// Update notification preferences
export const updateNotificationPreferences = async (preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> => {
  try {
    const response = await api.post('/notification-preferences', preferences);
    return response.data;
  } catch (error) {
    console.error('Error updating notification preferences:', error);

    // Return a merged object of default preferences with the requested changes
    return {
      ...defaultPreferences,
      ...preferences,
      updated_at: new Date().toISOString()
    };
  }
};
