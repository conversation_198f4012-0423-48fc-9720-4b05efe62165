import { Project } from '@/entities/Project';
import { Task } from '@/api/tasksApi';

/**
 * Calculate project progress percentage based on completed tasks
 *
 * @param project The project object
 * @param tasks Optional array of tasks (if not provided, will use project.total_tasks and project.completed_tasks)
 * @returns Progress percentage (0-100)
 */
export const calculateProjectProgress = (
  project: Project | null,
  tasks?: Task[]
): number => {
  if (!project) return 0;

  // If tasks are provided, calculate based on those
  if (tasks && tasks.length > 0) {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task =>
      task.status === 'completed' ||
      task.status === '2' ||
      task.status === 2
    ).length;

    return totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  }

  // Otherwise use the project's own task counts
  if (project.total_tasks && project.total_tasks > 0) {
    const completedTasks = project.completed_tasks || 0;
    return Math.round((completedTasks / project.total_tasks) * 100);
  }

  // If completion_percentage is directly available, use that
  if (project.completion_percentage !== null && project.completion_percentage !== undefined) {
    return project.completion_percentage;
  }

  // Fallback to progress field if it's a number between 0-100
  if (typeof project.progress === 'number' && project.progress >= 0 && project.progress <= 100) {
    return project.progress;
  }

  // Default fallback
  return 0;
};

/**
 * Get the appropriate color for a progress value
 *
 * @param progress Progress percentage (0-100)
 * @returns CSS color variable name or hex color
 */
export const getProgressColor = (progress: number): string => {
  if (progress >= 100) return '#10b981'; // Success green
  if (progress >= 75) return '#3b82f6'; // Blue
  if (progress >= 50) return '#6366f1'; // Primary purple
  if (progress >= 25) return '#f59e0b'; // Amber
  return '#ef4444'; // Red
};

/**
 * Get the status text based on progress percentage
 *
 * @param progress Progress percentage (0-100)
 * @returns Status text
 */
export const getProgressStatus = (progress: number): string => {
  if (progress >= 100) return 'Completed';
  if (progress >= 75) return 'Almost Done';
  if (progress >= 50) return 'Halfway';
  if (progress >= 25) return 'In Progress';
  if (progress > 0) return 'Just Started';
  return 'Not Started';
};
