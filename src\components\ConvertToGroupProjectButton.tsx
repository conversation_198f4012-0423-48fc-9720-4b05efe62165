import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Project } from '@/entities/Project';
import { User } from '@/entities/User';
import { Users } from 'lucide-react';
import { convertToGroupProject } from '@/api/projectsApi';

interface ConvertToGroupProjectButtonProps {
  project: Project;
  user: User | null;
  onProjectConverted: (updatedProject: Project) => void;
}

const ConvertToGroupProjectButton: React.FC<ConvertToGroupProjectButtonProps> = ({
  project,
  user,
  onProjectConverted
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Only show the button if the user is the project owner and it's not already a group project
  if (!user || project.user_id !== user.id || project.is_group_project) {
    return null;
  }

  const handleConvertToGroup = async () => {
    try {
      setIsLoading(true);
      const response = await convertToGroupProject(project.id);

      // Create an updated project object with the is_group_project flag set to true
      const updatedProject = {
        ...project,
        is_group_project: true,
        // If the response includes a project object, merge its properties
        ...(response?.project || {})
      };

      console.log('Project converted to group project:', updatedProject);

      // Show success message
      toast({
        title: "Project converted",
        description: "This project is now a group project. You can add members to it.",
      });

      // Update the project in the parent component
      onProjectConverted(updatedProject);

      // Reload the page to ensure all components are updated
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      console.error('Error converting project to group:', error);

      // Even if there's an error, the backend might have successfully converted the project
      // So we'll reload the page after a delay to get the latest state
      toast({
        title: "Processing...",
        description: "The project may have been converted. Refreshing the page to check status.",
      });

      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleConvertToGroup}
      disabled={isLoading}
      className="flex items-center gap-1"
    >
      <Users size={16} className="mr-1" />
      {isLoading ? "Converting..." : "Convert to Group Project"}
    </Button>
  );
};

export default ConvertToGroupProjectButton;
