import React, { useState, useEffect } from 'react';
import { Activity, User, FileText, CheckCircle, MessageSquare } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getRecentActivities, ActivityItem } from '@/api/activitiesApi';
import { Skeleton } from '@/components/ui/skeleton';

// Define the activity types

const ActivityIcon = ({ type }: { type: ActivityItem['type'] }) => {
  switch (type) {
    case 'project_created':
      return <FileText className="h-4 w-4 text-blue-500" />;
    case 'task_completed':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'user_joined':
      return <User className="h-4 w-4 text-purple-500" />;
    case 'comment_added':
      return <MessageSquare className="h-4 w-4 text-indigo-500" />;
    default:
      return <Activity className="h-4 w-4 text-gray-500" />;
  }
};

const ActivityMessage = ({ item }: { item: ActivityItem }) => {
  switch (item.type) {
    case 'project_created':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span> created a new project{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    case 'task_completed':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span> completed task{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    case 'user_joined':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span> joined project{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    case 'comment_added':
      return (
        <span>
          <span className="font-medium">{item.user.name}</span> commented on{' '}
          <span className="font-medium">{item.target}</span>
        </span>
      );
    default:
      return <span>Unknown activity</span>;
  }
};

const RecentActivity: React.FC = () => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setIsLoading(true);
        const data = await getRecentActivities(5);
        setActivities(data);
      } catch (error) {
        // Error is already handled in the API function
      } finally {
        setIsLoading(false);
      }
    };

    fetchActivities();
  }, []);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-lg font-medium">
          <Activity className="mr-2 h-5 w-5 text-primary" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {isLoading ? (
            // Loading skeleton
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="flex items-start space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-3 w-[120px]" />
                </div>
              </div>
            ))
          ) : activities.length === 0 ? (
            // No activities state
            <p className="text-sm text-muted-foreground text-center py-4">
              No recent activity to display
            </p>
          ) : (
            // Activities list
            activities.map(activity => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="relative mt-1">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                    <AvatarFallback>
                      {activity.user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -right-1 -bottom-1 rounded-full bg-background p-0.5">
                    <ActivityIcon type={activity.type} />
                  </div>
                </div>
                <div>
                  <p className="text-sm">
                    <ActivityMessage item={activity} />
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">{activity.timestamp}</p>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentActivity;
