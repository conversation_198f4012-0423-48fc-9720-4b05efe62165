import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, ArrowRight } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Project } from '@/entities/Project';
import { getAllProjects } from '@/api/projectsApi';
import { useToast } from '@/components/ui/use-toast';
import Loader from '@/components/Loader';

const UpcomingDeadlines: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchProjects = async () => {
      setIsLoading(true);
      try {
        const data = await getAllProjects();
        setProjects(data);
      } catch (error) {
        toast({
          title: "Failed to load deadlines",
          description: "Could not load upcoming deadlines. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, [toast]);

  // Get projects with upcoming deadlines (next 14 days)
  const now = new Date();
  const twoWeeksLater = new Date();
  twoWeeksLater.setDate(now.getDate() + 14);

  const upcomingDeadlines = projects
    .filter(project => {
      if (!project.end_date) return false;
      const endDate = new Date(project.end_date);
      return endDate >= now && endDate <= twoWeeksLater;
    })
    .sort((a, b) => {
      const dateA = new Date(a.end_date || '');
      const dateB = new Date(b.end_date || '');
      return dateA.getTime() - dateB.getTime();
    })
    .slice(0, 5); // Show only 5 most imminent deadlines

  const handleProjectClick = (projectId: number) => {
    navigate(`/projects/${projectId}/board`);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No date';
    
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Check if it's today
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    }
    
    // Check if it's tomorrow
    if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    }
    
    // Otherwise return formatted date
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-lg font-medium">
          <Calendar className="mr-2 h-5 w-5 text-primary" />
          Upcoming Deadlines
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader />
          </div>
        ) : upcomingDeadlines.length > 0 ? (
          <div className="space-y-4">
            {upcomingDeadlines.map(project => (
              <div 
                key={project.id}
                className="flex items-center justify-between p-3 rounded-md border border-border hover:bg-accent/50 cursor-pointer transition-colors"
                onClick={() => handleProjectClick(project.id)}
              >
                <div>
                  <h3 className="font-medium text-sm">{project.name}</h3>
                  <div className="flex items-center text-xs text-muted-foreground mt-1">
                    <Clock className="h-3 w-3 mr-1" />
                    Due {formatDate(project.end_date)}
                  </div>
                </div>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-muted-foreground">
            <p>No upcoming deadlines in the next 14 days</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UpcomingDeadlines;
