import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON>alogDescription, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { getWorkspaces } from '@/api/workspacesApi';
import { Workspace } from '@/entities/Workspace';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';

interface WorkspaceSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (workspaceId: number) => void;
  title: string;
  description: string;
  actionText: string;
  currentWorkspaceId?: number;
}

const WorkspaceSelectionModal: React.FC<WorkspaceSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  title,
  description,
  actionText,
  currentWorkspaceId
}) => {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [selectedWorkspaceId, setSelectedWorkspaceId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadWorkspaces();
    }
  }, [isOpen]);

  const loadWorkspaces = async () => {
    setIsLoading(true);
    try {
      const workspacesData = await getWorkspaces();
      // Filter out the current workspace if provided
      const filteredWorkspaces = currentWorkspaceId 
        ? workspacesData.filter((w: Workspace) => w.id !== currentWorkspaceId)
        : workspacesData;
      
      setWorkspaces(filteredWorkspaces);
      
      // Select the first workspace by default if available
      if (filteredWorkspaces.length > 0 && !selectedWorkspaceId) {
        setSelectedWorkspaceId(filteredWorkspaces[0].id);
      }
    } catch (error) {
      console.error('Failed to load workspaces:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = () => {
    if (selectedWorkspaceId) {
      setIsSubmitting(true);
      onSelect(selectedWorkspaceId);
      // Note: We don't close the modal here as the parent component will handle that
      // based on the success/failure of the operation
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : workspaces.length === 0 ? (
          <div className="py-6 text-center">
            <p className="text-muted-foreground">No other workspaces available. Please create a new workspace first.</p>
          </div>
        ) : (
          <div className="py-4">
            <RadioGroup 
              value={selectedWorkspaceId?.toString()} 
              onValueChange={(value) => setSelectedWorkspaceId(parseInt(value))}
              className="space-y-3"
            >
              {workspaces.map((workspace) => (
                <div key={workspace.id} className="flex items-center space-x-2 border p-3 rounded-md">
                  <RadioGroupItem value={workspace.id.toString()} id={`workspace-${workspace.id}`} />
                  <Label htmlFor={`workspace-${workspace.id}`} className="flex-1 cursor-pointer">
                    <div className="font-medium">{workspace.name}</div>
                    {workspace.description && (
                      <div className="text-sm text-muted-foreground">{workspace.description}</div>
                    )}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={isLoading || workspaces.length === 0 || !selectedWorkspaceId || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              actionText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WorkspaceSelectionModal;
