{"search": {"title": "Buscar", "button": "Buscar", "placeholder": "Buscar proyectos, tareas o personas...", "tip": "Consejo: Usa Ctrl+K para búsquedas más rápidas.", "tipTitle": "Consejo pro:"}, "settings": {"title": "Configuración", "subtitle": "Administra la configuración y preferencias de tu cuenta", "tabs": {"general": "General", "notifications": "Notificaciones", "security": "Seguridad"}, "appearance": {"title": "Apariencia", "subtitle": "Personaliza el aspecto y la sensación de la aplicación", "darkMode": "<PERSON><PERSON>", "language": "Idioma", "lightModeActivated": "Modo claro activado", "darkModeActivated": "Modo oscuro activado", "themeChanged": "El tema de la aplicación ha sido cambiado a modo {{mode}}."}, "emailNotifications": {"title": "Notificaciones por Correo", "subtitle": "Elige qué tipos de correos electrónicos recibes", "comments": {"title": "Comentarios y Menciones", "description": "Recibe notificaciones cuando alguien te menciona o comenta en tus proyectos"}, "projects": {"title": "Actualizaciones de Proyectos", "description": "Recibe notificaciones sobre cambios de estado y hitos de proyectos"}, "newsletter": {"title": "Boletín y Actualizaciones", "description": "Recibe actualizaciones de productos y boletines informativos"}, "saveButton": "Guardar Preferencias de Notificación"}, "inAppNotifications": {"title": "Notificaciones en la Aplicación", "subtitle": "Configura tus ajustes de notificaciones en la aplicación", "taskAssigned": "<PERSON><PERSON>", "taskDue": "<PERSON><PERSON>róxi<PERSON> a <PERSON>encer", "projectActivity": "Actividad del Proyecto"}, "security": {"title": "Contraseña", "subtitle": "Actualiza tu contraseña", "currentPassword": "Contraseña Actual", "newPassword": "Nueva Contraseña", "confirmPassword": "Con<PERSON><PERSON><PERSON>", "updateButton": "Actualizar <PERSON>"}, "languages": {"english": "Inglés", "spanish": "Español", "french": "<PERSON><PERSON><PERSON><PERSON>", "turkish": "<PERSON><PERSON><PERSON>", "changed": "Idioma cambiado", "changedDescription": "El idioma de la aplicación ha sido cambiado a {{language}}."}}, "profile": {"title": "Perfil", "subtitle": "Administra tu información personal", "photo": {"title": "Foto de Perfil", "subtitle": "Actualiza tu foto de perfil", "uploadButton": "Subir Foto"}, "personalInfo": {"title": "Información Personal", "subtitle": "Actualiza tus datos personales", "firstName": "Nombre", "lastName": "Apellido", "email": "Correo Electrónico", "phone": "Número de Teléfono", "jobTitle": "Cargo", "birthDate": "<PERSON><PERSON> de Nacimiento", "bio": "Biografía", "saveButton": "Guardar Cambios"}}, "common": {"save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "back": "Atrás", "next": "Siguient<PERSON>", "submit": "Enviar", "loading": "Cargando...", "retry": "Reintentar", "error": "Error"}, "navbar": {"suite": "Suite", "workspace": "Espacio de Trabajo", "timeline": "Cronología", "calendar": "Calendario", "team": "Equipo", "search": "Buscar...", "help": "<PERSON><PERSON><PERSON>", "profile": "Perfil", "userSettings": "Configuración de Usuario", "workspaceSettings": "Configuración del Espacio de Trabajo", "signOut": "<PERSON><PERSON><PERSON>", "lightMode": "Cambiar a modo claro", "darkMode": "Cambiar a modo oscuro"}, "workspace": {"welcome": "¡Bienvenido de nuevo, {{name}}! Esto es lo que está sucediendo hoy.", "allProjects": "Todos los Proyectos", "inProgress": "En Progreso", "completed": "Completados", "atRisk": "En Riesgo", "notStarted": "No Iniciados", "projectsOverview": "Resumen de Proyectos", "viewAll": "<PERSON><PERSON>", "createNewProject": "Crear Nuevo Proyecto", "upcomingDeadlines": "Próximos <PERSON>", "calendarView": "Vista de Calendario", "noUpcomingDeadlines": "No se encontraron próximos plazos.", "team": "Equipo", "administrator": "Administrador", "moreMembersCount": "+{{count}} mi<PERSON><PERSON><PERSON> más", "comparedToLastMonth": "comparado con el mes pasado", "loadingError": "Error al cargar el espacio de trabajo. Por favor, inténtelo de nuevo.", "searchProjects": "Buscar proyectos...", "retry": "Reintentar", "notFound": "Espacio de trabajo no encontrado o no tienes acceso a él.", "notFoundTitle": "Espacio de trabajo no encontrado", "notFoundDescription": "El espacio de trabajo al que intentas acceder no existe o no tienes permiso para verlo.", "overallProgress": "Progreso General", "averageCompletion": "Finalización Promedio", "thisWeek": "<PERSON><PERSON> semana", "projectTimeline": "Cronología del Proyecto", "dueDate": "Vence", "selectWorkspaceForTimeline": "Necesitas seleccionar un espacio de trabajo para ver la cronología.", "goToWorkspaces": "<PERSON><PERSON> a Espacios de Trabajo", "backToWorkspace": "Volver al Espacio de Trabajo", "statistics": {"allProjects": "Todos los Proyectos", "inProgress": "En Progreso", "completed": "Completados", "atRisk": "En Riesgo"}}, "project": {"name": "Nombre del Proyecto", "namePlaceholder": "Ingrese el nombre del proyecto", "description": "Descripción del Proyecto", "descriptionPlaceholder": "Describa su proyecto", "startDate": "Fecha de Inicio", "endDate": "Fecha de Finalización", "status": "Estado", "priority": "Prioridad", "selectPriority": "Seleccione prioridad", "members": "Mi<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> Proye<PERSON>o", "createDescription": "Complete los detalles a continuación para crear un nuevo proyecto.", "creating": "Creando...", "edit": "Editar Proye<PERSON>o", "delete": "Eliminar Proyecto", "group": "Grupo", "personal": "Personal", "high": "Alta", "medium": "Media", "low": "Baja", "addTasks": "<PERSON><PERSON><PERSON> Proyecto", "errors": {"nameRequired": "El nombre del proyecto es obligatorio", "nameLength": "El nombre del proyecto debe tener al menos 3 caracteres"}, "toast": {"created": "Proyecto creado", "createdDescription": "Su nuevo proyecto ha sido creado con éxito.", "failed": "Error al crear el proyecto", "failedDescription": "Hubo un error al crear su proyecto. Por favor, inténtelo de nuevo."}}, "timeline": {"title": "Cronología", "visualize": "Visualice el cronograma del proyecto y realice un seguimiento del progreso", "addMilestone": "<PERSON><PERSON><PERSON>", "addProjectMilestone": "<PERSON><PERSON><PERSON> del Proyecto", "createMilestone": "Cree un hito para marcar fechas importantes en la cronología de su proyecto.", "milestoneTitle": "<PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON>", "description": "Descripción (opcional)", "projectLaunch": "ej., Lanzamiento del Proyecto", "milestoneDetails": "<PERSON><PERSON><PERSON> de<PERSON>les sobre este hito", "cancel": "<PERSON><PERSON><PERSON>", "createMilestoneButton": "<PERSON><PERSON><PERSON>", "previous": "Anterior", "next": "Siguient<PERSON>", "today": "Hoy", "filters": "<PERSON><PERSON><PERSON>", "status": "Estado", "allStatuses": "Todos los Estados", "completed": "Completado", "inProgress": "En Progreso", "notStarted": "No Iniciado", "atRisk": "En Riesgo", "assignee": "Asignado a", "allAssignees": "Todos los Asignados", "view": "Vista", "week": "Se<PERSON>", "month": "<PERSON><PERSON>", "quarter": "Trimestre", "loading": "Cargando cronología del proyecto...", "projectNotFound": "Proyecto no encontrado", "projectNotFoundDesc": "El proyecto que está buscando no existe o no tiene acceso a él.", "backToProjects": "Volver a Proyectos", "success": "Éxito", "milestoneCreated": "Hito creado con éxito", "error": "Error", "failedToCreate": "Error al crear el hito. Por favor, inténtelo de nuevo.", "failedToLoad": "Error al cargar los datos de la cronología. Por favor, inténtelo de nuevo.", "noProjectsMatchFilter": "No hay proyectos que coincidan con el filtro seleccionado", "noProjectsAvailable": "No hay proyectos disponibles todavía", "noTimelineItems": "No hay elementos de cronología disponibles para este proyecto"}}