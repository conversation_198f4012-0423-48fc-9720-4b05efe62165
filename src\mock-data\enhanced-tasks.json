[{"id": 201, "project_id": 2, "list_id": 4, "title": "Research competitor apps", "description": "Analyze top 5 competitor apps in the market to identify key features and opportunities", "status": "to_do", "priority": "medium", "assigned_to": 2, "position": 1, "due_date": "2023-05-10", "created_at": "2023-05-02T09:15:00Z", "updated_at": "2023-05-02T09:15:00Z"}, {"id": 202, "project_id": 2, "list_id": 4, "title": "Define app architecture", "description": "Create technical architecture document outlining app structure, technologies, and data flow", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-05-15", "created_at": "2023-05-02T10:30:00Z", "updated_at": "2023-05-02T10:30:00Z"}, {"id": 203, "project_id": 2, "list_id": 4, "title": "Plan user onboarding flow", "description": "Design the user registration and onboarding experience for new app users", "status": "to_do", "priority": "medium", "assigned_to": 2, "position": 3, "due_date": "2023-05-20", "created_at": "2023-05-03T11:45:00Z", "updated_at": "2023-05-03T11:45:00Z"}, {"id": 204, "project_id": 2, "list_id": 5, "title": "Create app wireframes", "description": "Design low-fidelity wireframes for all main screens of the mobile application", "status": "to_do", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-05-25", "created_at": "2023-05-05T09:00:00Z", "updated_at": "2023-05-05T09:00:00Z"}, {"id": 205, "project_id": 2, "list_id": 5, "title": "Setup development environment", "description": "Configure development environment with necessary SDKs, libraries, and tools for all team members", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-05-12", "created_at": "2023-05-05T10:15:00Z", "updated_at": "2023-05-05T10:15:00Z"}, {"id": 206, "project_id": 2, "list_id": 6, "title": "Design app logo and branding", "description": "Create logo, color scheme, and visual identity for the mobile application", "status": "in_progress", "priority": "medium", "assigned_to": 2, "position": 1, "due_date": "2023-05-18", "created_at": "2023-05-06T14:30:00Z", "updated_at": "2023-05-10T11:20:00Z"}, {"id": 207, "project_id": 2, "list_id": 6, "title": "Implement authentication system", "description": "Develop user authentication including login, registration, password reset, and social login options", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-05-30", "created_at": "2023-05-08T09:45:00Z", "updated_at": "2023-05-15T16:30:00Z"}, {"id": 208, "project_id": 2, "list_id": 7, "title": "Create app icon set", "description": "Design and export app icons in all required sizes for iOS and Android platforms", "status": "review", "priority": "low", "assigned_to": 2, "position": 1, "due_date": "2023-05-15", "created_at": "2023-05-05T13:20:00Z", "updated_at": "2023-05-12T10:15:00Z"}, {"id": 209, "project_id": 2, "list_id": 8, "title": "Project kickoff meeting", "description": "Initial team meeting to discuss project goals, timeline, and responsibilities", "status": "completed", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-05-03", "created_at": "2023-05-01T11:00:00Z", "updated_at": "2023-05-03T15:30:00Z"}, {"id": 210, "project_id": 2, "list_id": 8, "title": "Requirements gathering", "description": "Collect and document all client requirements for the mobile application", "status": "completed", "priority": "high", "assigned_to": 1, "position": 2, "due_date": "2023-05-08", "created_at": "2023-05-02T13:45:00Z", "updated_at": "2023-05-08T17:00:00Z"}, {"id": 211, "project_id": 2, "list_id": 7, "title": "User flow diagrams", "description": "Create comprehensive user flow diagrams for all main app functions", "status": "review", "priority": "medium", "assigned_to": 2, "position": 2, "due_date": "2023-05-22", "created_at": "2023-05-10T09:30:00Z", "updated_at": "2023-05-20T14:15:00Z"}, {"id": 212, "project_id": 2, "list_id": 6, "title": "Create UI component library", "description": "Design and implement reusable UI components for the mobile application", "status": "in_progress", "priority": "medium", "assigned_to": 2, "position": 3, "due_date": "2023-06-05", "created_at": "2023-05-15T11:20:00Z", "updated_at": "2023-05-22T09:45:00Z"}, {"id": 213, "project_id": 2, "list_id": 5, "title": "Design database schema", "description": "Create database schema for the application including all tables, relationships, and indexes", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-06-01", "created_at": "2023-05-18T10:30:00Z", "updated_at": "2023-05-18T10:30:00Z"}, {"id": 214, "project_id": 2, "list_id": 5, "title": "Implement API endpoints", "description": "Develop RESTful API endpoints for all app features", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 4, "due_date": "2023-06-10", "created_at": "2023-05-20T09:15:00Z", "updated_at": "2023-05-20T09:15:00Z"}, {"id": 215, "project_id": 2, "list_id": 4, "title": "Plan offline functionality", "description": "Design approach for app functionality when device is offline", "status": "to_do", "priority": "low", "assigned_to": 3, "position": 4, "due_date": "2023-06-15", "created_at": "2023-05-22T14:30:00Z", "updated_at": "2023-05-22T14:30:00Z"}, {"id": 216, "project_id": 2, "list_id": 4, "title": "Research push notification options", "description": "Evaluate different push notification services and implementation approaches", "status": "to_do", "priority": "medium", "assigned_to": 3, "position": 5, "due_date": "2023-06-05", "created_at": "2023-05-23T11:45:00Z", "updated_at": "2023-05-23T11:45:00Z"}, {"id": 301, "project_id": 3, "list_id": 9, "title": "Define campaign objectives", "description": "Establish clear marketing objectives, KPIs, and success metrics for the Q3 product launch campaign", "status": "completed", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-06-15", "created_at": "2023-06-10T13:00:00Z", "updated_at": "2023-06-15T16:30:00Z"}, {"id": 302, "project_id": 3, "list_id": 9, "title": "Identify target audience", "description": "Research and define primary and secondary target audiences for the product launch", "status": "completed", "priority": "high", "assigned_to": 2, "position": 2, "due_date": "2023-06-18", "created_at": "2023-06-11T09:15:00Z", "updated_at": "2023-06-17T14:45:00Z"}, {"id": 303, "project_id": 3, "list_id": 9, "title": "Develop marketing budget", "description": "Create detailed budget allocation for all campaign activities including advertising, content creation, and events", "status": "completed", "priority": "high", "assigned_to": 2, "position": 3, "due_date": "2023-06-20", "created_at": "2023-06-12T10:30:00Z", "updated_at": "2023-06-19T15:20:00Z"}, {"id": 304, "project_id": 3, "list_id": 9, "title": "Create campaign timeline", "description": "Develop detailed timeline with key milestones for the entire marketing campaign", "status": "completed", "priority": "medium", "assigned_to": 1, "position": 4, "due_date": "2023-06-22", "created_at": "2023-06-13T11:45:00Z", "updated_at": "2023-06-21T13:10:00Z"}, {"id": 305, "project_id": 3, "list_id": 10, "title": "Design campaign logo", "description": "Create distinctive logo and visual identity for the Q3 product launch campaign", "status": "completed", "priority": "medium", "assigned_to": 2, "position": 1, "due_date": "2023-06-25", "created_at": "2023-06-15T09:00:00Z", "updated_at": "2023-06-24T16:45:00Z"}, {"id": 306, "project_id": 3, "list_id": 10, "title": "Create social media graphics", "description": "Design social media graphics for Facebook, Instagram, Twitter, and LinkedIn following campaign style guide", "status": "in_progress", "priority": "high", "assigned_to": 2, "position": 2, "due_date": "2023-07-05", "created_at": "2023-06-20T10:15:00Z", "updated_at": "2023-06-30T14:30:00Z"}, {"id": 307, "project_id": 3, "list_id": 10, "title": "Write blog posts", "description": "Create a series of 5 blog posts highlighting different aspects of the new product", "status": "in_progress", "priority": "medium", "assigned_to": 1, "position": 3, "due_date": "2023-07-10", "created_at": "2023-06-22T11:30:00Z", "updated_at": "2023-07-01T15:45:00Z"}, {"id": 308, "project_id": 3, "list_id": 10, "title": "Produce product demo video", "description": "Create 2-minute product demonstration video showcasing key features and benefits", "status": "to_do", "priority": "high", "assigned_to": 2, "position": 4, "due_date": "2023-07-15", "created_at": "2023-06-25T13:45:00Z", "updated_at": "2023-06-25T13:45:00Z"}, {"id": 309, "project_id": 3, "list_id": 11, "title": "Review campaign strategy document", "description": "Final review and approval of comprehensive campaign strategy document", "status": "completed", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-06-23", "created_at": "2023-06-18T09:30:00Z", "updated_at": "2023-06-22T17:15:00Z"}, {"id": 310, "project_id": 3, "list_id": 11, "title": "Review press release draft", "description": "Review and provide feedback on press release draft for product launch", "status": "in_progress", "priority": "medium", "assigned_to": 2, "position": 2, "due_date": "2023-07-08", "created_at": "2023-06-30T10:45:00Z", "updated_at": "2023-07-03T14:20:00Z"}, {"id": 311, "project_id": 3, "list_id": 12, "title": "Schedule social media posts", "description": "Schedule first wave of social media posts across all platforms for product launch", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-07-20", "created_at": "2023-07-01T11:00:00Z", "updated_at": "2023-07-01T11:00:00Z"}, {"id": 312, "project_id": 3, "list_id": 12, "title": "Schedule email campaign", "description": "Set up and schedule email campaign sequence for product launch announcement", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 2, "due_date": "2023-07-25", "created_at": "2023-07-05T13:15:00Z", "updated_at": "2023-07-05T13:15:00Z"}, {"id": 313, "project_id": 3, "list_id": 13, "title": "Launch campaign website", "description": "Publish dedicated product launch landing page with all campaign materials", "status": "completed", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-06-30", "created_at": "2023-06-15T09:30:00Z", "updated_at": "2023-06-29T16:45:00Z"}, {"id": 314, "project_id": 3, "list_id": 13, "title": "Publish teaser content", "description": "Release teaser content across social media channels to build anticipation", "status": "completed", "priority": "medium", "assigned_to": 2, "position": 2, "due_date": "2023-07-01", "created_at": "2023-06-20T10:45:00Z", "updated_at": "2023-06-30T15:30:00Z"}, {"id": 401, "project_id": 4, "list_id": 14, "title": "Analyze current database structure", "description": "Document the current database schema, relationships, and data types", "status": "completed", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-07-05", "created_at": "2023-07-01T15:00:00Z", "updated_at": "2023-07-05T16:30:00Z"}, {"id": 402, "project_id": 4, "list_id": 14, "title": "Define cloud platform requirements", "description": "Determine storage, performance, and scaling requirements for the new cloud database", "status": "completed", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-07-08", "created_at": "2023-07-02T09:15:00Z", "updated_at": "2023-07-07T14:45:00Z"}, {"id": 403, "project_id": 4, "list_id": 14, "title": "Create migration strategy document", "description": "Develop comprehensive migration plan including timeline, resources, and risk mitigation", "status": "completed", "priority": "medium", "assigned_to": 3, "position": 3, "due_date": "2023-07-12", "created_at": "2023-07-03T10:30:00Z", "updated_at": "2023-07-11T15:20:00Z"}, {"id": 404, "project_id": 4, "list_id": 15, "title": "Design new database schema", "description": "Create optimized database schema for the cloud platform with improved structure", "status": "completed", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-07-15", "created_at": "2023-07-06T09:00:00Z", "updated_at": "2023-07-14T16:45:00Z"}, {"id": 405, "project_id": 4, "list_id": 15, "title": "Develop data extraction scripts", "description": "Create scripts to extract data from legacy database in appropriate format", "status": "completed", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-07-20", "created_at": "2023-07-10T11:15:00Z", "updated_at": "2023-07-19T14:30:00Z"}, {"id": 406, "project_id": 4, "list_id": 15, "title": "Develop data transformation scripts", "description": "Create scripts to transform extracted data to match new schema structure", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-07-25", "created_at": "2023-07-15T10:30:00Z", "updated_at": "2023-07-22T15:45:00Z"}, {"id": 407, "project_id": 4, "list_id": 15, "title": "Develop data loading scripts", "description": "Create scripts to load transformed data into the new cloud database", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 4, "due_date": "2023-07-30", "created_at": "2023-07-20T09:45:00Z", "updated_at": "2023-07-25T16:30:00Z"}, {"id": 408, "project_id": 4, "list_id": 16, "title": "Create test dataset", "description": "Prepare representative test dataset for migration validation", "status": "in_progress", "priority": "medium", "assigned_to": 3, "position": 1, "due_date": "2023-07-28", "created_at": "2023-07-22T11:00:00Z", "updated_at": "2023-07-25T14:15:00Z"}, {"id": 409, "project_id": 4, "list_id": 16, "title": "Develop validation tests", "description": "Create automated tests to verify data integrity after migration", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-08-02", "created_at": "2023-07-23T10:15:00Z", "updated_at": "2023-07-23T10:15:00Z"}, {"id": 410, "project_id": 4, "list_id": 16, "title": "Perform test migration", "description": "Execute complete migration process in test environment", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-08-05", "created_at": "2023-07-24T09:30:00Z", "updated_at": "2023-07-24T09:30:00Z"}, {"id": 411, "project_id": 4, "list_id": 17, "title": "Create deployment plan", "description": "Develop detailed deployment plan including downtime schedule and rollback procedures", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-08-08", "created_at": "2023-07-25T11:45:00Z", "updated_at": "2023-07-25T11:45:00Z"}, {"id": 412, "project_id": 4, "list_id": 17, "title": "Schedule production migration", "description": "Coordinate with stakeholders to schedule production migration with minimal disruption", "status": "to_do", "priority": "medium", "assigned_to": 3, "position": 2, "due_date": "2023-08-10", "created_at": "2023-07-26T10:00:00Z", "updated_at": "2023-07-26T10:00:00Z"}, {"id": 413, "project_id": 4, "list_id": 17, "title": "Prepare post-migration verification", "description": "Create checklist and procedures for verifying successful migration in production", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-08-12", "created_at": "2023-07-27T09:15:00Z", "updated_at": "2023-07-27T09:15:00Z"}, {"id": 601, "project_id": 6, "list_id": 23, "title": "Define content types and taxonomies", "description": "Create comprehensive documentation of all content types, fields, and taxonomies required for the CMS", "status": "completed", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-07-25", "created_at": "2023-07-16T09:30:00Z", "updated_at": "2023-07-24T15:45:00Z"}, {"id": 602, "project_id": 6, "list_id": 24, "title": "Design content editor interface", "description": "Create wireframes and mockups for the content editing interface with focus on usability", "status": "in_progress", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-08-10", "created_at": "2023-07-26T10:15:00Z", "updated_at": "2023-08-01T14:30:00Z"}, {"id": 603, "project_id": 6, "list_id": 25, "title": "Implement user authentication system", "description": "Develop secure authentication system with role-based permissions for CMS users", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-08-25", "created_at": "2023-08-01T11:00:00Z", "updated_at": "2023-08-01T11:00:00Z"}, {"id": 701, "project_id": 7, "list_id": 27, "title": "Literature review on machine learning applications", "description": "Conduct comprehensive literature review of recent papers on machine learning applications in target domain", "status": "completed", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-06-15", "created_at": "2023-06-01T13:00:00Z", "updated_at": "2023-06-14T16:30:00Z"}, {"id": 702, "project_id": 7, "list_id": 27, "title": "Identify research questions", "description": "Define specific research questions and hypotheses to be addressed in the project", "status": "completed", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-06-20", "created_at": "2023-06-05T09:15:00Z", "updated_at": "2023-06-19T14:45:00Z"}, {"id": 703, "project_id": 7, "list_id": 27, "title": "Explore existing models and frameworks", "description": "Analyze existing machine learning models and frameworks applicable to our research questions", "status": "completed", "priority": "medium", "assigned_to": 3, "position": 3, "due_date": "2023-06-25", "created_at": "2023-06-10T10:30:00Z", "updated_at": "2023-06-24T15:20:00Z"}, {"id": 704, "project_id": 7, "list_id": 27, "title": "Define evaluation metrics", "description": "Establish appropriate metrics for evaluating model performance and research outcomes", "status": "completed", "priority": "high", "assigned_to": 3, "position": 4, "due_date": "2023-06-30", "created_at": "2023-06-15T11:45:00Z", "updated_at": "2023-06-29T13:10:00Z"}, {"id": 705, "project_id": 7, "list_id": 28, "title": "Identify data sources", "description": "Research and document potential data sources for the project", "status": "completed", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-07-05", "created_at": "2023-06-20T09:00:00Z", "updated_at": "2023-07-04T16:45:00Z"}, {"id": 706, "project_id": 7, "list_id": 28, "title": "Develop data collection pipeline", "description": "Create automated pipeline for collecting and storing data from identified sources", "status": "completed", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-07-15", "created_at": "2023-07-01T10:15:00Z", "updated_at": "2023-07-14T14:30:00Z"}, {"id": 707, "project_id": 7, "list_id": 28, "title": "Data cleaning and preprocessing", "description": "Develop scripts for cleaning, normalizing, and preprocessing collected data", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-07-25", "created_at": "2023-07-10T11:30:00Z", "updated_at": "2023-07-20T15:45:00Z"}, {"id": 708, "project_id": 7, "list_id": 28, "title": "Feature engineering", "description": "Identify and extract relevant features from preprocessed data", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 4, "due_date": "2023-08-05", "created_at": "2023-07-20T13:45:00Z", "updated_at": "2023-07-30T16:30:00Z"}, {"id": 709, "project_id": 7, "list_id": 28, "title": "Data augmentation", "description": "Implement data augmentation techniques to enhance training dataset", "status": "to_do", "priority": "medium", "assigned_to": 3, "position": 5, "due_date": "2023-08-15", "created_at": "2023-07-25T09:30:00Z", "updated_at": "2023-07-25T09:30:00Z"}, {"id": 710, "project_id": 7, "list_id": 29, "title": "Baseline model implementation", "description": "Implement baseline machine learning models for initial benchmarking", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-08-10", "created_at": "2023-07-15T09:00:00Z", "updated_at": "2023-08-01T14:15:00Z"}, {"id": 711, "project_id": 7, "list_id": 29, "title": "Advanced model architecture design", "description": "Design advanced neural network architectures for improved performance", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-08-25", "created_at": "2023-07-25T10:15:00Z", "updated_at": "2023-07-25T10:15:00Z"}, {"id": 712, "project_id": 7, "list_id": 29, "title": "Hyperparameter optimization", "description": "Implement automated hyperparameter tuning for model optimization", "status": "to_do", "priority": "medium", "assigned_to": 3, "position": 3, "due_date": "2023-09-05", "created_at": "2023-08-01T11:30:00Z", "updated_at": "2023-08-01T11:30:00Z"}, {"id": 713, "project_id": 7, "list_id": 29, "title": "Model training pipeline", "description": "Develop automated pipeline for model training, validation, and checkpointing", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 4, "due_date": "2023-09-15", "created_at": "2023-08-05T09:45:00Z", "updated_at": "2023-08-05T09:45:00Z"}, {"id": 714, "project_id": 7, "list_id": 29, "title": "Transfer learning implementation", "description": "Apply transfer learning techniques using pre-trained models", "status": "to_do", "priority": "medium", "assigned_to": 3, "position": 5, "due_date": "2023-09-25", "created_at": "2023-08-10T10:00:00Z", "updated_at": "2023-08-10T10:00:00Z"}, {"id": 715, "project_id": 7, "list_id": 30, "title": "Performance evaluation framework", "description": "Develop comprehensive framework for evaluating model performance", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-10-05", "created_at": "2023-08-15T11:15:00Z", "updated_at": "2023-08-15T11:15:00Z"}, {"id": 716, "project_id": 7, "list_id": 30, "title": "Comparative analysis", "description": "Conduct comparative analysis of different model architectures and approaches", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-10-15", "created_at": "2023-08-20T09:30:00Z", "updated_at": "2023-08-20T09:30:00Z"}, {"id": 717, "project_id": 7, "list_id": 30, "title": "Error analysis", "description": "Perform detailed error analysis to identify model weaknesses and improvement opportunities", "status": "to_do", "priority": "medium", "assigned_to": 3, "position": 3, "due_date": "2023-10-25", "created_at": "2023-08-25T10:45:00Z", "updated_at": "2023-08-25T10:45:00Z"}, {"id": 718, "project_id": 7, "list_id": 30, "title": "Real-world testing", "description": "Test models on real-world data and scenarios to validate performance", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 4, "due_date": "2023-11-05", "created_at": "2023-08-30T11:00:00Z", "updated_at": "2023-08-30T11:00:00Z"}, {"id": 719, "project_id": 7, "list_id": 30, "title": "Research paper preparation", "description": "Prepare research paper documenting methodology, results, and findings", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 5, "due_date": "2023-11-20", "created_at": "2023-09-05T09:15:00Z", "updated_at": "2023-09-05T09:15:00Z"}, {"id": 801, "project_id": 8, "list_id": 31, "title": "Define game concept", "description": "Create detailed game concept document outlining core gameplay, target audience, and unique selling points", "status": "completed", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-05-20", "created_at": "2023-05-15T15:00:00Z", "updated_at": "2023-05-19T16:30:00Z"}, {"id": 802, "project_id": 8, "list_id": 31, "title": "Create game story and characters", "description": "Develop storyline, character backgrounds, and narrative elements for the game", "status": "completed", "priority": "medium", "assigned_to": 1, "position": 2, "due_date": "2023-05-25", "created_at": "2023-05-16T09:15:00Z", "updated_at": "2023-05-24T14:45:00Z"}, {"id": 803, "project_id": 8, "list_id": 31, "title": "Define game mechanics", "description": "Document core gameplay mechanics, rules, and player interactions", "status": "completed", "priority": "high", "assigned_to": 1, "position": 3, "due_date": "2023-05-30", "created_at": "2023-05-17T10:30:00Z", "updated_at": "2023-05-29T15:20:00Z"}, {"id": 804, "project_id": 8, "list_id": 32, "title": "Create character designs", "description": "Design main character and supporting character visuals with multiple animations", "status": "completed", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-06-10", "created_at": "2023-05-25T11:45:00Z", "updated_at": "2023-06-09T13:10:00Z"}, {"id": 805, "project_id": 8, "list_id": 32, "title": "Design game environments", "description": "Create visual designs for all game levels and environments", "status": "completed", "priority": "medium", "assigned_to": 2, "position": 2, "due_date": "2023-06-20", "created_at": "2023-06-01T09:00:00Z", "updated_at": "2023-06-19T16:45:00Z"}, {"id": 806, "project_id": 8, "list_id": 32, "title": "Create UI/UX design", "description": "Design user interface elements including menus, HUD, and interactive components", "status": "completed", "priority": "high", "assigned_to": 2, "position": 3, "due_date": "2023-06-30", "created_at": "2023-06-10T10:15:00Z", "updated_at": "2023-06-28T14:30:00Z"}, {"id": 807, "project_id": 8, "list_id": 33, "title": "Implement core gameplay mechanics", "description": "Develop and implement fundamental gameplay systems and mechanics", "status": "completed", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-07-15", "created_at": "2023-06-20T11:30:00Z", "updated_at": "2023-07-14T15:45:00Z"}, {"id": 808, "project_id": 8, "list_id": 33, "title": "Implement character controls", "description": "Develop character movement, animations, and player input handling", "status": "completed", "priority": "high", "assigned_to": 1, "position": 2, "due_date": "2023-07-25", "created_at": "2023-07-01T13:45:00Z", "updated_at": "2023-07-24T16:30:00Z"}, {"id": 809, "project_id": 8, "list_id": 33, "title": "Implement game levels", "description": "Build all game levels with environment assets, obstacles, and interactive elements", "status": "completed", "priority": "high", "assigned_to": 1, "position": 3, "due_date": "2023-08-05", "created_at": "2023-07-15T09:30:00Z", "updated_at": "2023-08-04T14:15:00Z"}, {"id": 810, "project_id": 8, "list_id": 33, "title": "Implement UI systems", "description": "Develop menu systems, HUD, and other user interface components", "status": "completed", "priority": "medium", "assigned_to": 1, "position": 4, "due_date": "2023-08-10", "created_at": "2023-07-25T10:45:00Z", "updated_at": "2023-08-09T15:30:00Z"}, {"id": 811, "project_id": 8, "list_id": 34, "title": "Perform functionality testing", "description": "Test all game features and mechanics for bugs and issues", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-08-20", "created_at": "2023-08-10T11:00:00Z", "updated_at": "2023-08-15T09:15:00Z"}, {"id": 812, "project_id": 8, "list_id": 34, "title": "Perform performance testing", "description": "Test game performance on various devices and optimize for smooth gameplay", "status": "in_progress", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-08-25", "created_at": "2023-08-15T10:15:00Z", "updated_at": "2023-08-20T14:30:00Z"}, {"id": 813, "project_id": 8, "list_id": 34, "title": "Conduct user testing", "description": "Organize playtesting sessions with target audience to gather feedback", "status": "to_do", "priority": "medium", "assigned_to": 1, "position": 3, "due_date": "2023-08-30", "created_at": "2023-08-20T11:30:00Z", "updated_at": "2023-08-20T11:30:00Z"}, {"id": 814, "project_id": 8, "list_id": 35, "title": "Prepare app store assets", "description": "Create screenshots, videos, and promotional materials for app store listings", "status": "to_do", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-09-01", "created_at": "2023-08-20T09:00:00Z", "updated_at": "2023-08-20T09:00:00Z"}, {"id": 815, "project_id": 8, "list_id": 35, "title": "Implement analytics and crash reporting", "description": "Integrate analytics and crash reporting tools for post-launch monitoring", "status": "to_do", "priority": "medium", "assigned_to": 3, "position": 2, "due_date": "2023-09-05", "created_at": "2023-08-25T10:15:00Z", "updated_at": "2023-08-25T10:15:00Z"}, {"id": 816, "project_id": 8, "list_id": 35, "title": "Prepare for app submission", "description": "Complete all requirements for iOS and Android app store submissions", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 3, "due_date": "2023-09-08", "created_at": "2023-08-30T11:30:00Z", "updated_at": "2023-08-30T11:30:00Z"}, {"id": 501, "project_id": 5, "list_id": 18, "title": "Conduct market research", "description": "Research current e-commerce trends, competitors, and target audience needs", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-08-10", "created_at": "2023-08-01T11:00:00Z", "updated_at": "2023-08-01T11:00:00Z"}, {"id": 502, "project_id": 5, "list_id": 18, "title": "Define product catalog structure", "description": "Create detailed structure for product categories, attributes, and relationships", "status": "to_do", "priority": "high", "assigned_to": 2, "position": 2, "due_date": "2023-08-15", "created_at": "2023-08-02T09:15:00Z", "updated_at": "2023-08-02T09:15:00Z"}, {"id": 503, "project_id": 5, "list_id": 18, "title": "Define payment processing requirements", "description": "Research and select payment gateways, define checkout flow and security requirements", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-08-18", "created_at": "2023-08-03T10:30:00Z", "updated_at": "2023-08-03T10:30:00Z"}, {"id": 504, "project_id": 5, "list_id": 18, "title": "Define shipping and fulfillment requirements", "description": "Document shipping options, carrier integrations, and order fulfillment workflow", "status": "to_do", "priority": "medium", "assigned_to": 1, "position": 4, "due_date": "2023-08-20", "created_at": "2023-08-04T11:45:00Z", "updated_at": "2023-08-04T11:45:00Z"}, {"id": 505, "project_id": 5, "list_id": 19, "title": "Create user journey maps", "description": "Map out complete user journeys from discovery to purchase and post-purchase", "status": "to_do", "priority": "high", "assigned_to": 2, "position": 1, "due_date": "2023-08-25", "created_at": "2023-08-05T09:00:00Z", "updated_at": "2023-08-05T09:00:00Z"}, {"id": 506, "project_id": 5, "list_id": 19, "title": "Design product page templates", "description": "Create wireframes and mockups for product listing and detail pages", "status": "to_do", "priority": "high", "assigned_to": 2, "position": 2, "due_date": "2023-08-30", "created_at": "2023-08-06T10:15:00Z", "updated_at": "2023-08-06T10:15:00Z"}, {"id": 507, "project_id": 5, "list_id": 19, "title": "Design checkout flow", "description": "Create wireframes and mockups for multi-step checkout process", "status": "to_do", "priority": "high", "assigned_to": 2, "position": 3, "due_date": "2023-09-05", "created_at": "2023-08-07T11:30:00Z", "updated_at": "2023-08-07T11:30:00Z"}, {"id": 508, "project_id": 5, "list_id": 19, "title": "Create design system", "description": "Develop comprehensive design system with components, styles, and guidelines", "status": "to_do", "priority": "medium", "assigned_to": 2, "position": 4, "due_date": "2023-09-10", "created_at": "2023-08-08T09:45:00Z", "updated_at": "2023-08-08T09:45:00Z"}, {"id": 509, "project_id": 5, "list_id": 20, "title": "Set up development environment", "description": "Configure development, staging, and production environments with CI/CD pipeline", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 1, "due_date": "2023-09-15", "created_at": "2023-08-10T10:00:00Z", "updated_at": "2023-08-10T10:00:00Z"}, {"id": 510, "project_id": 5, "list_id": 20, "title": "Implement product catalog", "description": "Develop database schema and APIs for product catalog management", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-09-25", "created_at": "2023-08-12T11:15:00Z", "updated_at": "2023-08-12T11:15:00Z"}, {"id": 511, "project_id": 5, "list_id": 20, "title": "Implement user authentication", "description": "Develop secure user authentication and account management features", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-10-05", "created_at": "2023-08-15T09:30:00Z", "updated_at": "2023-08-15T09:30:00Z"}, {"id": 512, "project_id": 5, "list_id": 20, "title": "Implement shopping cart", "description": "Develop shopping cart functionality with persistent storage", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 4, "due_date": "2023-10-15", "created_at": "2023-08-18T10:45:00Z", "updated_at": "2023-08-18T10:45:00Z"}, {"id": 513, "project_id": 5, "list_id": 21, "title": "Create test plan", "description": "Develop comprehensive test plan covering all platform features and integrations", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-10-25", "created_at": "2023-08-20T11:00:00Z", "updated_at": "2023-08-20T11:00:00Z"}, {"id": 514, "project_id": 5, "list_id": 21, "title": "Implement automated tests", "description": "Develop automated test suite for critical platform functionality", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 2, "due_date": "2023-11-05", "created_at": "2023-08-22T09:15:00Z", "updated_at": "2023-08-22T09:15:00Z"}, {"id": 515, "project_id": 5, "list_id": 21, "title": "Conduct user acceptance testing", "description": "Organize UAT sessions with stakeholders and potential users", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 3, "due_date": "2023-11-15", "created_at": "2023-08-25T10:30:00Z", "updated_at": "2023-08-25T10:30:00Z"}, {"id": 516, "project_id": 5, "list_id": 22, "title": "Create deployment plan", "description": "Develop detailed deployment strategy including rollback procedures", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-11-25", "created_at": "2023-08-28T11:45:00Z", "updated_at": "2023-08-28T11:45:00Z"}, {"id": 517, "project_id": 5, "list_id": 22, "title": "Prepare marketing materials", "description": "Create launch announcement and marketing materials", "status": "to_do", "priority": "medium", "assigned_to": 2, "position": 2, "due_date": "2023-12-01", "created_at": "2023-08-30T09:00:00Z", "updated_at": "2023-08-30T09:00:00Z"}, {"id": 518, "project_id": 5, "list_id": 22, "title": "Conduct performance testing", "description": "Test platform performance under expected load conditions", "status": "to_do", "priority": "high", "assigned_to": 3, "position": 3, "due_date": "2023-12-05", "created_at": "2023-09-01T10:15:00Z", "updated_at": "2023-09-01T10:15:00Z"}, {"id": 1, "project_id": 1, "list_id": 3, "title": "Design wireframes", "description": "Create wireframes for all main pages of the website including homepage, about, portfolio, and contact sections", "status": "completed", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-05-01", "created_at": "2023-04-16T09:30:00Z", "updated_at": "2023-04-25T14:20:00Z"}, {"id": 2, "project_id": 1, "list_id": 2, "title": "Implement homepage", "description": "Code the homepage based on approved wireframes with responsive design and modern animations", "status": "in_progress", "priority": "high", "assigned_to": 1, "position": 1, "due_date": "2023-05-15", "created_at": "2023-04-26T10:15:00Z", "updated_at": "2023-05-05T11:30:00Z"}, {"id": 3, "project_id": 1, "list_id": 1, "title": "Create portfolio section", "description": "Design and implement the portfolio showcase section with filtering and detailed project views", "status": "to_do", "priority": "medium", "assigned_to": 1, "position": 1, "due_date": "2023-06-01", "created_at": "2023-04-26T10:20:00Z", "updated_at": "2023-04-26T10:20:00Z"}, {"id": 101, "project_id": 1, "list_id": 1, "title": "Implement contact form", "description": "Create a contact form with validation and email notification functionality", "status": "to_do", "priority": "high", "assigned_to": 1, "position": 2, "due_date": "2023-06-05", "created_at": "2023-04-28T11:20:00Z", "updated_at": "2023-04-28T11:20:00Z"}, {"id": 102, "project_id": 1, "list_id": 1, "title": "Add blog section", "description": "Design and implement a blog section with categories and tags", "status": "to_do", "priority": "low", "assigned_to": 1, "position": 3, "due_date": "2023-06-15", "created_at": "2023-04-29T09:15:00Z", "updated_at": "2023-04-29T09:15:00Z"}, {"id": 103, "project_id": 1, "list_id": 1, "title": "SEO optimization", "description": "Implement SEO best practices including meta tags, sitemap, and structured data", "status": "to_do", "priority": "medium", "assigned_to": 1, "position": 4, "due_date": "2023-06-10", "created_at": "2023-04-30T14:20:00Z", "updated_at": "2023-04-30T14:20:00Z"}, {"id": 104, "project_id": 1, "list_id": 2, "title": "Implement about page", "description": "Create the about page with personal information, skills, and experience sections", "status": "in_progress", "priority": "medium", "assigned_to": 1, "position": 2, "due_date": "2023-05-20", "created_at": "2023-05-01T10:30:00Z", "updated_at": "2023-05-10T09:45:00Z"}, {"id": 105, "project_id": 1, "list_id": 2, "title": "Create responsive navigation", "description": "Implement a responsive navigation menu that works well on all device sizes", "status": "in_progress", "priority": "high", "assigned_to": 1, "position": 3, "due_date": "2023-05-12", "created_at": "2023-05-02T11:15:00Z", "updated_at": "2023-05-08T16:20:00Z"}, {"id": 106, "project_id": 1, "list_id": 3, "title": "Design logo", "description": "Create a personal brand logo for the website", "status": "completed", "priority": "medium", "assigned_to": 1, "position": 2, "due_date": "2023-04-25", "created_at": "2023-04-10T09:00:00Z", "updated_at": "2023-04-22T15:30:00Z"}, {"id": 107, "project_id": 1, "list_id": 3, "title": "Choose color scheme", "description": "Select a cohesive color palette for the website that reflects personal brand", "status": "completed", "priority": "low", "assigned_to": 1, "position": 3, "due_date": "2023-04-20", "created_at": "2023-04-05T10:45:00Z", "updated_at": "2023-04-18T11:20:00Z"}]