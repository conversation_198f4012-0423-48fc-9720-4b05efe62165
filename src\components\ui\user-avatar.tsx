import React from 'react';
import { User } from '@/entities/User';
import { User as UserIcon } from 'lucide-react';
import { getFullImageUrl } from '@/utils/imageUtils';
import { cn } from '@/lib/utils';
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export type UserStatus = 'online' | 'offline' | 'idle' | 'busy' | 'away' | 'in-meeting';

interface UserAvatarProps {
  user: User;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  showStatus?: boolean;
  status?: UserStatus;
  showTooltip?: boolean;
  className?: string;
  fallbackClassName?: string;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'md',
  showStatus = true,
  status,
  showTooltip = true,
  className,
  fallbackClassName,
}) => {
  // Size classes for the avatar
  const sizeClasses = {
    xs: {
      container: "h-6 w-6",
      indicator: "h-1.5 w-1.5",
      text: "text-xs",
    },
    sm: {
      container: "h-8 w-8",
      indicator: "h-2 w-2",
      text: "text-sm",
    },
    md: {
      container: "h-10 w-10",
      indicator: "h-2.5 w-2.5",
      text: "text-sm",
    },
    lg: {
      container: "h-12 w-12",
      indicator: "h-3 w-3",
      text: "text-base",
    },
    xl: {
      container: "h-16 w-16",
      indicator: "h-3.5 w-3.5",
      text: "text-base",
    },
  };

  // Get the appropriate size classes
  const { container, indicator, text } = sizeClasses[size];

  // If status is not provided, generate a random one for demo purposes
  const userStatus = status || getRandomStatus(user.id);

  // Status indicator colors
  const statusColors = {
    online: 'bg-success',
    offline: 'bg-muted-foreground',
    idle: 'bg-amber-400',
    busy: 'bg-destructive',
    away: 'bg-orange-400',
    'in-meeting': 'bg-purple-500',
  };

  // Get user initials for the avatar fallback
  const getUserInitials = (user: User): string => {
    if (user.first_name && user.last_name) {
      return `${user.first_name[0]}${user.last_name[0]}`;
    }
    if (user.name) {
      const nameParts = user.name.split(' ');
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[1][0]}`;
      }
      return nameParts[0][0];
    }
    return 'U';
  };

  // Random status for demo purposes
  function getRandomStatus(userId: number): UserStatus {
    const statuses: UserStatus[] = ['online', 'offline', 'idle', 'busy', 'away', 'in-meeting'];
    // Use the user ID to deterministically select a status for demo purposes
    return statuses[userId % statuses.length];
  }

  const avatar = (
    <div className="relative">
      <Avatar className={cn(container, className)}>
        <AvatarImage
          src={user.profile_picture ? getFullImageUrl(user.profile_picture) : undefined}
          alt={user.name || `${user.first_name} ${user.last_name}`}
        />
        <AvatarFallback className={cn("bg-primary/10 text-primary dark:bg-white/90 dark:text-primary", fallbackClassName)}>
          {getUserInitials(user)}
        </AvatarFallback>
      </Avatar>

      {showStatus && (
        <div
          className={cn(
            "absolute bottom-0 right-0 rounded-full border-2 border-background",
            indicator,
            statusColors[userStatus]
          )}
        />
      )}
    </div>
  );

  // Get the display name
  const displayName = user.name || `${user.first_name} ${user.last_name}`;

  if (showTooltip) {
    return (
      <TooltipProvider delayDuration={300}>
        <Tooltip>
          <TooltipTrigger asChild>
            {avatar}
          </TooltipTrigger>
          <TooltipContent side="bottom" className={text}>
            <div className="flex flex-col">
              <span className="font-medium">{displayName}</span>
              {showStatus && (
                <span className="text-xs text-muted-foreground capitalize">{userStatus}</span>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return avatar;
};

export default UserAvatar;
