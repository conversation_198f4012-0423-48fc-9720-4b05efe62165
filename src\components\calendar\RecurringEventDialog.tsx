import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { UpdateType, DeleteType } from '@/types/calendar';

interface RecurringEventDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  actionType: 'update' | 'delete';
  onAction: (type: UpdateType | DeleteType) => void;
}

const RecurringEventDialog: React.FC<RecurringEventDialogProps> = ({
  isOpen,
  onClose,
  title,
  description,
  actionType,
  onAction,
}) => {
  const handleAction = (type: UpdateType | DeleteType) => {
    onAction(type);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-3 py-4">
          <Button
            variant="outline"
            className="justify-start text-left"
            onClick={() => handleAction('this')}
          >
            <div className="flex flex-col items-start">
              <span className="font-medium">
                {actionType === 'update' ? 'This event only' : 'This occurrence only'}
              </span>
              <span className="text-xs text-muted-foreground">
                {actionType === 'update'
                  ? 'Changes will apply only to this specific occurrence'
                  : 'Only this occurrence will be deleted'}
              </span>
            </div>
          </Button>

          <Button
            variant="outline"
            className="justify-start text-left"
            onClick={() => handleAction('future')}
          >
            <div className="flex flex-col items-start">
              <span className="font-medium">
                {actionType === 'update' ? 'This and future events' : 'This and future occurrences'}
              </span>
              <span className="text-xs text-muted-foreground">
                {actionType === 'update'
                  ? 'Changes will apply to this and all future occurrences'
                  : 'This and all future occurrences will be deleted'}
              </span>
            </div>
          </Button>

          <Button
            variant="outline"
            className="justify-start text-left"
            onClick={() => handleAction('all')}
          >
            <div className="flex flex-col items-start">
              <span className="font-medium">
                {actionType === 'update' ? 'All events in series' : 'All occurrences'}
              </span>
              <span className="text-xs text-muted-foreground">
                {actionType === 'update'
                  ? 'Changes will apply to all occurrences in this series'
                  : 'The entire recurring event series will be deleted'}
              </span>
            </div>
          </Button>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RecurringEventDialog;
