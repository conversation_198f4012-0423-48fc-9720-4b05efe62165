
import api from './api';
import { Task } from './tasksApi';
import { toast } from "@/hooks/use-toast";

// List types
export interface List {
  id: number;
  project_id: number;
  title: string;
  description?: string;
  position: number;
  background?: string;
  tasks?: Task[];
  created_at?: string;
  updated_at?: string;
}

// Get all lists for a project
export const getProjectLists = async (projectId: number) => {
  try {
    const response = await api.get(`/projects/${projectId}/lists`);
    return response.data;
  } catch (error) {
    console.error('Error fetching project lists:', error);
    toast({
      title: "Failed to fetch lists",
      description: "Could not load the lists for this project",
      variant: "destructive"
    });
    throw error;
  }
};

// Create a new list
export const createList = async (listData: Partial<List>) => {
  try {
    const response = await api.post('/lists', listData);
    return response.data;
  } catch (error) {
    console.error('Error creating list:', error);
    toast({
      title: "Failed to create list",
      description: "Could not create the new list",
      variant: "destructive"
    });
    throw error;
  }
};

// Update a list
export const updateList = async (id: number, listData: Partial<List>) => {
  try {
    const response = await api.put(`/lists/${id}`, listData);
    return response.data;
  } catch (error) {
    console.error('Error updating list:', error);
    toast({
      title: "Failed to update list",
      description: "Could not update the list",
      variant: "destructive"
    });
    throw error;
  }
};

// Delete a list
export const deleteList = async (id: number) => {
  try {
    const response = await api.delete(`/lists/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting list:', error);
    toast({
      title: "Failed to delete list",
      description: "Could not delete the list",
      variant: "destructive"
    });
    throw error;
  }
};

// Reorder lists
export const reorderLists = async (projectId: number, listIds: number[]) => {
  try {
    const response = await api.post(`/projects/${projectId}/reorder-lists`, {
      list_ids: listIds
    });
    return response.data;
  } catch (error) {
    console.error('Error reordering lists:', error);
    toast({
      title: "Failed to reorder lists",
      description: "Could not update the list order",
      variant: "destructive"
    });
    throw error;
  }
};
