
import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';
import { User } from '@/entities/User';

// Chat types
export interface Chat {
  id: number;
  user_id: number;
  task_id: number;
  message: string;
  image?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  user?: User;
  mentionedUsers?: User[];
}

// Get chats for a specific task
export const getTaskChats = async (taskId: number) => {
  try {
    const response = await api.get('/chats', {
      params: { task_id: taskId }
    });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch chats",
      "Could not load chat messages"
    );
  }
};

// Create a new chat message
export const createChat = async (chatData: {
  task_id: number;
  message: string;
  image?: File;
  description?: string;
}) => {
  try {
    // If there's an image, use FormData
    if (chatData.image) {
      const formData = new FormData();
      formData.append('task_id', chatData.task_id.toString());
      formData.append('message', chatData.message);
      formData.append('image', chatData.image);

      if (chatData.description) {
        formData.append('description', chatData.description);
      }

      const response = await api.post('/chats', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } else {
      // Regular JSON request if no image
      const response = await api.post('/chats', chatData);
      return response.data;
    }
  } catch (error) {
    return handleApiError(
      error,
      "Failed to send message",
      "Could not send your message"
    );
  }
};

// Delete a chat message
export const deleteChat = async (id: number) => {
  try {
    const response = await api.delete(`/chats/${id}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to delete message",
      "Could not delete the message"
    );
  }
};
