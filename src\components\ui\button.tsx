import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 dark:shadow-sm dark:shadow-primary/20 dark:hover:shadow-primary/30",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 dark:shadow-sm dark:shadow-destructive/20 dark:hover:shadow-destructive/30",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground dark:border-border/70 dark:hover:border-border dark:hover:bg-accent/80",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 dark:bg-secondary/90 dark:text-secondary-foreground dark:hover:bg-secondary/70",
        ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/80 dark:hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline dark:text-primary-foreground dark:hover:text-primary-foreground/90",
        // Status variants
        success: "bg-success text-success-foreground hover:bg-success/90 dark:shadow-sm dark:shadow-success/20 dark:hover:shadow-success/30",
        warning: "bg-warning text-warning-foreground hover:bg-warning/90 dark:shadow-sm dark:shadow-warning/20 dark:hover:shadow-warning/30",
        info: "bg-info text-info-foreground hover:bg-info/90 dark:shadow-sm dark:shadow-info/20 dark:hover:shadow-info/30",
        // Subtle status variants
        "success-subtle": "bg-success/20 text-success dark:bg-success/30 dark:text-success-foreground hover:bg-success/30 dark:hover:bg-success/40",
        "warning-subtle": "bg-warning/20 text-warning dark:bg-warning/30 dark:text-warning-foreground hover:bg-warning/30 dark:hover:bg-warning/40",
        "destructive-subtle": "bg-destructive/20 text-destructive dark:bg-destructive/30 dark:text-destructive-foreground hover:bg-destructive/30 dark:hover:bg-destructive/40",
        "info-subtle": "bg-info/20 text-info dark:bg-info/30 dark:text-info-foreground hover:bg-info/30 dark:hover:bg-info/40",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
        xs: "h-7 rounded-md px-2 text-xs",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
