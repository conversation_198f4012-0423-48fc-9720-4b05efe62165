import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';

// Calendar event types
export interface CalendarEvent {
  id: number | string;
  title: string;
  description?: string;
  start_date: string;
  end_date?: string;
  type: 'task' | 'deadline' | 'meeting' | 'milestone';
  status?: 'not-started' | 'in-progress' | 'completed';
  priority?: 'low' | 'medium' | 'high';
  project_id?: number;
  project?: any;
  assignees?: Array<{
    id: number;
    name: string;
    avatar?: string;
  }>;
  is_task?: boolean;
}

// Get all calendar events for the user
export const getAllEvents = async (params?: any) => {
  try {
    const response = await api.get('/calendar-events', { params });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch calendar events",
      "Could not load calendar data"
    );
  }
};

// Get calendar events for a specific project
export const getProjectEvents = async (projectId: number) => {
  try {
    const response = await api.get('/calendar-events', {
      params: { project_id: projectId }
    });

    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch project calendar events",
      "Could not load project calendar data"
    );
  }
};

// Create a new calendar event
export const createEvent = async (eventData: Partial<CalendarEvent>) => {
  try {
    const response = await api.post('/calendar-events', eventData);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to create event",
      "Could not create the calendar event"
    );
  }
};

// Update a calendar event
export const updateEvent = async (id: number | string, eventData: Partial<CalendarEvent>) => {
  try {
    // If the ID is already a string with task_ prefix, use it as is
    // Otherwise, check if it's a task ID that needs the prefix
    let eventId = id;

    // If it's a string that doesn't have the task_ prefix but represents a task
    if (typeof id === 'string' && !id.startsWith('task_') && eventData.is_task) {
      eventId = `task_${id}`;
    }

    // If it's a number and represents a task
    if (typeof id === 'number' && eventData.is_task) {
      eventId = `task_${id}`;
    }

    const response = await api.put(`/calendar-events/${eventId}`, eventData);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to update event",
      "Could not update the calendar event"
    );
  }
};

// Delete a calendar event
export const deleteEvent = async (id: number | string) => {
  try {
    // If the ID is already a string with task_ prefix, use it as is
    let eventId = id;

    // If it's a string that starts with 'task_', it's already properly formatted
    if (typeof id === 'string' && id.startsWith('task_')) {
      eventId = id;
    }
    // If it's a string that might be a task ID
    else if (typeof id === 'string' && id.includes('task_')) {
      eventId = id;
    }

    const response = await api.delete(`/calendar-events/${eventId}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to delete event",
      "Could not delete the calendar event"
    );
  }
};

// Get a single calendar event by ID
export const getEvent = async (id: number | string) => {
  try {
    // Handle different ID formats
    let eventId = id;

    // If it's a string that already has the task_ prefix, use it as is
    if (typeof id === 'string' && id.startsWith('task_')) {
      eventId = id;
    }
    // For other IDs, we'll let the backend determine if it's a task or event

    const response = await api.get(`/calendar-events/${eventId}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch event",
      "Could not load the calendar event"
    );
  }
};

// Reschedule a calendar event (move to a different date/time)
export const rescheduleEvent = async (id: number | string, newStartDate: string, newEndDate?: string) => {
  try {
    // Handle different ID formats
    let eventId = id;
    let isTask = false;

    // If it's a string that already has the task_ prefix, it's a task
    if (typeof id === 'string' && id.startsWith('task_')) {
      eventId = id;
      isTask = true;
    }

    // For tasks, we need to update the task's start_date and end_date
    // For events, we need to update the event's start_date and end_date
    const data = {
      start_date: newStartDate,
      end_date: newEndDate,
      is_task: isTask
    };

    const response = await api.put(`/calendar-events/${eventId}/reschedule`, data);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to reschedule event",
      "Could not update the event date"
    );
  }
};
