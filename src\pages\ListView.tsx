import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Navbar from '@/components/Navbar';
import { getProject } from '@/api/projectsApi';
import { getTasks, deleteTask, Task } from '@/api/tasksApi';
import { List, Calendar, BarChart, Kanban, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { hasProjectPermission, ProjectPermission } from '@/utils/permissionUtils';
import BackButton from '@/components/BackButton';
import TaskTable from '@/components/task/TaskTable';
import TaskDetailModal from '@/components/TaskDetailModal';
import CreateTaskModal from '@/components/task/CreateTaskModal';
import PageBreadcrumbs from '@/components/navigation/PageBreadcrumbs';
import { useToast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const ListView: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // State for modals
  const [isTaskDetailModalOpen, setIsTaskDetailModalOpen] = useState(false);
  const [isCreateTaskModalOpen, setIsCreateTaskModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState<Task | null>(null);

  // Fetch project
  const { data: project, isLoading: projectLoading } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => getProject(Number(projectId)),
    enabled: !!projectId
  });

  // Fetch tasks
  const { data: tasks = [], isLoading: tasksLoading } = useQuery({
    queryKey: ['tasks', projectId],
    queryFn: () => getTasks(Number(projectId)),
    enabled: !!projectId
  });

  // Delete task mutation
  const deleteTaskMutation = useMutation({
    mutationFn: (taskId: number) => deleteTask(taskId),
    onSuccess: () => {
      // Invalidate both tasks and project data to ensure project cards are updated
      queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({
        title: "Task deleted",
        description: "The task has been successfully deleted.",
      });
      setCurrentTask(null);
    },
    onError: (error) => {
      console.error('Error deleting task:', error);
      toast({
        title: "Error",
        description: "Failed to delete the task. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleViewChange = (view: string) => {
    navigate(`/projects/${projectId}/${view}`);
  };

  const handleTaskClick = (task: Task) => {
    setCurrentTask(task);
    setIsTaskDetailModalOpen(true);
  };

  const handleCreateTask = () => {
    setIsCreateTaskModalOpen(true);
  };

  const handleEditTask = (task: Task) => {
    // This would be handled by the TaskDetailModal
    setCurrentTask(task);
    setIsTaskDetailModalOpen(true);
  };

  const handleDeleteTask = (task: Task) => {
    setCurrentTask(task);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteTask = () => {
    if (currentTask) {
      deleteTaskMutation.mutate(currentTask.id);
      setIsDeleteDialogOpen(false);
    }
  };

  if (projectLoading || tasksLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">Loading project data...</p>
          </div>
        </main>
      </div>
    );
  }

  // Check if user has permission to view the project
  const canViewProject = hasProjectPermission(project, user, ProjectPermission.VIEW_PROJECT);

  if (!canViewProject) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">You don't have permission to view this project.</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate('/dashboard')}
              >
                Go to Dashboard
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="max-w-7xl mx-auto px-4 py-6">
        {/* Breadcrumbs */}
        <PageBreadcrumbs projectName={project?.name} />

        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <BackButton />
                <h1 className="text-3xl font-bold text-foreground">
                  {project ? project.name : 'Project List'}
                </h1>
              </div>
              <p className="text-muted-foreground mt-1">
                {project?.description || 'Manage your tasks and track progress'}
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('board')}
              >
                <Kanban size={16} className="mr-1.5" />
                Kanban
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center"
              >
                <List size={16} className="mr-1.5" />
                List
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('calendar')}
              >
                <Calendar size={16} className="mr-1.5" />
                Calendar
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={() => handleViewChange('reports')}
              >
                <BarChart size={16} className="mr-1.5" />
                Reports
              </Button>

              {/* Add Task Button */}
              {project && hasProjectPermission(project, user, ProjectPermission.CREATE_TASK) && (
                <Button
                  className="ml-auto flex items-center gap-1.5"
                  onClick={handleCreateTask}
                >
                  <Plus size={16} />
                  Add Task
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Task Table */}
        {project && (
          <TaskTable
            tasks={tasks}
            project={project}
            currentUser={user}
            onTaskClick={handleTaskClick}
            onEditTask={handleEditTask}
            onDeleteTask={handleDeleteTask}
          />
        )}
      </main>

      {/* Task Detail Modal */}
      {project && currentTask && (
        <TaskDetailModal
          isOpen={isTaskDetailModalOpen}
          onClose={() => setIsTaskDetailModalOpen(false)}
          task={currentTask}
          project={project}
          user={user}
          onEditTask={handleEditTask}
        />
      )}

      {/* Create Task Modal */}
      {project && (
        <CreateTaskModal
          isOpen={isCreateTaskModalOpen}
          onClose={() => setIsCreateTaskModalOpen(false)}
          onSuccess={() => {
            // Invalidate both tasks and project data to ensure project cards are updated
            queryClient.invalidateQueries({ queryKey: ['tasks', projectId] });
            queryClient.invalidateQueries({ queryKey: ['project', projectId] });
            queryClient.invalidateQueries({ queryKey: ['projects'] });
            toast({ title: "Task created successfully" });
          }}
          project={project}
        />
      )}

      {/* Delete Task Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this task?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the task
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTask}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ListView;
