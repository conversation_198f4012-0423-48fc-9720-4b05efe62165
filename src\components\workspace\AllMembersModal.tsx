import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { User } from '@/entities/User';
import { Workspace } from '@/entities/Workspace';
import { Users, Search, Plus, Trash2, Crown, Shield, Eye } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { addWorkspaceUser, removeWorkspaceUser } from '@/api/workspacesApi';
import { searchUserByEmail } from '@/api/usersApi';
import UserAvatar from '@/components/ui/user-avatar';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface AllMembersModalProps {
  isOpen: boolean;
  onClose: () => void;
  members: User[];
  currentWorkspace: Workspace | null;
  onMemberAdded: () => void;
}

const AllMembersModal: React.FC<AllMembersModalProps> = ({
  isOpen,
  onClose,
  members,
  currentWorkspace,
  onMemberAdded
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddMember, setShowAddMember] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberRole, setNewMemberRole] = useState('editor');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Ensure members is an array before filtering
  const filteredMembers = (members || []).filter(member =>
    member.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddMember = async () => {
    if (!currentWorkspace || !newMemberEmail) return;

    setIsSubmitting(true);

    try {
      // Search for the user by email
      const foundUser = await searchUserByEmail(newMemberEmail);

      // Make sure to convert the role name to the correct role ID
      const roleId = parseInt(newMemberRole === 'admin' ? '1' : newMemberRole === 'editor' ? '2' : '3', 10);
      console.log(`Adding member with role ID: ${roleId} (type: ${typeof roleId})`);

      await addWorkspaceUser(
        currentWorkspace.id,
        foundUser.id,
        roleId
      );

      toast({
        title: "Member added",
        description: `${foundUser.first_name} ${foundUser.last_name} was successfully added to the workspace.`,
      });

      setNewMemberEmail('');
      setNewMemberRole('editor');
      setShowAddMember(false);
      onMemberAdded();
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "An error occurred while adding the user.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveMember = async (userId: number) => {
    if (!currentWorkspace) return;

    try {
      await removeWorkspaceUser(currentWorkspace.id, userId);

      toast({
        title: "Member removed",
        description: "User was successfully removed from the workspace.",
      });

      onMemberAdded();
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while removing the user.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Workspace Members</DialogTitle>
          <DialogDescription>
            Manage members and their roles in this workspace.
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-between items-center mb-4">
          <div className="relative flex-1 mr-2">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddMember(!showAddMember)}
          >
            <Plus size={16} className="mr-1" />
            Add Member
          </Button>
        </div>

        {showAddMember && (
          <div className="bg-muted/30 p-4 rounded-lg mb-4 border border-border">
            <h3 className="text-sm font-medium mb-2">Add New Member</h3>
            <div className="space-y-3">
              <div>
                <Input
                  placeholder="Email address"
                  value={newMemberEmail}
                  onChange={(e) => setNewMemberEmail(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2">
                <Select
                  value={newMemberRole}
                  onValueChange={setNewMemberRole}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="editor">Editor</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleAddMember}
                  disabled={!newMemberEmail || isSubmitting}
                >
                  {isSubmitting ? 'Adding...' : 'Add'}
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="max-h-[400px] overflow-y-auto">
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ staggerChildren: 0.05 }}
          >
            {filteredMembers?.length > 0 ? (
              filteredMembers.map((member, index) => {
                // Determine role icon and badge
                const isOwner = currentWorkspace && member.id === currentWorkspace.owner.id;
                let roleIcon = <Eye size={14} />;
                let roleBadge = "Viewer";
                let badgeClass = "bg-blue-50 text-blue-700 border-blue-200";

                if (isOwner) {
                  roleIcon = <Crown size={14} />;
                  roleBadge = "Owner";
                  badgeClass = "bg-amber-50 text-amber-700 border-amber-200";
                } else if (member.role_id === 1) {
                  roleIcon = <Crown size={14} />;
                  roleBadge = "Admin";
                  badgeClass = "bg-amber-50 text-amber-700 border-amber-200";
                } else if (member.role_id === 2) {
                  roleIcon = <Shield size={14} />;
                  roleBadge = "Editor";
                  badgeClass = "bg-green-50 text-green-700 border-green-200";
                }

                return (
                  <motion.div
                    key={member.id}
                    className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/10 transition-colors"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 24,
                      delay: 0.03 * index
                    }}
                  >
                    <div className="flex items-center flex-1">
                      <UserAvatar
                        user={member}
                        size="md"
                        showStatus={true}
                      />
                      <div className="ml-3 flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{member.first_name} {member.last_name}</h4>
                          <Badge variant="outline" className={cn("flex items-center gap-1 px-1.5 py-0", badgeClass)}>
                            {roleIcon}
                            <span className="text-xs">{roleBadge}</span>
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">{member.email}</p>
                      </div>
                    </div>

                    {/* Don't allow removing the workspace owner */}
                    {currentWorkspace && member.id !== currentWorkspace.owner.id && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveMember(member.id)}
                        className="text-muted-foreground hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 size={16} />
                      </Button>
                    )}
                  </motion.div>
                );
              })
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-10 w-10 mx-auto mb-2 text-muted-foreground/50" />
                <p>No members found matching your search criteria.</p>
              </div>
            )}
          </motion.div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AllMembersModal;
