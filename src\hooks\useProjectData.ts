
import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import {  getProjects, getStarredProjects } from '@/api/projectsApi';
import { useToast } from '@/hooks/use-toast';
import { Project } from '@/entities/Project';

interface UseProjectDataReturn {
  projects: Project[];
  starredProjects: Project[];
  isLoading: boolean;
  error: string | null;
  refreshProjects: (workspaceId?: number) => Promise<void>;
}

export const useProjectData = (workspaceId?: number): UseProjectDataReturn => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [starredProjects, setStarredProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();

  const fetchData = useCallback(async (wsId?: number) => {
    if (!isAuthenticated) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch projects
      const projectsData = await getProjects(wsId || workspaceId);
      setProjects(projectsData);
      
      // Fetch starred projects
      const starredData = await getStarredProjects();
      setStarredProjects(starredData);
      
    } catch (err) {
      console.error('Error fetching project data:', err);
      setError('Failed to load project data. Please try again.');
      toast({
        variant: "destructive",
        title: "Error loading data",
        description: "Failed to load project data. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, workspaceId, toast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { 
    projects, 
    starredProjects, 
    isLoading, 
    error,
    refreshProjects: fetchData
  };
};
