
import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';

// Attachment types
export interface Attachment {
  id: number;
  task_id: number;
  user_id: number;
  file_name: string;
  file_type: string;
  file_size: number;
  file_path: string;
  created_at?: string;
  updated_at?: string;
}

// Upload a new attachment
export const uploadAttachment = async (formData: FormData) => {
  try {
    const response = await api.post('/attachments', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to upload attachment",
      "Could not upload the file"
    );
  }
};

// Get a specific attachment
export const getAttachment = async (id: number) => {
  try {
    const response = await api.get(`/attachments/${id}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to fetch attachment",
      "Could not load the requested file"
    );
  }
};

// Delete an attachment
export const deleteAttachment = async (id: number) => {
  try {
    const response = await api.delete(`/attachments/${id}`);
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Failed to delete attachment",
      "Could not delete the file"
    );
  }
};
