
# Development Guide

This document provides more detailed information for developers working on the Project Management Suite.

## Architecture Overview

The application follows a client-server architecture:

- **Frontend**: React SPA (Single Page Application) that communicates with the backend via REST API
- **Backend**: Laravel PHP application that provides the API and handles data persistence

## Frontend Development

### Component Structure

We follow a modular component approach:
- **UI Components**: Reusable, presentational components in `src/components/ui/`
- **Project Components**: Business logic components related to projects in `src/components/project/`
- **Page Components**: Full page layouts in `src/pages/`

### State Management

- **React Context**: Used for global state (auth, project, workspace contexts)
- **React Query**: Used for server state management and data fetching
- **Local State**: Component-specific state using React hooks

### Styling

- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Component library built on Radix UI primitives
- **Custom Components**: Extended components with consistent styling

### Data Flow

1. API requests are made through dedicated API service modules in `src/api/`
2. Data is cached and managed by React Query
3. Components consume data through custom hooks or direct React Query hooks
4. Updates are sent back to the server through API service modules

## Backend Development

### API Structure

- RESTful API endpoints defined in `routes/api.php`
- Controllers handle request validation and business logic
- Models represent database entities and relationships
- Resources transform data for API responses

### Authentication

- Laravel Sanctum for token-based authentication
- Protected routes require valid authentication token
- User roles and permissions for authorization

### Database Schema

The database schema includes tables for:
- Users and authentication
- Workspaces and workspace membership
- Projects and project membership
- Tasks, lists, and task assignments
- Attachments and comments

## Testing

### Frontend Testing

- Component tests with React Testing Library
- API mocking with MSW (Mock Service Worker)
- End-to-end tests with Cypress

### Backend Testing

- Unit tests for models and utilities
- Feature tests for API endpoints
- Database testing with in-memory SQLite

## Code Standards

### TypeScript

- Strong typing for all components and functions
- Interface definitions for props and data structures
- Type guards for runtime type checking

### PHP/Laravel

- PSR-12 coding standard
- Laravel best practices
- Dependency injection
- Repository pattern for data access

## Performance Considerations

### Frontend

- Lazy loading of routes and heavy components
- Memoization of expensive calculations
- Efficient re-rendering with proper React hooks usage
- Image optimization and compression

### Backend

- Database query optimization
- Eager loading of relationships
- Caching of frequently accessed data
- Pagination for large result sets

## Deployment

### Frontend Deployment

The frontend is built as a static site and can be deployed to any static hosting provider:

```bash
npm run build
# Deploy the dist/ directory
```

### Backend Deployment

The Laravel backend can be deployed to any PHP-compatible hosting:

1. Set up environment variables
2. Install dependencies with `composer install --no-dev`
3. Run migrations with `php artisan migrate`
4. Configure web server (Apache/Nginx) to point to public directory

## Continuous Integration

- GitHub Actions workflows for automated testing
- Automated builds and deployments
- Code quality checks with ESLint and PHPStan
