import React, { Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { ProjectProvider } from "./context/ProjectContext";
import { WorkspaceProvider } from "./context/WorkspaceContext";
import { ThemeProvider } from "./context/ThemeContext";
import { LanguageProvider } from "./context/LanguageContext";
import { NotificationProvider } from "./context/NotificationContext";
import { HumanVerificationProvider } from "./context/HumanVerificationContext";
import ProtectedRoute from "./components/ProtectedRoute";
import DefaultWorkspaceRedirect from "./components/DefaultWorkspaceRedirect";
import Loader from "./components/Loader";

// Core pages loaded immediately
import Index from "./pages/Index";
import Login from "./pages/Login";
import NotFound from "./pages/errors/NotFound";
import Forbidden from "./pages/errors/Forbidden";

// Lazy-loaded pages
const Workspace = lazy(() => import("./pages/Workspace"));
const ProjectsPage = lazy(() => import("./pages/ProjectsPage"));
const KanbanBoard = lazy(() => import("./pages/KanbanBoard"));
const ListView = lazy(() => import("./pages/ListView"));
const CalendarViewPage = lazy(() => import("./pages/CalendarView"));
const ProjectCalendar = lazy(() => import("./pages/ProjectCalendar"));
const EditProject = lazy(() => import("./pages/EditProject"));
const ReportsView = lazy(() => import("./pages/ReportsView"));
const Timeline = lazy(() => import("./pages/Timeline"));
const Calendar = lazy(() => import("./pages/Calendar"));
const CreateEvent = lazy(() => import("./pages/CreateEvent"));
const EditEvent = lazy(() => import("./pages/EditEvent"));
const EventDetails = lazy(() => import("./pages/EventDetails"));
const TeamPage = lazy(() => import("./pages/TeamPage"));
const ProfilePage = lazy(() => import("./pages/ProfilePage"));
const SettingsPage = lazy(() => import("./pages/SettingsPage"));
const WorkspaceSettingsPage = lazy(() => import("./pages/WorkspaceSettingsPage"));
const ActivityPage = lazy(() => import("./pages/ActivityPage"));
const ProjectTimeline = lazy(() => import("./pages/ProjectTimeline"));
const AcceptProjectInvitation = lazy(() => import("./pages/AcceptProjectInvitation"));
const SearchResults = lazy(() => import("./pages/SearchResults"));
const LeaveProjectSuccess = lazy(() => import("./pages/LeaveProjectSuccess"));

// Create a queryClient instance with optimized settings
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60000, // 1 minute
      cacheTime: 900000, // 15 minutes
      refetchOnWindowFocus: false,
      retry: 1,
      structuralSharing: true,
    },
    mutations: {
      // When mutations complete, automatically invalidate related queries
      onSuccess: (_, __, ___, meta) => {
        // If the mutation has metadata about which queries to invalidate, use it
        if (meta?.invalidates) {
          meta.invalidates.forEach(queryKey => {
            queryClient.invalidateQueries({ queryKey });
          });
        }
      },
    },
  },
});

const App = () => (
  <HashRouter>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <LanguageProvider>
            <TooltipProvider>
              <NotificationProvider>
                <HumanVerificationProvider>
                  <WorkspaceProvider>
                    <ProjectProvider>
                      <Toaster />
                      <Sonner />
                <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/login" element={<Login />} />
                <Route path="/404" element={<NotFound />} />
                <Route path="/403" element={<Forbidden />} />
                <Route path="/accept-project-invitation" element={
                  <Suspense fallback={<Loader />}>
                    <AcceptProjectInvitation />
                  </Suspense>
                } />
                {/* Redirect from root to the default workspace */}
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <DefaultWorkspaceRedirect />
                  </ProtectedRoute>
                } />
                <Route path="/workspace/:workspaceId" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <Workspace />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/projects" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <ProjectsPage />
                    </Suspense>
                  </ProtectedRoute>
                } />
                {/* Add route for single project view that redirects to board view */}
                <Route path="/projects/:projectId" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <KanbanBoard />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/kanban-board" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <KanbanBoard />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:projectId/board" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <KanbanBoard />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:projectId/list" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <ListView />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:projectId/calendar" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <ProjectCalendar />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:projectId/reports" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <ReportsView />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:projectId/timeline" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <ProjectTimeline />
                    </Suspense>
                  </ProtectedRoute>
                } />

                <Route path="/projects/:projectId/edit" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <EditProject />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/timeline/:workspaceId" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <Timeline />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/calendar" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <Calendar />
                    </Suspense>
                  </ProtectedRoute>
                } />

                <Route path="/calendar/create" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <CreateEvent />
                    </Suspense>
                  </ProtectedRoute>
                } />

                <Route path="/calendar/create/:date" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <CreateEvent />
                    </Suspense>
                  </ProtectedRoute>
                } />

                <Route path="/calendar/edit/:eventId" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <EditEvent />
                    </Suspense>
                  </ProtectedRoute>
                } />

                <Route path="/calendar/events/:eventId" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <EventDetails />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/team" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <TeamPage />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <ProfilePage />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/settings" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <SettingsPage />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/workspace/:workspaceId/settings" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <WorkspaceSettingsPage />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/search" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <SearchResults />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/activity" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <ActivityPage />
                    </Suspense>
                  </ProtectedRoute>
                } />
                <Route path="/projects/leave-success" element={
                  <ProtectedRoute>
                    <Suspense fallback={<Loader />}>
                      <LeaveProjectSuccess />
                    </Suspense>
                  </ProtectedRoute>
                } />
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
                    </ProjectProvider>
                  </WorkspaceProvider>
                </HumanVerificationProvider>
              </NotificationProvider>
            </TooltipProvider>
          </LanguageProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </HashRouter>
);

export default App;
