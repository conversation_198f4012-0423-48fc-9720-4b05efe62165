
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { User, Mail, Phone, Briefcase, CalendarIcon, Info } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { format, isAfter, isFuture } from 'date-fns';
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from '@/lib/utils';

interface ProfileData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  job_title: string;
  bio: string;
  birth_date?: string;
}

interface ProfileFormProps {
  userData: ProfileData;
  onSubmit: (data: Partial<ProfileData>) => Promise<void>;
}

const ProfileForm: React.FC<ProfileFormProps> = ({ userData, onSubmit }) => {
  const [formData, setFormData] = useState({
    firstName: userData.first_name || '',
    lastName: userData.last_name || '',
    email: userData.email || '',
    phone: userData.phone || '',
    jobTitle: userData.job_title || '',
    bio: userData.bio || '',
    birthDate: userData.birth_date ? new Date(userData.birth_date) : undefined,
  });

  const [errors, setErrors] = useState({
    phone: '',
    birthDate: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // For phone field, only allow digits
    if (name === 'phone') {
      // Only update if the value contains only digits or is empty
      if (value === '' || /^\d*$/.test(value)) {
        setFormData(prev => ({ ...prev, phone: value }));
        setErrors(prev => ({ ...prev, phone: '' }));
      }
      // Don't update state if non-numeric characters are entered
    } else {
      // For all other fields, update normally
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle keydown events for the phone input to prevent non-numeric characters
  const handlePhoneKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter, and navigation keys
    if (
      e.key === 'Backspace' ||
      e.key === 'Delete' ||
      e.key === 'Tab' ||
      e.key === 'Escape' ||
      e.key === 'Enter' ||
      e.key === 'ArrowLeft' ||
      e.key === 'ArrowRight' ||
      e.key === 'ArrowUp' ||
      e.key === 'ArrowDown' ||
      e.key === 'Home' ||
      e.key === 'End' ||
      // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
      (e.ctrlKey && (e.key === 'a' || e.key === 'c' || e.key === 'v' || e.key === 'x'))
    ) {
      return;
    }

    // Prevent input if the key is not a number
    if (!/^\d$/.test(e.key)) {
      e.preventDefault();
    }
  };

  const handleDateChange = (date: Date | undefined) => {
    setFormData(prev => ({ ...prev, birthDate: date }));

    // Validate birth date
    if (date && isFuture(date)) {
      setErrors(prev => ({ ...prev, birthDate: 'Birth date cannot be in the future' }));
    } else {
      setErrors(prev => ({ ...prev, birthDate: '' }));
    }
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const newErrors = { phone: '', birthDate: '' };

    // Validate phone number
    if (formData.phone && !/^\d*$/.test(formData.phone)) {
      newErrors.phone = 'Phone number must contain only digits';
      isValid = false;
    }

    // Validate birth date
    if (formData.birthDate && isFuture(formData.birthDate)) {
      newErrors.birthDate = 'Birth date cannot be in the future';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submitting
    if (!validateForm()) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fix the errors in the form before submitting.",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Map form data to user data format
      const updatedData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        phone: formData.phone,
        job_title: formData.jobTitle,
        bio: formData.bio,
        birth_date: formData.birthDate ? format(formData.birthDate, 'yyyy-MM-dd') : undefined
      };

      await onSubmit(updatedData);

    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        variant: "destructive",
        title: "Profile update failed",
        description: "We couldn't update your profile. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="md:col-span-2">
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
        <CardDescription>
          Update your personal details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <div className="relative">
                <Input
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="pl-9"
                />
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <div className="relative">
                <Input
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="pl-9"
                />
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <div className="relative">
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                className="pl-9"
                disabled
              />
              <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <div className="relative">
              <Input
                id="phone"
                name="phone"
                type="tel"
                inputMode="numeric"
                pattern="[0-9]*"
                value={formData.phone}
                onChange={handleInputChange}
                onKeyDown={handlePhoneKeyDown}
                placeholder="Enter digits only"
                className={cn("pl-9", errors.phone ? "border-destructive focus-visible:ring-destructive" : "")}
              />
              <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            </div>
            {errors.phone && (
              <span className="text-destructive text-xs mt-1 flex items-center gap-1">
                <Info size={12} />
                {errors.phone}
              </span>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="jobTitle">Job Title</Label>
            <div className="relative">
              <Input
                id="jobTitle"
                name="jobTitle"
                value={formData.jobTitle}
                onChange={handleInputChange}
                className="pl-9"
              />
              <Briefcase className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="birthDate">Birth Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !formData.birthDate && "text-muted-foreground",
                    errors.birthDate && "border-destructive text-destructive"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.birthDate ? (
                    format(formData.birthDate, "PPP")
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.birthDate}
                  onSelect={handleDateChange}
                  initialFocus
                  disabled={(date) => isFuture(date)}
                  toDate={new Date()}
                />
              </PopoverContent>
            </Popover>
            {errors.birthDate && (
              <span className="text-destructive text-xs mt-1 flex items-center gap-1">
                <Info size={12} />
                {errors.birthDate}
              </span>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              name="bio"
              value={formData.bio}
              onChange={handleInputChange}
              rows={4}
              placeholder="Tell us a little about yourself"
            />
          </div>

          <Button type="submit" className="w-full sm:w-auto" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default ProfileForm;
