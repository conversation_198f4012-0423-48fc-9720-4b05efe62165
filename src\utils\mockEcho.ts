/**
 * Mock implementation of Laravel Echo for static deployments
 * This provides a fake Echo instance that doesn't actually connect to any WebSocket server
 */

// Define the channel interface
interface Channel {
  listen: (event: string, callback: (data: any) => void) => Channel;
  listenForWhisper: (event: string, callback: (data: any) => void) => Channel;
  notification: (callback: (data: any) => void) => Channel;
  stopListening: (event?: string) => Channel;
}

// Define the private channel interface
interface PrivateChannel extends Channel {
  whisper: (event: string, data: any) => PrivateChannel;
}

// Define the presence channel interface
interface PresenceChannel extends PrivateChannel {
  here: (callback: (users: any[]) => void) => PresenceChannel;
  joining: (callback: (user: any) => void) => PresenceChannel;
  leaving: (callback: (user: any) => void) => PresenceChannel;
}

// Define the Echo interface
interface MockEcho {
  channel: (channel: string) => Channel;
  private: (channel: string) => PrivateChannel;
  join: (channel: string) => PresenceChannel;
  leave: (channel: string) => void;
  disconnect: () => void;
  connect: () => void;
  socketId: () => string;
}

// Create a mock channel
const createMockChannel = (): Channel => {
  return {
    listen: (event, callback) => {
      console.log(`[Mock Echo] Listening for event "${event}" on channel`);
      return createMockChannel();
    },
    listenForWhisper: (event, callback) => {
      console.log(`[Mock Echo] Listening for whisper "${event}" on channel`);
      return createMockChannel();
    },
    notification: (callback) => {
      console.log(`[Mock Echo] Listening for notifications on channel`);
      return createMockChannel();
    },
    stopListening: (event) => {
      if (event) {
        console.log(`[Mock Echo] Stopped listening for event "${event}" on channel`);
      } else {
        console.log(`[Mock Echo] Stopped listening to all events on channel`);
      }
      return createMockChannel();
    }
  };
};

// Create a mock private channel
const createMockPrivateChannel = (): PrivateChannel => {
  return {
    ...createMockChannel(),
    whisper: (event, data) => {
      console.log(`[Mock Echo] Whisper event "${event}" with data:`, data);
      return createMockPrivateChannel();
    }
  };
};

// Create a mock presence channel
const createMockPresenceChannel = (): PresenceChannel => {
  return {
    ...createMockPrivateChannel(),
    here: (callback) => {
      console.log(`[Mock Echo] Here callback registered`);
      // Simulate some users
      setTimeout(() => callback([{ id: 1, name: 'John Doe' }]), 100);
      return createMockPresenceChannel();
    },
    joining: (callback) => {
      console.log(`[Mock Echo] Joining callback registered`);
      return createMockPresenceChannel();
    },
    leaving: (callback) => {
      console.log(`[Mock Echo] Leaving callback registered`);
      return createMockPresenceChannel();
    }
  };
};

// Create the mock Echo instance
const createMockEcho = (): MockEcho => {
  console.log('[Mock Echo] Creating mock Echo instance');

  return {
    channel: (channel) => {
      console.log(`[Mock Echo] Subscribing to channel: ${channel}`);
      return createMockChannel();
    },
    private: (channel) => {
      console.log(`[Mock Echo] Subscribing to private channel: ${channel}`);
      return createMockPrivateChannel();
    },
    join: (channel) => {
      console.log(`[Mock Echo] Joining presence channel: ${channel}`);
      return createMockPresenceChannel();
    },
    leave: (channel) => {
      console.log(`[Mock Echo] Leaving channel: ${channel}`);
    },
    disconnect: () => {
      console.log(`[Mock Echo] Disconnecting`);
    },
    connect: () => {
      console.log(`[Mock Echo] Connecting`);
    },
    socketId: () => {
      return `mock-socket-id-${Date.now()}`;
    }
  };
};

export default createMockEcho;
