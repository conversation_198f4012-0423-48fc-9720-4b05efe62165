import { normalizeUser, User } from "./User";

export interface Project {
    id: number;
    workspace_id: number;
    user_id: number;
    owner: User | null;
    template_id: number | null;
    name: string;
    slug: string;
    description: string | null;
    progress: ProjectProgress | null;
    status: ProjectStatus | null;
    _status: number;
    start_date: string | null;
    end_date: string | null;
    priority: ProjectPriority | null;
    created_at: string;
    updated_at: string | null;
    is_starred: boolean | null,
    is_group_project: boolean,
    members: User[] | null,
    total_tasks: number,
    completed_tasks: number | null,
    completion_percentage: number | null,
}


export const normalizeProjects = (data: any[]): Project[] => {
    return data.map(normalizeProject);
};


export const normalizeProject = (raw: any): Project => {
    return {
        id: Number(raw.id),
        workspace_id: Number(raw.workspace_id),
        user_id: Number(raw.user_id),
        owner: raw.user == undefined ? null : normalizeUser(raw.user),
        template_id: raw.template_id !== null ? Number(raw.template_id) : null,
        name: String(raw.name),
        slug: String(raw.slug),
        _status:Number(raw.progress),
        description: raw.description ?? null,
        progress: raw.progress !== null ?
            (typeof raw.progress === 'string'
                ? ProjectProgress[raw.progress as keyof typeof ProjectProgress]
                : raw.progress in ProjectProgress
                    ? raw.progress
                    : null)
            : null,
        status: raw.status !== null ? ProjectStatus[raw.status as keyof typeof ProjectStatus] : null,
        start_date: raw.start_date ?? null,
        end_date: raw.end_date ?? null,
        priority: raw.priority !== null ? ProjectPriority[raw.priority as keyof typeof ProjectPriority] : null,
        created_at: String(raw.created_at),
        updated_at: raw.updated_at ?? null,
        is_starred: raw.is_starred == undefined ? null : raw.is_starred,
        is_group_project: raw.is_group_project == undefined ? false : Boolean(raw.is_group_project),
        members: raw.members == undefined ? null : raw.members.map(normalizeUser),
        // Keep the original values without forcing conversion to ensure our new progress bar can handle any format
        total_tasks: raw.total_tasks === undefined ? null : raw.total_tasks,
        completed_tasks: raw.completed_tasks === undefined ? null : raw.completed_tasks,
        completion_percentage: raw.completion_percentage === undefined ? null : raw.completion_percentage,
    };

}
export enum ProjectStatus {
    Inactive = 0,    // English name (previously Turkish: Inaktif)
    Active = 1,      // English name (previously Turkish: Aktif)
    Completed = 2,   // English name (previously Turkish: Tamamlandı)
}

export enum ProjectPriority {
    Low = 0,      // English name (previously Turkish: Düşük)
    Medium = 1,   // English name (previously Turkish: Orta)
    High = 2,     // English name (previously Turkish: Yüksek)
}

export enum ProjectProgress {
    NotStarted = 0,    // English name (previously Turkish: Başlanmadı)
    InProgress = 1,    // English name (previously Turkish: DevamEdiyor)
    Completed = 2,     // English name (previously Turkish: Tamamlandı)
    UnderReview = 3,   // English name (previously Turkish: İncelemeAltında)
}

// For backward compatibility
// The following objects map deprecated Turkish names to their English enum values
// These are kept for backward compatibility with existing data
export const ProjectStatusBackwardCompatibility = {
    // Turkish names (deprecated)
    Inaktif: ProjectStatus.Inactive,   // Inactive
    Aktif: ProjectStatus.Active,       // Active
    Tamamlandı: ProjectStatus.Completed // Completed
};

export const ProjectPriorityBackwardCompatibility = {
    // Turkish names (deprecated)
    Düşük: ProjectPriority.Low,    // Low
    Orta: ProjectPriority.Medium,  // Medium
    Yüksek: ProjectPriority.High   // High
};

export const ProjectProgressBackwardCompatibility = {
    // Turkish names (deprecated)
    Başlanmadı: ProjectProgress.NotStarted,     // Not Started
    DevamEdiyor: ProjectProgress.InProgress,    // In Progress
    Tamamlandı: ProjectProgress.Completed,      // Completed
    İncelemeAltında: ProjectProgress.UnderReview, // Under Review

    // English names for backward compatibility too
    NotStarted: ProjectProgress.NotStarted,
    InProgress: ProjectProgress.InProgress,
    Completed: ProjectProgress.Completed,
    UnderReview: ProjectProgress.UnderReview
};



