import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, Clock, GripHorizontal } from 'lucide-react';
import { format, addDays, startOfMonth, getMonth, getYear, isSameDay, isBefore, isAfter, isToday, parseISO } from 'date-fns';
import { enUS, es, fr, tr } from 'date-fns/locale'; // Import all supported locales
import { Project } from '@/entities/Project';
import { updateProject } from '@/api/projectsApi';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { getFullImageUrl } from '@/utils/imageUtils';

interface TimelineItem {
  id: string;
  title: string;
  startDate: Date;
  endDate: Date;
  status: string;
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

interface TimelineDisplayProps {
  projects?: Project[];
  items?: TimelineItem[];
  startDate?: Date;
  endDate?: Date;
  viewMode?: 'week' | 'month' | 'quarter';
}

const statusConfig = {
  0: { color: 'bg-green-500 border-green-600', icon: CheckCircle, textColor: 'text-white' },
  1: { color: 'bg-primary border-primary/70', icon: Clock, textColor: 'text-white' },
  2: { color: 'bg-orange-400 border-orange-500', icon: Clock, textColor: 'text-white' },
  3: { color: 'bg-red-500 border-red-600', icon: AlertCircle, textColor: 'text-white' },
};

const TimelineDisplay: React.FC<TimelineDisplayProps> = ({
  projects,
  items,
  startDate = new Date(),
  endDate = addDays(new Date(), 28),
  viewMode = 'month',
}) => {
  const { toast } = useToast();
  const { t, i18n } = useTranslation();
  const timelineRef = useRef<HTMLDivElement>(null);
  const [updatedProjects, setUpdatedProjects] = useState<Project[]>(projects || []);

  // Get the appropriate date-fns locale based on the current language
  const getLocale = () => {
    switch (i18n.language) {
      case 'es': return es;
      case 'fr': return fr;
      case 'tr': return tr;
      default: return enUS;
    }
  };

  // Debug props changes
  useEffect(() => {
    console.log('TimelineDisplay props updated:', {
      startDate: startDate ? format(startDate, 'yyyy-MM-dd') : 'none',
      endDate: endDate ? format(endDate, 'yyyy-MM-dd') : 'none',
      itemsCount: items?.length || 0,
      viewMode
    });

    // Log a warning if no items are provided
    if (!items || items.length === 0) {
      console.warn('TimelineDisplay: No items provided');
    } else {
      console.log('TimelineDisplay items:', items);
    }
  }, [startDate, endDate, items, viewMode]);

  // Generate date range first to avoid the "Cannot access dateRange before initialization" error
  const generateDateRange = () => {
    const dates = [];
    let currentDate = startDate;

    while (isBefore(currentDate, endDate) || isSameDay(currentDate, endDate)) {
      dates.push(currentDate);
      currentDate = addDays(currentDate, 1);
    }

    return dates;
  };

  const dateRange = generateDateRange();

  const [resizingProject, setResizingProject] = useState<{
    id: number;
    edge: 'start' | 'end';
    initialX: number;
    initialDate: Date;
    project: Project;
  } | null>(null);

  // Update local state when projects prop changes
  useEffect(() => {
    if (projects) {
      setUpdatedProjects(projects);
    }
  }, [projects]);

  // Start resizing
  const handleResizeStart = (
    project: Project,
    edge: 'start' | 'end',
    e: React.MouseEvent
  ) => {
    e.stopPropagation();

    if (!timelineRef.current) return;

    const timelineRect = timelineRef.current.getBoundingClientRect();
    const initialX = e.clientX - timelineRect.left;

    setResizingProject({
      id: project.id,
      edge,
      initialX,
      initialDate: edge === 'start'
        ? new Date(project.start_date)
        : new Date(project.end_date),
      project
    });
  };

  // Handle mouse move during resize
  useEffect(() => {
    if (!resizingProject) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!timelineRef.current || !resizingProject) return;

      const timelineRect = timelineRef.current.getBoundingClientRect();
      const mouseX = e.clientX - timelineRect.left;
      const timelineWidth = timelineRect.width;

      // Calculate the day difference based on mouse movement
      const totalDays = dateRange.length;
      const pixelsPerDay = timelineWidth / totalDays;
      const daysDiff = Math.round((mouseX - resizingProject.initialX) / pixelsPerDay);

      // Create a copy of the project to update
      const updatedProject = { ...resizingProject.project };

      if (resizingProject.edge === 'start') {
        // Update start date
        const newStartDate = addDays(resizingProject.initialDate, daysDiff);

        // Don't allow start date to be after end date
        if (isBefore(newStartDate, new Date(updatedProject.end_date))) {
          updatedProject.start_date = format(newStartDate, 'yyyy-MM-dd');
        }
      } else {
        // Update end date
        const newEndDate = addDays(resizingProject.initialDate, daysDiff);

        // Don't allow end date to be before start date
        if (isAfter(newEndDate, new Date(updatedProject.start_date))) {
          updatedProject.end_date = format(newEndDate, 'yyyy-MM-dd');
        }
      }

      // Update the local state
      setUpdatedProjects(prev =>
        prev.map(p => p.id === updatedProject.id ? updatedProject : p)
      );
    };

    const handleMouseUp = async () => {
      if (!resizingProject) return;

      try {
        // Find the updated project
        const updatedProject = updatedProjects.find(p => p.id === resizingProject.id);

        if (!updatedProject) {
          throw new Error('Updated project not found');
        }

        // Save the updated project to the backend with all required fields
        await updateProject(resizingProject.id, {
          name: updatedProject.name,
          description: updatedProject.description,
          start_date: updatedProject.start_date,
          end_date: updatedProject.end_date
        });

        toast({
          title: "Project updated",
          description: "Project timeline has been updated successfully.",
        });
      } catch (error) {
        console.error('Failed to update project:', error);
        toast({
          variant: "destructive",
          title: "Update failed",
          description: "Failed to update project timeline. Please try again.",
        });

        // Revert to original projects on error
        if (projects) {
          setUpdatedProjects(projects);
        }
      }

      // Reset resizing state
      setResizingProject(null);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [resizingProject, dateRange.length, updatedProjects, projects, toast]);

  const groupDatesByWeek = () => {
    const weeks: Date[][] = [];
    let currentWeek: Date[] = [];

    // For week view, ensure we have exactly 7 days (Sun-Sat)
    if (viewMode === 'week') {
      // Make sure we start with Sunday
      const firstDay = startDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      let startingDate = new Date(startDate);

      // If we don't start on Sunday, adjust to the previous Sunday
      if (firstDay !== 0) {
        startingDate = new Date(startDate);
        startingDate.setDate(startDate.getDate() - firstDay);
      }

      // Create a single week with 7 days
      const weekDays: Date[] = [];
      for (let i = 0; i < 7; i++) {
        const day = new Date(startingDate);
        day.setDate(startingDate.getDate() + i);
        weekDays.push(day);
      }

      return [weekDays];
    }

    // For month and quarter views, group by weeks
    dateRange.forEach((date, index) => {
      // If this is the first date or it's a Sunday, start a new week
      if (index === 0 || date.getDay() === 0) {
        if (currentWeek.length > 0) {
          weeks.push(currentWeek);
        }
        currentWeek = [date];
      } else {
        currentWeek.push(date);
      }

      // If this is the last date, add the current week
      if (index === dateRange.length - 1) {
        weeks.push(currentWeek);
      }
    });

    return weeks;
  };

  const weeks = groupDatesByWeek();

  const getProjectStyle = (project: Project) => {
    const firstDate = dateRange[0];
    const lastDate = dateRange[dateRange.length - 1];
    const totalDays = dateRange.length;

    // Use the updated project from state if available
    const updatedProject = updatedProjects.find(p => p.id === project.id) || project;

    const start = new Date(updatedProject.start_date);
    const end = new Date(updatedProject.end_date);

    // For week view, calculate position based on day of week
    if (viewMode === 'week') {
      const startDayOfWeek = start.getDay(); // 0-6
      const endDayOfWeek = end.getDay(); // 0-6

      // Calculate the start position as percentage of the week
      const startOffset = (startDayOfWeek / 7) * 100;

      // Calculate the number of days between start and end (inclusive)
      let daysDiff = endDayOfWeek - startDayOfWeek + 1;
      if (daysDiff <= 0 && endDayOfWeek < startDayOfWeek) {
        daysDiff += 7; // Handle case where end day is earlier in the week than start day
      }

      // Calculate width as percentage of the week
      const width = Math.max(5, (daysDiff / 7) * 100); // Minimum width of 5%

      return {
        left: `${startOffset}%`,
        width: `${width}%`,
      };
    }

    // For month and quarter views
    const effectiveStartDate = isBefore(start, firstDate) ? firstDate : start;
    const effectiveEndDate = isAfter(end, lastDate) ? lastDate : end;

    const startOffset = Math.max(0, Math.floor(
      ((effectiveStartDate.getTime() - firstDate.getTime()) / (********)) / totalDays * 100
    ));

    const endOffset = Math.min(100, Math.ceil(
      ((effectiveEndDate.getTime() - firstDate.getTime()) / (********) + 1) / totalDays * 100
    ));

    const width = endOffset - startOffset;

    return {
      left: `${startOffset}%`,
      width: `${width}%`,
    };
  };

  const isTodayDate = (date: Date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  const getMonthGroups = () => {
    // For week view, just return the week label
    if (viewMode === 'week') {
      return [{
        start: 0,
        end: 6,
        month: format(dateRange[0], 'MMMM yyyy', { locale: getLocale() })
      }];
    }

    const months: { start: number, end: number, month: string }[] = [];
    let currentMonth = getMonth(dateRange[0]);
    let currentYear = getYear(dateRange[0]);
    let startIndex = 0;

    // For quarter view, ensure we have proper month divisions
    if (viewMode === 'quarter') {
      // Get the first day of each month in the quarter
      const uniqueMonths: Date[] = [];
      const monthsSet = new Set<string>();

      dateRange.forEach(date => {
        const monthKey = `${getYear(date)}-${getMonth(date)}`;
        if (!monthsSet.has(monthKey)) {
          monthsSet.add(monthKey);
          uniqueMonths.push(new Date(getYear(date), getMonth(date), 1));
        }
      });

      // Create month groups based on unique months
      uniqueMonths.forEach((monthDate, idx) => {
        const monthStart = dateRange.findIndex(date =>
          getMonth(date) === getMonth(monthDate) && getYear(date) === getYear(monthDate)
        );

        const nextMonthDate = idx < uniqueMonths.length - 1 ? uniqueMonths[idx + 1] : null;
        let monthEnd;

        if (nextMonthDate) {
          monthEnd = dateRange.findIndex(date =>
            getMonth(date) === getMonth(nextMonthDate) && getYear(date) === getYear(nextMonthDate)
          ) - 1;
        } else {
          monthEnd = dateRange.length - 1;
        }

        months.push({
          start: monthStart,
          end: monthEnd,
          month: format(monthDate, 'MMMM yyyy', { locale: getLocale() })
        });
      });

      return months;
    }

    // For month view, use the original logic
    dateRange.forEach((date, index) => {
      const month = getMonth(date);
      const year = getYear(date);

      if (month !== currentMonth || year !== currentYear) {
        months.push({
          start: startIndex,
          end: index - 1,
          month: format(dateRange[startIndex], 'MMMM yyyy', { locale: getLocale() })
        });

        currentMonth = month;
        currentYear = year;
        startIndex = index;
      }

      if (index === dateRange.length - 1) {
        months.push({
          start: startIndex,
          end: index,
          month: format(date, 'MMMM yyyy', { locale: getLocale() })
        });
      }
    });

    return months;
  };

  // Helper functions for timeline items
  const calculateItemPosition = (itemStartDate: Date) => {
    const firstDate = dateRange[0];
    const totalDays = dateRange.length;

    // If item starts before the visible range, align it with the start
    if (isBefore(itemStartDate, firstDate)) {
      return 0;
    }

    // Calculate position as percentage of total timeline width
    const daysDiff = Math.floor(
      (itemStartDate.getTime() - firstDate.getTime()) / (********)
    );

    // For week view, we need to adjust the calculation to account for the full 7-day week
    if (viewMode === 'week') {
      // Calculate the day of the week (0-6)
      const dayOfWeek = itemStartDate.getDay();
      // Calculate position based on day of week (each day is 1/7 of the width)
      return (dayOfWeek / 7) * 100;
    }

    return Math.max(0, (daysDiff / totalDays) * 100);
  };

  const calculateItemWidth = (itemStartDate: Date, itemEndDate: Date) => {
    const firstDate = dateRange[0];
    const lastDate = dateRange[dateRange.length - 1];
    const totalDays = dateRange.length;

    // Adjust dates if they fall outside the visible range
    const effectiveStartDate = isBefore(itemStartDate, firstDate) ? firstDate : itemStartDate;
    const effectiveEndDate = isAfter(itemEndDate, lastDate) ? lastDate : itemEndDate;

    // For week view, calculate width based on days of the week
    if (viewMode === 'week') {
      const startDayOfWeek = effectiveStartDate.getDay(); // 0-6
      const endDayOfWeek = effectiveEndDate.getDay(); // 0-6

      // Calculate the number of days between start and end (inclusive)
      let daysDiff = endDayOfWeek - startDayOfWeek + 1;
      if (daysDiff <= 0) {
        daysDiff += 7; // Handle case where end day is earlier in the week than start day
      }

      // Calculate width as percentage of the week
      return Math.max(5, (daysDiff / 7) * 100); // Minimum width of 5%
    }

    // Calculate duration in days for month and quarter views
    const durationDays = Math.ceil(
      (effectiveEndDate.getTime() - effectiveStartDate.getTime()) / (********) + 1
    );

    // Calculate width as percentage of total timeline width
    return Math.max(5, (durationDays / totalDays) * 100); // Minimum width of 5%
  };

  const monthGroups = getMonthGroups();

  return (
    <div className="timeline-container relative overflow-x-auto" ref={timelineRef}>
      <div className="min-w-[800px]">
        {/* Monthly headers */}
        <div className="flex border-b border-border h-10">
          {monthGroups.map((monthGroup, index) => {
            const width = ((monthGroup.end - monthGroup.start + 1) / dateRange.length) * 100;
            return (
              <div
                key={`month-${index}`}
                className="flex-shrink-0 px-2 py-1 font-medium flex items-center justify-center text-sm bg-muted/30"
                style={{ width: `${width}%` }}
              >
                {monthGroup.month}
              </div>
            );
          })}
        </div>

        {/* Days of the week */}
        <div className="flex border-b border-border">
          {weeks.map((week, weekIndex) => (
            <div key={`week-${weekIndex}`} className="flex-1 flex">
              {week.map((day, dayIndex) => (
                <div
                  key={`day-${weekIndex}-${dayIndex}`}
                  className={cn(
                    "flex-1 text-center py-2 text-xs font-medium border-r border-border last:border-r-0",
                    isTodayDate(day) ? "bg-primary/10" : (day.getDay() === 0 || day.getDay() === 6) ? "bg-muted/30" : ""
                  )}
                >
                  <div className="mb-1 text-muted-foreground">
                    {format(day, 'EEE', { locale: getLocale() })}
                  </div>
                  <div className={cn(
                    "inline-flex items-center justify-center w-6 h-6 rounded-full",
                    isTodayDate(day) ? "bg-primary text-white" : ""
                  )}>
                    {format(day, 'd')}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Timeline bars */}
        <div className="relative pt-8 pb-4">
          <div className="absolute top-0 left-0 right-0 bottom-0 flex">
            {weeks.map((week, weekIndex) => (
              <div key={`grid-week-${weekIndex}`} className="flex-1 flex">
                {week.map((day, dayIndex) => (
                  <div
                    key={`grid-day-${weekIndex}-${dayIndex}`}
                    className={cn(
                      "flex-1 border-r border-border/60",
                      // Make sure the last day of each week has a border
                      dayIndex === week.length - 1 && weekIndex < weeks.length - 1 ? "border-r-2" :
                      // Remove right border on the very last day
                      (weekIndex === weeks.length - 1 && dayIndex === week.length - 1) ? "border-r-0" : "",
                      isTodayDate(day) ? "bg-primary/5" :
                        (day.getDay() === 0 || day.getDay() === 6) ? "bg-muted/20" : ""
                    )}
                  />
                ))}
              </div>
            ))}
          </div>

          {/* Add vertical grid lines for quarter view */}
          {viewMode === 'quarter' && (
            <div className="absolute top-0 left-0 right-0 bottom-0 pointer-events-none">
              {monthGroups.map((month, index) => (
                <div
                  key={`month-divider-${index}`}
                  className="absolute top-0 bottom-0 border-r-2 border-border/80"
                  style={{
                    left: `${(month.end + 1) / dateRange.length * 100}%`,
                    display: index < monthGroups.length - 1 ? 'block' : 'none'
                  }}
                />
              ))}
            </div>
          )}

          <div className="space-y-5 relative z-10">
            {updatedProjects && updatedProjects.length > 0 && updatedProjects.map((project) => {
              const style = getProjectStyle(project);
              // Make sure we have a valid status, default to 1 (in progress) if not
              const statusKey = project._status !== undefined && project._status in statusConfig
                ? project._status
                : 1; // Default to "in progress"
              const StatusIcon = statusConfig[statusKey].icon;
              const isResizing = resizingProject?.id === project.id;

              return (
                <div key={project.id} className="relative h-10">
                  <div
                    className={cn(
                      "absolute top-0 h-full rounded-md border shadow-sm flex items-center px-3 transition-all",
                      statusConfig[statusKey].color,
                      isResizing ? "z-10 shadow-md" : ""
                    )}
                    style={style}
                  >
                    {/* Left resize handle */}
                    <div
                      className={cn(
                        "absolute left-0 top-0 h-full w-2 cursor-ew-resize group",
                        "hover:bg-white/30 hover:border-l-2 hover:border-white",
                        isResizing && resizingProject?.edge === 'start' ? "bg-white/30 border-l-2 border-white" : ""
                      )}
                      onMouseDown={(e) => handleResizeStart(project, 'start', e)}
                    >
                      <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-4 h-4 rounded-full bg-white opacity-0 group-hover:opacity-100 flex items-center justify-center">
                        <GripHorizontal size={10} className="text-primary" />
                      </div>
                    </div>

                    <div className="flex items-center justify-between w-full overflow-hidden whitespace-nowrap">
                      <div className="flex items-center space-x-2 overflow-hidden">
                        <StatusIcon size={14} className="shrink-0 text-white" />
                        <span className="font-medium text-xs overflow-hidden text-ellipsis text-white">
                          {project.name}
                        </span>
                      </div>

                      {project.owner && (
                        <div className="h-6 w-6 rounded-full bg-white/80 overflow-hidden flex-shrink-0 ml-2 border border-white/90">
                          {project.owner?.profile_picture ? (
                            <img
                              src={project.owner?.profile_picture}
                              alt={project.owner?.name}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center bg-primary text-white text-xs font-bold">
                              {project.owner?.name.charAt(0)}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Right resize handle */}
                    <div
                      className={cn(
                        "absolute right-0 top-0 h-full w-2 cursor-ew-resize group",
                        "hover:bg-white/30 hover:border-r-2 hover:border-white",
                        isResizing && resizingProject?.edge === 'end' ? "bg-white/30 border-r-2 border-white" : ""
                      )}
                      onMouseDown={(e) => handleResizeStart(project, 'end', e)}
                    >
                      <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-4 h-4 rounded-full bg-white opacity-0 group-hover:opacity-100 flex items-center justify-center">
                        <GripHorizontal size={10} className="text-primary" />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

            {items && Array.isArray(items) && items.length > 0 && items.map((item) => {
              if (!item || !item.startDate || !item.endDate) {
                console.warn('Invalid timeline item:', item);
                return null;
              }

              // Convert TimelineItem to a format compatible with our display logic
              const itemStyle = {
                left: `${calculateItemPosition(item.startDate)}%`,
                width: `${calculateItemWidth(item.startDate, item.endDate)}%`,
              };

              // Map status string to status config
              const statusMapping: {[key: string]: number} = {
                'completed': 0,
                'in-progress': 1,
                'not-started': 2,
                'at-risk': 3
              };

              // Make sure we have a valid status
              const status = item.status || 'in-progress';
              const statusKey = statusMapping[status] || 1; // Default to in-progress
              const ItemStatusIcon = statusConfig[statusKey].icon;

              return (
                <div key={item.id} className="relative h-10">
                  <div
                    className={cn(
                      "absolute top-0 h-full rounded-md border shadow-sm flex items-center px-3 transition-all",
                      statusConfig[statusKey].color
                    )}
                    style={itemStyle}
                  >
                    <div className="flex items-center justify-between w-full overflow-hidden whitespace-nowrap">
                      <div className="flex items-center space-x-2 overflow-hidden">
                        <ItemStatusIcon size={14} className="shrink-0 text-white" />
                        <span className="font-medium text-xs overflow-hidden text-ellipsis text-white">
                          {item.title}
                        </span>
                      </div>

                      {item.assignee && (
                        <div className="h-6 w-6 rounded-full bg-white/80 overflow-hidden flex-shrink-0 ml-2 border border-white/90">
                          {item.assignee.avatar ? (
                            <img
                              src={getFullImageUrl(item.assignee.avatar)}
                              alt={item.assignee.name}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center bg-primary text-white text-xs font-bold">
                              {item.assignee.name.charAt(0)}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimelineDisplay;