import React, { useState, useEffect } from 'react';
import {
  Filter, X, Search, ArrowUpDown, Save, Star, Clock,
  Calendar as CalendarIcon, User, AlertCircle, CheckCircle,
  ChevronDown, Bookmark, BookmarkCheck, CircleDashed,
  ArrowUp, ArrowRight, ArrowDown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { TaskFilter } from '@/api/tasksApi';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import {
  <PERSON>alog,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose
} from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import { getFullImageUrl } from '@/utils/imageUtils';

interface TaskFilterPanelProps {
  projectId: number;
  users: {
    id: number;
    name?: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
    user?: {
      id: number;
      first_name?: string;
      last_name?: string;
      profile_picture?: string;
    }
  }[];
  onFilterChange: (filters: TaskFilter) => void;
  onSortChange: (field: string, direction: 'asc' | 'desc') => void;
}

interface SavedFilter extends TaskFilter {
  id: string;
  name: string;
}

// Helper function to safely get user initials
const getUserInitial = (user: TaskFilterPanelProps['users'][0]) => {
  if (!user) return '?';

  // If name is available, use the first character
  if (user.name) {
    return user.name.charAt(0);
  }

  // If first_name is available, use its first character
  if (user.first_name) {
    return user.first_name.charAt(0);
  }

  // Check nested user object
  if (user.user) {
    if (user.user.first_name) {
      return user.user.first_name.charAt(0);
    }
  }

  // Fallback to user ID
  return `${user.id}`.charAt(0);
};

// Helper function to get a display name for a user
const getUserDisplayName = (user?: TaskFilterPanelProps['users'][0]) => {
  if (!user) return 'Unknown';

  // Direct properties
  if (user.name) {
    return user.name;
  }

  if (user.first_name && user.last_name) {
    return `${user.first_name} ${user.last_name}`;
  }

  if (user.first_name) {
    return user.first_name;
  }

  // Check nested user object
  if (user.user) {
    if (user.user.first_name && user.user.last_name) {
      return `${user.user.first_name} ${user.user.last_name}`;
    }

    if (user.user.first_name) {
      return user.user.first_name;
    }
  }

  return `User ${user.id}`;
};

const TaskFilterPanel: React.FC<TaskFilterPanelProps> = ({
  projectId,
  users,
  onFilterChange,
  onSortChange
}) => {
  const { user: currentUser } = useAuth();

  // Filter state
  const [status, setStatus] = useState<string | undefined>(undefined);
  const [priority, setPriority] = useState<string | undefined>(undefined);
  const [assignee, setAssignee] = useState<number | undefined>(undefined);
  const [dueDateStart, setDueDateStart] = useState<Date | undefined>(undefined);
  const [dueDateEnd, setDueDateEnd] = useState<Date | undefined>(undefined);
  const [searchTerm, setSearchTerm] = useState('');

  // Sort state
  const [sortField, setSortField] = useState('position');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Saved filters
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [isSaveFilterDialogOpen, setIsSaveFilterDialogOpen] = useState(false);
  const [newFilterName, setNewFilterName] = useState('');

  // Active filters count
  const getActiveFiltersCount = () => {
    let count = 0;
    if (status) count++;
    if (priority) count++;
    if (assignee) count++;
    if (dueDateStart || dueDateEnd) count++;
    if (searchTerm) count++;
    return count;
  };

  // Load saved filters from localStorage
  useEffect(() => {
    const savedFiltersJson = localStorage.getItem(`saved-filters-${projectId}`);
    if (savedFiltersJson) {
      try {
        const parsedFilters = JSON.parse(savedFiltersJson);
        setSavedFilters(parsedFilters);
      } catch (e) {
        console.error('Error parsing saved filters:', e);
      }
    }
  }, [projectId]);

  // Apply filters when they change - with debounce to prevent rapid re-renders
  useEffect(() => {
    // Create the filter object
    const filters: TaskFilter = {
      project_id: projectId,
      status,
      priority,
      assignee_id: assignee,
      due_date_start: dueDateStart ? format(dueDateStart, 'yyyy-MM-dd') : undefined,
      due_date_end: dueDateEnd ? format(dueDateEnd, 'yyyy-MM-dd') : undefined,
      search: searchTerm || undefined,
      sort_field: sortField,
      sort_direction: sortDirection
    };

    // Use a timeout to debounce filter changes
    const timeoutId = setTimeout(() => {
      console.log('Applying filters:', filters);
      onFilterChange(filters);
    }, 300); // 300ms debounce to give more time between filter changes

    // Cleanup timeout on component unmount or when dependencies change
    return () => clearTimeout(timeoutId);
  }, [
    projectId,
    status,
    priority,
    assignee,
    dueDateStart,
    dueDateEnd,
    searchTerm,
    sortField,
    sortDirection,
    onFilterChange
  ]);

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (sortField === field) {
      // Toggle direction if same field
      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      setSortDirection(newDirection);
      onSortChange(field, newDirection);
    } else {
      // New field, default to asc
      setSortField(field);
      setSortDirection('asc');
      onSortChange(field, 'asc');
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setStatus(undefined);
    setPriority(undefined);
    setAssignee(undefined);
    setDueDateStart(undefined);
    setDueDateEnd(undefined);
    setSearchTerm('');
    setSortField('position');
    setSortDirection('asc');
  };

  // Save current filter
  const saveCurrentFilter = () => {
    if (!newFilterName.trim()) {
      toast({
        title: "Filter name required",
        description: "Please enter a name for your filter",
        variant: "destructive"
      });
      return;
    }

    const newFilter: SavedFilter = {
      id: `filter-${Date.now()}`,
      name: newFilterName,
      project_id: projectId,
      status,
      priority,
      assignee_id: assignee,
      due_date_start: dueDateStart ? format(dueDateStart, 'yyyy-MM-dd') : undefined,
      due_date_end: dueDateEnd ? format(dueDateEnd, 'yyyy-MM-dd') : undefined,
      search: searchTerm || undefined,
      sort_field: sortField,
      sort_direction: sortDirection
    };

    const updatedFilters = [...savedFilters, newFilter];
    setSavedFilters(updatedFilters);

    // Save to localStorage
    localStorage.setItem(`saved-filters-${projectId}`, JSON.stringify(updatedFilters));

    setIsSaveFilterDialogOpen(false);
    setNewFilterName('');

    toast({
      title: "Filter saved",
      description: `Your filter "${newFilterName}" has been saved`
    });
  };

  // Apply a saved filter
  const applySavedFilter = (filter: SavedFilter) => {
    setStatus(filter.status);
    setPriority(filter.priority);
    setAssignee(filter.assignee_id);

    if (filter.due_date_start) {
      setDueDateStart(new Date(filter.due_date_start));
    } else {
      setDueDateStart(undefined);
    }

    if (filter.due_date_end) {
      setDueDateEnd(new Date(filter.due_date_end));
    } else {
      setDueDateEnd(undefined);
    }

    setSearchTerm(filter.search || '');
    setSortField(filter.sort_field || 'position');
    setSortDirection(filter.sort_direction || 'asc');

    toast({
      title: "Filter applied",
      description: `Applied filter: ${filter.name}`
    });
  };

  // Delete a saved filter
  const deleteSavedFilter = (id: string) => {
    const updatedFilters = savedFilters.filter(filter => filter.id !== id);
    setSavedFilters(updatedFilters);

    // Save to localStorage
    localStorage.setItem(`saved-filters-${projectId}`, JSON.stringify(updatedFilters));

    toast({
      title: "Filter deleted",
      description: "Your saved filter has been deleted"
    });
  };

  return (
    <div className="space-y-4">
      {/* Search and filter controls */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-grow">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tasks..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-10 px-4">
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {getActiveFiltersCount() > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {getActiveFiltersCount()}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-96 p-5 max-h-[90vh] overflow-y-auto">
              <div className="space-y-5">
                <div className="flex items-center justify-between">
                  <h4 className="text-base font-medium text-foreground">Filter Tasks</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={resetFilters}
                    className="h-8 px-3 text-xs hover:bg-muted"
                  >
                    Reset All
                  </Button>
                </div>

                <Separator className="my-2" />

                {/* Quick Filters Section */}
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-muted-foreground">Quick Filters</h5>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn(
                        "justify-start h-9 text-sm",
                        assignee === currentUser?.id ? "bg-primary/10 border-primary/20 text-primary" : ""
                      )}
                      onClick={() => setAssignee(assignee === currentUser?.id ? undefined : currentUser?.id)}
                    >
                      <User className="h-3.5 w-3.5 mr-2" />
                      My Tasks
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn(
                        "justify-start h-9 text-sm",
                        status === 'completed' ? "bg-primary/10 border-primary/20 text-primary" : ""
                      )}
                      onClick={() => setStatus(status === 'completed' ? undefined : 'completed')}
                    >
                      <CheckCircle className="h-3.5 w-3.5 mr-2" />
                      Completed
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn(
                        "justify-start h-9 text-sm",
                        dueDateEnd && new Date(dueDateEnd) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) ?
                        "bg-primary/10 border-primary/20 text-primary" : ""
                      )}
                      onClick={() => {
                        const nextWeek = new Date();
                        nextWeek.setDate(nextWeek.getDate() + 7);
                        setDueDateEnd(dueDateEnd ? undefined : nextWeek);
                      }}
                    >
                      <Clock className="h-3.5 w-3.5 mr-2" />
                      Due Soon
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn(
                        "justify-start h-9 text-sm",
                        priority === 'high' ? "bg-primary/10 border-primary/20 text-primary" : ""
                      )}
                      onClick={() => setPriority(priority === 'high' ? undefined : 'high')}
                    >
                      <AlertCircle className="h-3.5 w-3.5 mr-2" />
                      High Priority
                    </Button>
                  </div>
                </div>

                <Separator className="my-2" />

                {/* Filter By Section */}
                <div className="space-y-4">
                  <h5 className="text-sm font-medium text-muted-foreground">Filter By</h5>

                  <div className="space-y-3">
                    <Label className="text-xs font-medium">Status</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {['not-started', 'in-progress', 'completed', 'at-risk'].map((statusOption) => (
                        <Button
                          key={statusOption}
                          variant="outline"
                          size="sm"
                          className={cn(
                            "justify-start h-9 text-sm",
                            status === statusOption ? "bg-primary/10 border-primary/20 text-primary" : ""
                          )}
                          onClick={() => setStatus(status === statusOption ? undefined : statusOption)}
                        >
                          {statusOption === 'not-started' && <CircleDashed className="h-3.5 w-3.5 mr-2 text-muted-foreground" />}
                          {statusOption === 'in-progress' && <Clock className="h-3.5 w-3.5 mr-2 text-blue-500" />}
                          {statusOption === 'completed' && <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-500" />}
                          {statusOption === 'at-risk' && <AlertCircle className="h-3.5 w-3.5 mr-2 text-red-500" />}
                          {statusOption === 'not-started' ? 'To Do' :
                           statusOption === 'in-progress' ? 'In Progress' :
                           statusOption === 'completed' ? 'Completed' : 'At Risk'}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-xs font-medium">Priority</Label>
                    <div className="grid grid-cols-3 gap-2">
                      {['high', 'medium', 'low'].map((priorityOption) => (
                        <Button
                          key={priorityOption}
                          variant="outline"
                          size="sm"
                          className={cn(
                            "justify-start h-9 text-sm",
                            priority === priorityOption ? "bg-primary/10 border-primary/20 text-primary" : ""
                          )}
                          onClick={() => setPriority(priority === priorityOption ? undefined : priorityOption)}
                        >
                          {priorityOption === 'high' && <ArrowUp className="h-3.5 w-3.5 mr-2 text-red-500" />}
                          {priorityOption === 'medium' && <ArrowRight className="h-3.5 w-3.5 mr-2 text-yellow-500" />}
                          {priorityOption === 'low' && <ArrowDown className="h-3.5 w-3.5 mr-2 text-green-500" />}
                          {priorityOption.charAt(0).toUpperCase() + priorityOption.slice(1)}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-xs font-medium">Assignee</Label>
                    <Select
                      value={assignee?.toString() || "any"}
                      onValueChange={(value) => setAssignee(value === "any" ? undefined : Number(value))}
                    >
                      <SelectTrigger className="h-10">
                        <SelectValue placeholder="Any assignee" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="any">Any assignee</SelectItem>
                        {users.map((user) => (
                          <SelectItem key={user.id} value={user.id.toString()}>
                            <div className="flex items-center">
                              <Avatar className="h-5 w-5 mr-2">
                                <AvatarImage src={getFullImageUrl(user.profile_picture)} alt={user.name || `${user.id}`} />
                                <AvatarFallback>{getUserInitial(user)}</AvatarFallback>
                              </Avatar>
                              {user.name || `User ${user.id}`}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-xs font-medium">Due Date Range</Label>
                    <div className="flex gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal h-10"
                          >
                            <CalendarIcon className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                            {dueDateStart ? (
                              format(dueDateStart, 'MMM dd, yyyy')
                            ) : (
                              <span className="text-muted-foreground">Start date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={dueDateStart}
                            onSelect={setDueDateStart}
                            initialFocus
                            className="rounded-md border"
                          />
                        </PopoverContent>
                      </Popover>

                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal h-10"
                          >
                            <CalendarIcon className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                            {dueDateEnd ? (
                              format(dueDateEnd, 'MMM dd, yyyy')
                            ) : (
                              <span className="text-muted-foreground">End date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={dueDateEnd}
                            onSelect={setDueDateEnd}
                            initialFocus
                            className="rounded-md border"
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </div>

                <Separator className="my-2" />

                <div className="flex justify-between items-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsSaveFilterDialogOpen(true)}
                    className="h-9 px-3"
                  >
                    <Save className="h-4 w-4 mr-2 text-primary" />
                    Save Filter
                  </Button>

                  {savedFilters.length > 0 && (
                    <Select
                      defaultValue="select"
                      onValueChange={(value) => {
                        if (value === "select") return;
                        const filter = savedFilters.find(f => f.id === value);
                        if (filter) applySavedFilter(filter);
                      }}
                    >
                      <SelectTrigger className="w-[180px] h-9">
                        <SelectValue placeholder="Saved filters" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="select">Select a filter</SelectItem>
                        {savedFilters.map((filter) => (
                          <SelectItem key={filter.id} value={filter.id}>
                            <div className="flex items-center">
                              <BookmarkCheck className="h-3.5 w-3.5 mr-2 text-primary" />
                              {filter.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </div>
            </PopoverContent>
          </Popover>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-10">
                <ArrowUpDown className="h-4 w-4 mr-2" />
                Sort
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56">
              <div className="space-y-2">
                <h4 className="font-medium mb-2">Sort by</h4>

                <div className="space-y-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleSortChange('title')}
                  >
                    Title
                    {sortField === 'title' && (
                      <span className="ml-auto">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleSortChange('priority')}
                  >
                    Priority
                    {sortField === 'priority' && (
                      <span className="ml-auto">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleSortChange('status')}
                  >
                    Status
                    {sortField === 'status' && (
                      <span className="ml-auto">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleSortChange('end_date')}
                  >
                    Due Date
                    {sortField === 'end_date' && (
                      <span className="ml-auto">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleSortChange('created_at')}
                  >
                    Created Date
                    {sortField === 'created_at' && (
                      <span className="ml-auto">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleSortChange('position')}
                  >
                    Default Order
                    {sortField === 'position' && (
                      <span className="ml-auto">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Active filters display */}
      {getActiveFiltersCount() > 0 && (
        <div className="bg-muted/30 rounded-md p-2 mt-2">
          <div className="flex items-center justify-between mb-1.5">
            <h5 className="text-xs font-medium text-muted-foreground">Active Filters</h5>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={resetFilters}
            >
              Clear All
            </Button>
          </div>
          <div className="flex flex-wrap gap-1.5">
            {status && (
              <Badge variant="outline" className="flex items-center gap-1 bg-primary/5 border-primary/20">
                {status === 'not-started' && <CircleDashed className="h-3 w-3" />}
                {status === 'in-progress' && <Clock className="h-3 w-3" />}
                {status === 'completed' && <CheckCircle className="h-3 w-3" />}
                {status === 'at-risk' && <AlertCircle className="h-3 w-3" />}
                <span className="text-xs">
                  {status === 'not-started' ? 'To Do' :
                   status === 'in-progress' ? 'In Progress' :
                   status === 'completed' ? 'Completed' :
                   status === 'at-risk' ? 'At Risk' : status}
                </span>
                <X
                  className="h-3 w-3 cursor-pointer opacity-70 hover:opacity-100"
                  onClick={() => setStatus(undefined)}
                />
              </Badge>
            )}

            {priority && (
              <Badge variant="outline" className="flex items-center gap-1 bg-primary/5 border-primary/20">
                {priority === 'high' && <ArrowUp className="h-3 w-3 text-priority-high" />}
                {priority === 'medium' && <ArrowRight className="h-3 w-3 text-priority-medium" />}
                {priority === 'low' && <ArrowDown className="h-3 w-3 text-priority-low" />}
                <span className="text-xs">
                  {priority.charAt(0).toUpperCase() + priority.slice(1)}
                </span>
                <X
                  className="h-3 w-3 cursor-pointer opacity-70 hover:opacity-100"
                  onClick={() => setPriority(undefined)}
                />
              </Badge>
            )}

            {assignee !== undefined && (
              <Badge variant="outline" className="flex items-center gap-1 bg-primary/5 border-primary/20">
                <User className="h-3 w-3" />
                <span className="text-xs">
                  {getUserDisplayName(users.find(u => u.id === assignee))}
                </span>
                <X
                  className="h-3 w-3 cursor-pointer opacity-70 hover:opacity-100"
                  onClick={() => setAssignee(undefined)}
                />
              </Badge>
            )}

            {(dueDateStart || dueDateEnd) && (
              <Badge variant="outline" className="flex items-center gap-1 bg-primary/5 border-primary/20">
                <CalendarIcon className="h-3 w-3" />
                <span className="text-xs">
                  {dueDateStart ? format(dueDateStart, 'MMM d') : 'Any'} - {dueDateEnd ? format(dueDateEnd, 'MMM d') : 'Any'}
                </span>
                <X
                  className="h-3 w-3 cursor-pointer opacity-70 hover:opacity-100"
                  onClick={() => {
                    setDueDateStart(undefined);
                    setDueDateEnd(undefined);
                  }}
                />
              </Badge>
            )}

            {searchTerm && (
              <Badge variant="outline" className="flex items-center gap-1 bg-primary/5 border-primary/20">
                <Search className="h-3 w-3" />
                <span className="text-xs truncate max-w-[150px]">
                  {searchTerm}
                </span>
                <X
                  className="h-3 w-3 cursor-pointer opacity-70 hover:opacity-100"
                  onClick={() => setSearchTerm('')}
                />
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Save filter dialog */}
      <Dialog open={isSaveFilterDialogOpen} onOpenChange={setIsSaveFilterDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Filter</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="filter-name">Filter Name</Label>
              <Input
                id="filter-name"
                placeholder="My Filter"
                value={newFilterName}
                onChange={(e) => setNewFilterName(e.target.value)}
              />
            </div>

            <div className="space-y-1">
              <Label>Active Filters</Label>
              <div className="text-sm text-muted-foreground">
                {status && <div>Status: {status}</div>}
                {priority && <div>Priority: {priority}</div>}
                {assignee !== undefined && (
                  <div>Assignee: {getUserDisplayName(users.find(u => u.id === assignee))}</div>
                )}
                {(dueDateStart || dueDateEnd) && (
                  <div>
                    Due Date: {dueDateStart ? format(dueDateStart, 'MMM d, yyyy') : 'Any'} - {dueDateEnd ? format(dueDateEnd, 'MMM d, yyyy') : 'Any'}
                  </div>
                )}
                {searchTerm && <div>Search: {searchTerm}</div>}
                {!getActiveFiltersCount() && <div>No active filters</div>}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSaveFilterDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveCurrentFilter}>Save Filter</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Saved filters management */}
      {savedFilters.length > 0 && (
        <div className="mt-4 bg-muted/30 rounded-md p-3">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium flex items-center gap-1.5">
              <Bookmark className="h-4 w-4 text-primary" />
              Saved Filters
            </h4>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {savedFilters.map((filter) => (
              <div
                key={filter.id}
                className="flex items-center justify-between bg-background rounded-md border border-border p-2 hover:border-primary/30 transition-colors"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 px-2 justify-start font-normal text-sm"
                  onClick={() => applySavedFilter(filter)}
                >
                  <BookmarkCheck className="h-3.5 w-3.5 mr-2 text-primary" />
                  {filter.name}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 opacity-70 hover:opacity-100 hover:bg-destructive/10"
                  onClick={() => deleteSavedFilter(filter.id)}
                >
                  <X className="h-3.5 w-3.5" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskFilterPanel;
