[{"id": 1, "name": "Website Redesign", "description": "Collaborative project to redesign our company website", "user_id": 1, "workspace_id": 1, "is_group_project": true, "priority": 2, "progress": 1, "start_date": "2025-04-15", "end_date": "2025-06-30", "slug": "website-redesign-123", "created_at": "2023-04-15T08:30:00Z", "updated_at": "2023-05-20T14:45:00Z"}, {"id": 2, "name": "Mobile App Development", "description": "Developing a new mobile app for client XYZ", "user_id": 1, "workspace_id": 1, "is_group_project": true, "priority": 3, "progress": 0, "start_date": "2025-05-01", "end_date": "2025-08-15", "slug": "mobile-app-dev-456", "created_at": "2023-05-01T09:15:00Z", "updated_at": "2023-05-25T11:20:00Z"}, {"id": 3, "name": "Marketing Campaign", "description": "Q3 marketing campaign for product launch", "user_id": 2, "workspace_id": 2, "is_group_project": true, "priority": 1, "progress": 2, "start_date": "2023-06-10", "end_date": "2023-09-20", "slug": "marketing-campaign-789", "created_at": "2023-06-10T10:45:00Z", "updated_at": "2023-07-05T16:30:00Z"}, {"id": 4, "name": "Database Migration", "description": "Migrate legacy database to new cloud platform", "user_id": 3, "workspace_id": 3, "is_group_project": true, "priority": 2, "progress": 1, "start_date": "2023-07-01", "end_date": "2023-08-15", "slug": "db-migration-012", "created_at": "2023-07-01T13:20:00Z", "updated_at": "2023-07-15T09:45:00Z"}, {"id": 5, "name": "E-commerce Platform", "description": "Building a new e-commerce platform with advanced features", "user_id": 1, "workspace_id": 1, "is_group_project": true, "priority": 3, "progress": 0, "start_date": "2025-04-01", "end_date": "2025-07-15", "slug": "ecommerce-platform-567", "created_at": "2023-08-01T10:00:00Z", "updated_at": "2023-08-01T10:00:00Z"}, {"id": 6, "name": "Content Management System", "description": "Developing a custom CMS for client ABC", "user_id": 2, "workspace_id": 2, "is_group_project": true, "priority": 2, "progress": 1, "start_date": "2023-07-15", "end_date": "2023-10-30", "slug": "cms-project-890", "created_at": "2023-07-15T09:30:00Z", "updated_at": "2023-08-05T14:20:00Z"}, {"id": 7, "name": "AI Research Initiative", "description": "Research project exploring machine learning applications", "user_id": 3, "workspace_id": 3, "is_group_project": true, "priority": 1, "progress": 2, "start_date": "2023-06-01", "end_date": "2023-11-30", "slug": "ai-research-345", "created_at": "2023-06-01T11:15:00Z", "updated_at": "2023-07-20T16:45:00Z"}, {"id": 8, "name": "Mobile Game Development", "description": "Creating a casual mobile game for iOS and Android", "user_id": 1, "workspace_id": 1, "is_group_project": true, "priority": 2, "progress": 3, "start_date": "2025-05-15", "end_date": "2025-09-10", "slug": "mobile-game-678", "created_at": "2023-05-15T13:45:00Z", "updated_at": "2023-08-10T10:30:00Z"}, {"id": 9, "name": "Personal Blog", "description": "My personal blog about technology and programming", "user_id": 1, "workspace_id": 1, "is_group_project": false, "priority": 1, "progress": 2, "start_date": "2025-03-10", "end_date": "2025-04-30", "slug": "personal-blog-123", "created_at": "2023-03-10T09:30:00Z", "updated_at": "2023-04-05T14:15:00Z"}, {"id": 10, "name": "Learning React Native", "description": "Personal project to learn React Native mobile development", "user_id": 2, "workspace_id": 2, "is_group_project": false, "priority": 0, "progress": 1, "start_date": "2025-06-01", "end_date": "2025-08-31", "slug": "learning-react-native-456", "created_at": "2023-06-01T10:00:00Z", "updated_at": "2023-07-15T11:30:00Z"}, {"id": 11, "name": "Photography Portfolio", "description": "Personal photography portfolio website", "user_id": 3, "workspace_id": 3, "is_group_project": false, "priority": 1, "progress": 0, "start_date": "2025-07-01", "end_date": "2025-09-15", "slug": "photography-portfolio-789", "created_at": "2023-07-01T08:45:00Z", "updated_at": "2023-07-01T08:45:00Z"}]