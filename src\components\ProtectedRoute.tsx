import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Loader2, AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from './ui/button';
import { getLocalStorageData } from '@/utils/sessionLocalStorageUtil';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [authError, setAuthError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Additional check for token validity
  useEffect(() => {
    const validateToken = () => {
      try {
        const token = getLocalStorageData('token');
        const userData = getLocalStorageData('user');

        // If we're already authenticated according to the context, we're good
        if (isAuthenticated && user) {
          setAuthError(null);
          return;
        }

        // If we have a token but no user data, or we're not authenticated, something's wrong
        if (token && (!userData || !isAuthenticated)) {
          console.error('Token exists but authentication failed');
          setAuthError('Authentication error. Please try again or log in again.');

          // If we've retried too many times, clear the token and redirect to login
          if (retryCount > 2) {
            console.log('Too many authentication retries, clearing token');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { state: { from: location }, replace: true });
          }
        }
      } catch (error) {
        console.error('Error validating token in ProtectedRoute:', error);
        setAuthError('Authentication error. Please try again or log in again.');
      }
    };

    validateToken();
  }, [isAuthenticated, user, retryCount, navigate, location]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  if (authError) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-8 bg-background">
        <div className="w-full max-w-md shadow-lg border border-border rounded-lg p-6">
          <div className="flex items-center gap-2 text-destructive mb-4">
            <AlertTriangle className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Authentication Error</h2>
          </div>

          <p className="text-muted-foreground mb-6">{authError}</p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={() => {
                setAuthError(null);
                setRetryCount(prev => prev + 1);
                // Force a re-render to trigger the useEffect again
                navigate(location.pathname);
              }}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>

            <Button
              variant="outline"
              onClick={() => navigate('/login', { state: { from: location }, replace: true })}
            >
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
