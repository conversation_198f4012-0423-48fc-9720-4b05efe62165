import { useQueries, useQueryClient, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import { useMemo } from 'react';

/**
 * Custom hook to combine multiple queries into a single result
 * This helps reduce unnecessary re-renders when multiple queries are used together
 * 
 * @param queries Array of query options
 * @returns Combined query result with data, loading, and error states
 */
export function useCombinedQueries<TData = unknown, TError = unknown>(
  queries: UseQueryOptions<TData, TError>[]
): {
  data: (TData | undefined)[];
  isLoading: boolean;
  isError: boolean;
  errors: (TError | null)[];
  isSuccess: boolean;
} {
  // Use the useQueries hook to execute all queries
  const queryResults = useQueries({ queries }) as UseQueryResult<TData, TError>[];
  
  // Memoize the combined result to prevent unnecessary re-renders
  return useMemo(() => {
    const data = queryResults.map(result => result.data);
    const isLoading = queryResults.some(result => result.isLoading);
    const isError = queryResults.some(result => result.isError);
    const errors = queryResults.map(result => result.error);
    const isSuccess = queryResults.every(result => result.isSuccess);
    
    return {
      data,
      isLoading,
      isError,
      errors,
      isSuccess,
    };
  }, [queryResults]);
}

/**
 * Custom hook to combine multiple queries into a single result with a transform function
 * 
 * @param queries Array of query options
 * @param transformFn Function to transform the query results into a single value
 * @returns Combined and transformed query result
 */
export function useCombinedQueriesWithTransform<TData = unknown, TError = unknown, TResult = unknown>(
  queries: UseQueryOptions<TData, TError>[],
  transformFn: (data: (TData | undefined)[]) => TResult
): {
  data: TResult | undefined;
  isLoading: boolean;
  isError: boolean;
  errors: (TError | null)[];
  isSuccess: boolean;
} {
  const { data, isLoading, isError, errors, isSuccess } = useCombinedQueries<TData, TError>(queries);
  
  // Transform the data when all queries are successful
  const transformedData = useMemo(() => {
    if (isSuccess) {
      return transformFn(data);
    }
    return undefined;
  }, [data, isSuccess, transformFn]);
  
  return {
    data: transformedData,
    isLoading,
    isError,
    errors,
    isSuccess,
  };
}

/**
 * Custom hook to prefetch multiple queries
 * 
 * @param queries Array of query options
 */
export function usePrefetchQueries<TData = unknown, TError = unknown>(
  queries: UseQueryOptions<TData, TError>[]
): void {
  const queryClient = useQueryClient();
  
  // Prefetch all queries
  queries.forEach(query => {
    if (query.queryKey) {
      queryClient.prefetchQuery(query);
    }
  });
}

/**
 * Example usage:
 * 
 * ```tsx
 * // Instead of multiple useQuery calls:
 * const { data: projects } = useQuery(['projects', workspaceId], () => getProjects(workspaceId));
 * const { data: tasks } = useQuery(['tasks', projectId], () => getTasks(projectId));
 * 
 * // Use combined queries:
 * const { data, isLoading } = useCombinedQueries([
 *   {
 *     queryKey: ['projects', workspaceId],
 *     queryFn: () => getProjects(workspaceId),
 *   },
 *   {
 *     queryKey: ['tasks', projectId],
 *     queryFn: () => getTasks(projectId),
 *   },
 * ]);
 * 
 * // Access data:
 * const [projects, tasks] = data;
 * ```
 */
