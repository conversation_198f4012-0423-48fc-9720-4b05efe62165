import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

type ChangeType = 'increase' | 'decrease' | 'neutral';

interface StatusCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  description?: string;
  change?: {
    value: number;
    type: ChangeType;
  };
  className?: string;
}

export const StatusCard: React.FC<StatusCardProps> = ({
  title,
  value,
  icon,
  description,
  change,
  className,
}) => {
  const { t } = useTranslation();

  const changeVariants = {
    increase: {
      className: 'bg-success text-white',
      prefix: '+',
      icon: <TrendingUp className="h-3 w-3" />,
    },
    decrease: {
      className: 'bg-destructive text-white',
      prefix: '-',
      icon: <TrendingDown className="h-3 w-3" />,
    },
    neutral: {
      className: 'bg-muted text-muted-foreground',
      prefix: '',
      icon: null,
    },
  };

  return (
    <div className={cn(
      "bg-primary/5 border border-primary/20 rounded-xl shadow-sm overflow-hidden relative group transition-all duration-300 hover:shadow-md transform hover:translate-y-[-2px]",
      className
    )}>
      <div className="p-5">
        <div className="flex items-start justify-between">
          <div>
            <p className="text-sm font-medium text-primary mb-1">{title}</p>
            <h4 className="text-2xl font-bold">{value}</h4>

            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}

            {change && (
              <div className="flex items-center mt-1.5">
                <span className={cn(
                  "text-xs font-medium px-1.5 py-0.5 rounded-full flex items-center gap-0.5",
                  changeVariants[change.type].className
                )}>
                  {changeVariants[change.type].icon}
                  {changeVariants[change.type].prefix}
                  {Math.abs(change.value)}%
                </span>
                <span className="text-xs text-muted-foreground ml-1.5">{t('dashboard.comparedToLastMonth', 'vs. last month')}</span>
              </div>
            )}
          </div>

          <div className="h-10 w-10 rounded-full flex items-center justify-center bg-primary text-white">
            {icon}
          </div>
        </div>
      </div>

      {/* Jira-inspired hover effect with animated gradient border */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-transparent to-transparent group-hover:from-primary/20 group-hover:via-primary group-hover:to-primary/20 transition-all duration-500"></div>
    </div>
  );
};

export default StatusCard;
