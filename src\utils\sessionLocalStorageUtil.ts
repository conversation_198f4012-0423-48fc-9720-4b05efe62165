const SESSION_EXPIRY = Number(import.meta.env.VITE_SESSION_EXPIRY) || 10800000;

export const setLocalStorageData = (key: string, value: any, ttl: number = SESSION_EXPIRY) => {
    const now = new Date();
    const item = {
        value: value,
        expiry: now.getTime() + ttl,
    };
    localStorage.setItem(key, JSON.stringify(item));
};


export const getLocalStorageData = (key: string) => {
    const itemStr = localStorage.getItem(key);

    if (!itemStr) return null;

    try {
        const item = JSON.parse(itemStr);
        const now = new Date();

        if (!item.expiry || !item.value) {
            localStorage.removeItem(key);
            return null;
        }

        if (now.getTime() > item.expiry) {
            localStorage.removeItem(key);
            return null;
        }
        return item.value;
    } catch (error) {
        // If there's an error parsing JSON, remove the corrupted data
        console.error('Error parsing localStorage data:', error);
        localStorage.removeItem(key);
        return null;
    }
};

export const removeFromLocalStorage = (key: string) => {
    localStorage.removeItem(key);
};
