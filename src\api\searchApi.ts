import api from './api';
import { handleApiError } from '@/utils/apiErrorHandling';
import { Project } from '@/entities/Project';
import { Task } from './tasksApi';
import { User } from '@/entities/User';

// Search result types
export interface SearchResults {
  query: string;
  results: {
    projects?: Project[];
    tasks?: Task[];
    users?: User[];
  };
  total_count: number;
}

/**
 * Perform a global search across projects, tasks, and users
 * @param query Search query string
 * @returns Search results
 */
export const globalSearch = async (query: string): Promise<SearchResults> => {
  try {
    const response = await api.get('/search', { params: { query } });
    return response.data;
  } catch (error) {
    return handleApiError(
      error,
      "Search failed",
      "Could not complete the search request"
    );
  }
};
