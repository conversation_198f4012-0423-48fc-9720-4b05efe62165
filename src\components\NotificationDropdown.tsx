import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useNotifications } from '@/context/NotificationContext';
import { formatDistanceToNow } from 'date-fns';
import { NotificationType } from '@/api/notificationsApi';
import {
  Bell,
  Check,
  CheckCheck,
  Clock,
  MessageSquare,
  Trash2,
  User,
  X,
  AlertCircle,
  Calendar,
  FileText,
  Info,
  Mail,
  Star,
  Users
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { useWorkspace } from '@/context/WorkspaceContext';
import { useToast } from '@/hooks/use-toast';

const NotificationDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { fetchWorkspaceById } = useWorkspace();
  const {
    notifications,
    unreadCount,
    isLoading,
    fetchNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    removeNotification
  } = useNotifications();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Refresh notifications when opening dropdown
  useEffect(() => {
    if (isOpen) {
      fetchNotifications();
    }
  }, [isOpen, fetchNotifications]);

  // Get notification icon and color based on type
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'task_assigned':
        return <User className="h-4 w-4 text-blue-500" />;
      case 'task_due_soon':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'task_completed':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'comment_added':
        return <MessageSquare className="h-4 w-4 text-indigo-500" />;
      case 'project_invitation':
        return <Users className="h-4 w-4 text-purple-500" />;
      case 'project_update':
        return <FileText className="h-4 w-4 text-cyan-500" />;
      case 'mention':
        return <Star className="h-4 w-4 text-amber-500" />;
      case 'role_updated':
        return <User className="h-4 w-4 text-violet-500" />;
      case 'removed_from_project':
        return <X className="h-4 w-4 text-red-500" />;
      case 'system':
        return <Info className="h-4 w-4 text-gray-500" />;
      default:
        return <Bell className="h-4 w-4 text-primary" />;
    }
  };

  // Get notification background color based on type
  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case 'task_assigned':
        return 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500';
      case 'task_due_soon':
        return 'bg-amber-50 dark:bg-amber-900/20 border-l-4 border-amber-500';
      case 'task_completed':
        return 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500';
      case 'comment_added':
        return 'bg-indigo-50 dark:bg-indigo-900/20 border-l-4 border-indigo-500';
      case 'project_invitation':
        return 'bg-purple-50 dark:bg-purple-900/20 border-l-4 border-purple-500';
      case 'project_update':
        return 'bg-cyan-50 dark:bg-cyan-900/20 border-l-4 border-cyan-500';
      case 'mention':
        return 'bg-amber-50 dark:bg-amber-900/20 border-l-4 border-amber-500';
      case 'role_updated':
        return 'bg-violet-50 dark:bg-violet-900/20 border-l-4 border-violet-500';
      case 'removed_from_project':
        return 'bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500';
      case 'system':
        return 'bg-gray-50 dark:bg-gray-800/40 border-l-4 border-gray-500';
      default:
        return 'border-l-4 border-primary';
    }
  };

  // Get notification link based on type and data
  const getNotificationLink = (notification: any) => {
    const { type, data } = notification as { type: NotificationType, data: any };

    if (!data) return '#';

    // For role_updated and other workspace-related notifications, we'll handle navigation
    // programmatically in handleNotificationClick to properly handle missing workspaces
    if (type === 'role_updated' ||
        type === 'removed_from_project' ||
        type === 'workspace_invitation_accepted') {
      // Return a placeholder URL that will be overridden by our click handler
      return '#';
    }

    switch (type) {
      case 'task_assigned':
      case 'task_due_soon':
      case 'task_completed':
        // Task-related notifications
        return `/projects/${data.project_id}/board?task=${data.task_id}`;
      case 'comment_added':
        // Comment notifications
        return `/projects/${data.project_id}/board?task=${data.task_id}&comment=${data.comment_id}`;
      case 'project_invitation':
        // Project invitation notifications - navigate directly to the project
        return `/projects/${data.project_id}/board`;
      case 'project_update':
        // Project update notifications - navigate directly to the project board
        return `/projects/${data.project_id}/board`;
      case 'mention':
        // Mention notifications
        if (data.task_id) {
          return `/projects/${data.project_id}/board?task=${data.task_id}`;
        }
        return `/projects/${data.project_id}/board`;
      case 'user_left_project':
      case 'project_ownership_received':
        // Project-related notifications
        if (data.project_id) {
          return `/projects/${data.project_id}/board`;
        }
        // For workspace-related notifications, we'll handle navigation programmatically
        return '#';
      case 'system':
        // System notifications
        if (data.url) {
          return data.url;
        }
        return '#';
      default:
        console.log('Unknown notification type:', type, data);
        // For unknown types, try to navigate to the project if project_id is available
        if (data && data.project_id) {
          return `/projects/${data.project_id}/board`;
        }
        return '#';
    }
  };

  // Handle notification click
  const handleNotificationClick = async (id: number, notification: any) => {
    try {
      // Mark the notification as read
      await markNotificationAsRead(id);

      // Close the dropdown after clicking a notification
      setIsOpen(false);

      // Update workspace context if the notification contains workspace_id
      const { type, data } = notification;

      if (data && data.workspace_id) {
        console.log('Notification contains workspace_id, updating workspace context:', data.workspace_id);

        try {
          // Fetch the workspace data to update the context
          await fetchWorkspaceById(data.workspace_id.toString());
        } catch (workspaceError) {
          console.error('Error fetching workspace:', workspaceError);

          // If workspace not found (404) or access denied (403), redirect to default workspace
          if (workspaceError.response &&
              (workspaceError.response.status === 404 || workspaceError.response.status === 403)) {

            console.log('Workspace not found or access denied, redirecting to dashboard');

            // Show toast notification
            toast({
              title: "Workspace not available",
              description: "The workspace referenced in this notification is no longer available or you don't have access to it.",
              variant: "destructive",
            });

            // Redirect to dashboard to get a default workspace
            navigate('/dashboard');
            return;
          }

          // If there's a project_id in the notification, try to navigate to the project directly
          if (data.project_id) {
            console.log('Falling back to project navigation:', data.project_id);
            navigate(`/projects/${data.project_id}/board`);
            return;
          }
        }
      } else if (data && data.project_id) {
        // For project-related notifications without workspace_id, navigate directly
        console.log('Notification contains project_id, navigating directly:', data.project_id);

        // Determine the appropriate path based on notification type
        let path = `/projects/${data.project_id}/board`;

        if (type === 'task_assigned' || type === 'task_due_soon' || type === 'task_completed') {
          if (data.task_id) {
            path = `/projects/${data.project_id}/board?task=${data.task_id}`;
          }
        } else if (type === 'comment_added') {
          if (data.task_id && data.comment_id) {
            path = `/projects/${data.project_id}/board?task=${data.task_id}&comment=${data.comment_id}`;
          }
        }

        // Use navigate instead of Link to ensure proper context updates
        navigate(path);
        return; // Prevent default Link behavior
      }
    } catch (error) {
      console.error('Error handling notification click:', error);

      // Show a generic error toast
      toast({
        title: "Error",
        description: "There was a problem processing this notification. Please try again.",
        variant: "destructive",
      });

      // Redirect to dashboard as a fallback
      navigate('/dashboard');
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Bell Button */}
      <button
        className="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors relative"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Notifications"
      >
        <Bell size={20} className={unreadCount > 0 ? "animate-pulse-subtle" : ""} />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 h-4 w-4 bg-primary rounded-full flex items-center justify-center text-[10px] text-white font-medium">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-[calc(100vw-32px)] sm:w-80 max-w-[320px] sm:max-w-none bg-card rounded-md shadow-elevated border border-border overflow-hidden animate-fade-in z-50 notification-dropdown">
          <div className="p-3 flex items-center justify-between border-b border-border">
            <h3 className="font-medium">Notifications</h3>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllNotificationsAsRead}
                className="h-8 px-2 text-xs"
              >
                <CheckCheck className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Mark all as read</span>
                <span className="sm:hidden">Clear all</span>
              </Button>
            )}
          </div>

          <ScrollArea className="h-[300px] sm:h-[350px]">
            {isLoading ? (
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : notifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-20 text-muted-foreground">
                <Bell className="h-5 w-5 mb-2" />
                <p className="text-sm">No notifications</p>
              </div>
            ) : (
              <div className="py-1">
                {notifications.map((notification) => (
                  <div key={notification.id} className="relative">
                    <Link
                      to={getNotificationLink(notification)}
                      className={cn(
                        "block px-4 py-3 hover:bg-accent transition-colors relative",
                        !notification.read && "bg-primary/5",
                        getNotificationColor(notification.type),
                        "animate-slide-in"
                      )}
                      style={{
                        animationDelay: `${notifications.indexOf(notification) * 50}ms`,
                        animationFillMode: 'backwards'
                      }}
                      onClick={(e) => {
                        // Prevent default Link behavior for:
                        // 1. Project notifications without workspace context
                        // 2. Workspace-related notifications (to handle missing workspaces)
                        if ((notification.data && notification.data.project_id && !notification.data.workspace_id) ||
                            notification.type === 'role_updated' ||
                            notification.type === 'removed_from_project' ||
                            notification.type === 'workspace_invitation_accepted') {
                          e.preventDefault();
                        }
                        handleNotificationClick(notification.id, notification);
                      }}
                    >
                      {!notification.read && (
                        <span className="absolute left-1 top-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-primary rounded-full animate-pulse-subtle"></span>
                      )}
                      <div className="flex items-start">
                        <div className="flex-shrink-0 mr-3">
                          {notification.related_user_id ? (
                            <Avatar className="h-8 w-8 border border-border">
                              <AvatarImage
                                src={notification.relatedUser?.profile_picture || ''}
                                alt={notification.relatedUser?.first_name || 'User'}
                              />
                              <AvatarFallback>
                                {notification.relatedUser?.first_name?.[0] || 'U'}
                              </AvatarFallback>
                            </Avatar>
                          ) : (
                            <div className={cn(
                              "h-8 w-8 rounded-full flex items-center justify-center",
                              notification.type === 'task_assigned' && "bg-blue-100 dark:bg-blue-900/30",
                              notification.type === 'task_due_soon' && "bg-amber-100 dark:bg-amber-900/30",
                              notification.type === 'task_completed' && "bg-green-100 dark:bg-green-900/30",
                              notification.type === 'comment_added' && "bg-indigo-100 dark:bg-indigo-900/30",
                              notification.type === 'project_invitation' && "bg-purple-100 dark:bg-purple-900/30",
                              notification.type === 'project_update' && "bg-cyan-100 dark:bg-cyan-900/30",
                              notification.type === 'mention' && "bg-amber-100 dark:bg-amber-900/30",
                              notification.type === 'role_updated' && "bg-violet-100 dark:bg-violet-900/30",
                              notification.type === 'removed_from_project' && "bg-red-100 dark:bg-red-900/30",
                              notification.type === 'system' && "bg-gray-100 dark:bg-gray-800"
                            )}>
                              {getNotificationIcon(notification.type)}
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-foreground truncate">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1 flex items-center">
                            <Clock className="h-3 w-3 mr-1 inline-block opacity-70" />
                            {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    </Link>
                    <div className="absolute top-2 right-2 flex space-x-1">
                      {!notification.read && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            markNotificationAsRead(notification.id);
                          }}
                          className="p-1 rounded-full hover:bg-background text-muted-foreground hover:text-foreground"
                          title="Mark as read"
                        >
                          <Check className="h-3 w-3" />
                        </button>
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeNotification(notification.id);
                        }}
                        className="p-1 rounded-full hover:bg-background text-muted-foreground hover:text-foreground"
                        title="Remove notification"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                    <Separator />
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
