import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import ProjectCard, { ProjectCardProps } from '../components/ProjectCard';
import { Project, getProjects, starProject, unstarProject, getStarredProjects } from '../api/projectsApi';
import { useAuth } from '../context/AuthContext';
import { useWorkspace } from '@/context/WorkspaceContext';
import { Plus, Search, Filter, ArrowUpDown, LayoutGrid, AlertTriangle, ArrowLeft, Check, Calendar, Users, ArrowUp, ArrowRight, ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import CreateProjectModal from '@/components/project/CreateProjectModal';
import { getLocalStorageData, setLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import Loader from '@/components/Loader';

const ProjectsPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    currentWorkspace,
    workspaces,
    fetchWorkspaces,
    fetchWorkspaceById,
    setCurrentWorkspace
  } = useWorkspace();
  const { toast } = useToast();
  // Get the initial filter from URL or localStorage
  const getInitialFilter = () => {
    // First check URL parameters
    const searchParams = new URLSearchParams(window.location.search);
    const filterParam = searchParams.get('filter');
    if (filterParam && ['all', 'active', 'completed', 'starred'].includes(filterParam)) {
      return filterParam;
    }

    // Then check localStorage
    const savedFilter = localStorage.getItem('projectsFilter');
    if (savedFilter && ['all', 'active', 'completed', 'starred'].includes(savedFilter)) {
      return savedFilter;
    }

    // Default to 'all'
    return 'all';
  };

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState(getInitialFilter());
  const [starredProjectIds, setStarredProjectIds] = useState<number[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [hiddenProjectIds, setHiddenProjectIds] = useState<number[]>([]);
  const hasFetchedRef = useRef(false);

  // Advanced filtering and sorting
  const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);
  const [isSortMenuOpen, setIsSortMenuOpen] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState<{
    priority: string | null;
    dueDate: string | null;
    hasMembers: boolean | null;
  }>({
    priority: null,
    dueDate: null,
    hasMembers: null
  });
  const [sortOption, setSortOption] = useState<{
    field: 'name' | 'dueDate' | 'priority' | 'progress';
    direction: 'asc' | 'desc';
  }>({
    field: 'name',
    direction: 'asc'
  });

  // Load a workspace if none is selected
  useEffect(() => {
    const loadWorkspace = async () => {
      if (hasFetchedRef.current) return;
      hasFetchedRef.current = true;

      setIsLoading(true);
      setLoadingError(null);

      try {
        // First check if we have a user-specific workspace in localStorage
        let savedWorkspace = null;

        if (user?.id) {
          // Try to get the user-specific workspace first
          const userWorkspaceKey = `workspace_${user.id}`;
          savedWorkspace = getLocalStorageData(userWorkspaceKey);

          // If not found, fall back to the regular workspace key
          if (!savedWorkspace || !savedWorkspace.id) {
            savedWorkspace = getLocalStorageData('workspace');

            // If we found a workspace in the regular key, validate it belongs to the current user
            if (savedWorkspace && savedWorkspace.owner && savedWorkspace.owner.id !== user.id) {
              console.warn('Found workspace belonging to another user, ignoring it');
              savedWorkspace = null;
              localStorage.removeItem('workspace');
            }
          }
        } else {
          // If no user is available, try the regular workspace key as fallback
          savedWorkspace = getLocalStorageData('workspace');
        }

        if (savedWorkspace && savedWorkspace.id) {
          await fetchWorkspaceById(savedWorkspace.id.toString());
          setIsLoading(false);
          return;
        }

        // If no workspace in localStorage, fetch workspaces
        if (!workspaces || workspaces.length === 0) {
          await fetchWorkspaces();
        }

        // If we have workspaces, use the first one
        if (workspaces && workspaces.length > 0) {
          const firstWorkspace = workspaces[0];
          await fetchWorkspaceById(firstWorkspace.id.toString());

          // Save to localStorage with user-specific key if user is available
          if (user?.id) {
            const userWorkspaceKey = `workspace_${user.id}`;
            setLocalStorageData(userWorkspaceKey, firstWorkspace, 24 * 60 * 60 * 1000);
          }

          // Also save to regular workspace key for backward compatibility
          setLocalStorageData('workspace', firstWorkspace, 24 * 60 * 60 * 1000);
        } else {
          setLoadingError("No workspaces found. Please create a workspace first.");
          toast({
            title: "No workspaces found",
            description: "You don't have any workspaces. Please create one.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error loading workspace:", error);
        setLoadingError("Failed to load workspace data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load workspace data. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (!currentWorkspace) {
      loadWorkspace();
    } else {
      setIsLoading(false);
    }
  }, [currentWorkspace, fetchWorkspaceById, fetchWorkspaces, workspaces, toast, user]);

  // Fetch projects using React Query with wrapped query functions
  const { data: projects = [], isLoading: isLoadingProjects, refetch: refetchProjects } = useQuery({
    queryKey: ['projects', currentWorkspace?.id],
    queryFn: async () => {
      return await getProjects(currentWorkspace?.id);
    },
    enabled: !!currentWorkspace,
  });

  // Fetch starred projects with wrapped query function
  const { data: starredProjects = [], isLoading: isLoadingStarred, refetch: refetchStarred } = useQuery({
    queryKey: ['starredProjects'],
    queryFn: async () => {
      return await getStarredProjects();
    },
  });

  // Update starred projects when data is loaded
  useEffect(() => {
    if (starredProjects && Array.isArray(starredProjects) && starredProjects.length) {
      setStarredProjectIds(starredProjects.map(project => project.id));
    }
  }, [starredProjects]);

  // Update URL and localStorage when filter changes, and reset hidden projects
  useEffect(() => {
    // Reset hidden projects
    setHiddenProjectIds([]);

    // Update URL with the selected filter
    const searchParams = new URLSearchParams(window.location.search);
    if (selectedFilter === 'all') {
      searchParams.delete('filter');
    } else {
      searchParams.set('filter', selectedFilter);
    }

    // Update the URL without reloading the page
    const newUrl = `${window.location.pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    window.history.pushState({ path: newUrl }, '', newUrl);

    // Save to localStorage
    localStorage.setItem('projectsFilter', selectedFilter);
  }, [selectedFilter]);

  // Handle starring/unstarring a project
  const handleToggleStar = async (id: string) => {
    try {
      const numericId = parseInt(id);
      const isCurrentlyStarred = starredProjectIds.includes(numericId);

      // If we're unstarring a project
      if (isCurrentlyStarred) {
        // If we're in the starred tab, immediately hide this project from the UI
        if (selectedFilter === 'starred') {
          // Force immediate removal from the UI by adding to hiddenProjectIds
          setHiddenProjectIds(prev => [...prev, numericId]);
        }

        // Make API call
        await unstarProject(numericId);

        // Immediately update the starredProjectIds state to remove this project
        setStarredProjectIds(prevIds => prevIds.filter(pid => pid !== numericId));

        toast({
          title: "Project removed from favorites"
        });

        // Force a re-render of the component
        setForceUpdate(prev => prev + 1);

        // Refetch data to ensure consistency
        refetchStarred();

        // If we're in the starred tab, also refetch projects and consider reloading
        if (selectedFilter === 'starred') {
          await refetchProjects();

          // If we're still seeing the project in the UI, force a reload as a last resort
          // This ensures the filter is preserved
          setTimeout(() => {
            const projectStillVisible = document.querySelector(`[data-project-id="${numericId}"]`);
            if (projectStillVisible) {
              // Ensure the URL has the filter parameter before reloading
              const currentUrl = window.location.href;
              if (currentUrl.includes('filter=starred')) {
                window.location.reload();
              } else {
                const separator = currentUrl.includes('?') ? '&' : '?';
                window.location.href = `${currentUrl}${separator}filter=starred`;
              }
            }
          }, 500); // Give React time to update the DOM
        }
      } else {
        // Make API call to star the project
        await starProject(numericId);

        // Immediately update the starredProjectIds state to add this project
        setStarredProjectIds(prevIds => [...prevIds, numericId]);

        toast({
          title: "Project added to favorites"
        });

        // Force a re-render of the component
        setForceUpdate(prev => prev + 1);

        // Refetch data to ensure consistency
        refetchStarred();
      }
    } catch (error) {
      console.error("Error toggling star status:", error);
      toast({
        title: "An error occurred",
        description: "Could not update project favorites.",
        variant: "destructive",
      });
    }
  };

  // Navigate to project kanban board
  const handleViewKanban = (projectId: string) => {
    navigate(`/projects/${projectId}/board`);
  };

  // Navigate to project timeline
  const handleViewTimeline = (projectId: string) => {
    navigate(`/projects/${projectId}/timeline`);
  };

  // Helper function to map Project to ProjectCardProps
  const mapProjectToCardProps = (project: Project): ProjectCardProps => {
    // Convert progress from string to number if needed
    const progressNumber = typeof project.progress === 'string'
      ? parseFloat(project.progress) || 0
      : project.progress || 0;

    return {
      id: project.id.toString(),
      title: project.name,
      description: project.description || '',
      progress: progressNumber,
      dueDate: project.end_date ? new Date(project.end_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) : 'No date',
      priority: (project.priority as 'low' | 'medium' | 'high') || 'medium',
      status: (project.status as 'completed' | 'in-progress' | 'not-started' | 'at-risk') || 'not-started',
      members: project.users?.map(user => ({
        id: user.id.toString(),
        name: user.name || `${user.first_name} ${user.last_name}`,
        avatar: user.avatar || undefined,
      })) || [],
      isStarred: starredProjectIds.includes(project.id),
      is_group_project: project.is_group_project, // Added this property
      onStar: handleToggleStar,
      onViewKanban: handleViewKanban,
      onViewTimeline: handleViewTimeline
    };
  };

  // Force re-render when starredProjectIds changes
  const [forceUpdate, setForceUpdate] = useState(0);

  // Update forceUpdate when starredProjectIds changes
  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [starredProjectIds]);

  // Helper function to check project status
  const isProjectActive = (project: Project) => {
    // Check both status and progress fields
    // Status: 1 = Active, Progress: 1 = InProgress
    return project.status === 1 ||
           project.status === 'active' ||
           project.status === 'in-progress' ||
           project.progress === 1 ||
           project.progress === 'InProgress';
  };

  const isProjectCompleted = (project: Project) => {
    // Status: 2 = Completed, Progress: 2 = Completed
    return project.status === 2 ||
           project.status === 'completed' ||
           project.progress === 2 ||
           project.progress === 'Completed';
  };

  // Apply advanced filters
  const applyAdvancedFilters = (project: Project) => {
    // Priority filter
    if (advancedFilters.priority) {
      // Normalize project priority to a string value for consistent comparison
      let normalizedPriority: string;

      if (typeof project.priority === 'number') {
        // Convert numeric priority to string
        normalizedPriority = project.priority === 0 ? 'low' :
                            project.priority === 1 ? 'medium' :
                            project.priority === 2 ? 'high' : 'medium';
      } else if (typeof project.priority === 'string') {
        // Convert string to lowercase for case-insensitive comparison
        normalizedPriority = project.priority.toLowerCase();
      } else {
        // Default to medium if priority is undefined or null
        normalizedPriority = 'medium';
      }

      // Compare the normalized priority with the filter value
      if (normalizedPriority !== advancedFilters.priority) {
        return false;
      }
    }

    // Due date filter
    if (advancedFilters.dueDate) {
      if (!project.end_date) return false;

      const today = new Date();
      const dueDate = new Date(project.end_date);

      if (advancedFilters.dueDate === 'overdue') {
        if (dueDate >= today) return false;
      } else if (advancedFilters.dueDate === 'thisWeek') {
        const endOfWeek = new Date(today);
        endOfWeek.setDate(today.getDate() + (7 - today.getDay()));
        if (dueDate > endOfWeek || dueDate < today) return false;
      } else if (advancedFilters.dueDate === 'thisMonth') {
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        if (dueDate > endOfMonth || dueDate < today) return false;
      }
    }

    // Has members filter
    if (advancedFilters.hasMembers !== null) {
      const hasMembers = project.members && project.members.length > 0;
      if (hasMembers !== advancedFilters.hasMembers) return false;
    }

    return true;
  };

  // Sort projects
  const sortProjects = (a: Project, b: Project) => {
    const direction = sortOption.direction === 'asc' ? 1 : -1;

    switch (sortOption.field) {
      case 'name':
        return direction * a.name.localeCompare(b.name);

      case 'dueDate':
        if (!a.end_date && !b.end_date) return 0;
        if (!a.end_date) return direction;
        if (!b.end_date) return -direction;
        return direction * (new Date(a.end_date).getTime() - new Date(b.end_date).getTime());

      case 'priority':
        // Normalize priority values to numbers for comparison
        const getPriorityValue = (priority: any): number => {
          if (typeof priority === 'number') {
            return priority;
          } else if (typeof priority === 'string') {
            const lowerPriority = priority.toLowerCase();
            return lowerPriority === 'high' ? 2 :
                   lowerPriority === 'medium' ? 1 :
                   lowerPriority === 'low' ? 0 : 1; // Default to medium (1)
          } else {
            return 1; // Default to medium if undefined or null
          }
        };

        const aPriority = getPriorityValue(a.priority);
        const bPriority = getPriorityValue(b.priority);

        // Higher priority first (2 = high, 1 = medium, 0 = low)
        return direction * (bPriority - aPriority);

      case 'progress':
        const aProgress = typeof a.progress === 'number' ? a.progress : 0;
        const bProgress = typeof b.progress === 'number' ? b.progress : 0;
        return direction * (aProgress - bProgress);

      default:
        return 0;
    }
  };

  // Filter and search projects
  const filteredProjects = projects
    .filter(project => {
      // First, filter out any projects that are in the hiddenProjectIds list
      if (hiddenProjectIds.includes(project.id)) {
        return false;
      }

      // Then apply the selected filter
      if (selectedFilter === 'starred') {
        // Use the most up-to-date starredProjectIds for filtering
        const isStarred = starredProjectIds.includes(project.id);
        return isStarred;
      }
      if (selectedFilter === 'active') {
        return isProjectActive(project);
      }
      if (selectedFilter === 'completed') {
        return isProjectCompleted(project);
      }
      return true; // 'all' filter
    })
    .filter(project => {
      // Search by name and description
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        project.name.toLowerCase().includes(searchLower) ||
        (project.description && project.description.toLowerCase().includes(searchLower))
      );
    })
    // Apply advanced filters
    .filter(applyAdvancedFilters)
    // Apply sorting
    .sort(sortProjects);

  // Handle creating a new project
  const handleCreateProject = () => {
    setIsCreateModalOpen(true);
  };

  // Handle successful project creation
  const handleProjectCreated = () => {
    // Refetch projects to update the list
    refetchProjects();
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      {isLoading && <Loader />}

      {loadingError && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center mb-6">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span>{loadingError}</span>
            <button
              onClick={() => {
                hasFetchedRef.current = false;
                setIsLoading(true);
                fetchWorkspaces();
              }}
              className="ml-auto bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-md text-sm"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 px-2 h-8"
                onClick={() => navigate('/dashboard')}
              >
                <ArrowLeft size={16} />
                Back
              </Button>
            </div>
            <h1 className="text-3xl font-bold text-foreground">Projects</h1>
            <p className="text-muted-foreground mt-1">
              Manage and track all your projects
            </p>
          </div>
          <Button
            className="sm:self-end flex items-center"
            onClick={handleCreateProject}
          >
            <Plus size={18} className="mr-1.5" />
            New Project
          </Button>
        </div>

        {/* Filters and Search */}
        <div className="bg-card border border-border rounded-lg shadow-sm mb-6">
          <div className="p-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-2">
              <Button
                variant={selectedFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter('all')}
                aria-selected={selectedFilter === 'all'}
              >
                All Projects
              </Button>
              <Button
                variant={selectedFilter === 'active' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter('active')}
                aria-selected={selectedFilter === 'active'}
              >
                Active
              </Button>
              <Button
                variant={selectedFilter === 'completed' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter('completed')}
                aria-selected={selectedFilter === 'completed'}
              >
                Completed
              </Button>
              <Button
                variant={selectedFilter === 'starred' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter('starred')}
                aria-selected={selectedFilter === 'starred'}
                className={selectedFilter === 'starred' ? 'bg-primary text-primary-foreground hover:bg-primary/90' : ''}
              >
                Starred
              </Button>
            </div>

            <div className="w-full sm:w-auto flex items-center gap-2">
              <div className="relative flex-grow">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search projects..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              {/* Filter Dropdown */}
              <DropdownMenu open={isFilterMenuOpen} onOpenChange={setIsFilterMenuOpen}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Filter Projects</DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  <DropdownMenuLabel className="text-xs font-normal text-muted-foreground pt-2">Priority</DropdownMenuLabel>
                  <DropdownMenuRadioGroup
                    value={advancedFilters.priority || ""}
                    onValueChange={(value) => setAdvancedFilters({
                      ...advancedFilters,
                      priority: value === "" ? null : value
                    })}
                  >
                    <DropdownMenuRadioItem value="">
                      <span className="flex items-center">
                        All Priorities
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="high">
                      <span className="flex items-center">
                        <ArrowUp className="mr-2 h-4 w-4 text-priority-high" />
                        High
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="medium">
                      <span className="flex items-center">
                        <ArrowRight className="mr-2 h-4 w-4 text-priority-medium" />
                        Medium
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="low">
                      <span className="flex items-center">
                        <ArrowDown className="mr-2 h-4 w-4 text-priority-low" />
                        Low
                      </span>
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>

                  <DropdownMenuSeparator />
                  <DropdownMenuLabel className="text-xs font-normal text-muted-foreground pt-2">Due Date</DropdownMenuLabel>
                  <DropdownMenuRadioGroup
                    value={advancedFilters.dueDate || ""}
                    onValueChange={(value) => setAdvancedFilters({
                      ...advancedFilters,
                      dueDate: value === "" ? null : value
                    })}
                  >
                    <DropdownMenuRadioItem value="">
                      <span className="flex items-center">
                        Any Date
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="overdue">
                      <span className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-destructive" />
                        Overdue
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="thisWeek">
                      <span className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-primary" />
                        Due This Week
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="thisMonth">
                      <span className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-info" />
                        Due This Month
                      </span>
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>

                  <DropdownMenuSeparator />
                  <DropdownMenuLabel className="text-xs font-normal text-muted-foreground pt-2">Team</DropdownMenuLabel>
                  <DropdownMenuRadioGroup
                    value={advancedFilters.hasMembers === null ? "" : advancedFilters.hasMembers ? "team" : "personal"}
                    onValueChange={(value) => setAdvancedFilters({
                      ...advancedFilters,
                      hasMembers: value === "" ? null : value === "team"
                    })}
                  >
                    <DropdownMenuRadioItem value="">
                      <span className="flex items-center">
                        All Projects
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="team">
                      <span className="flex items-center">
                        <Users className="mr-2 h-4 w-4 text-info" />
                        Team Projects
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="personal">
                      <span className="flex items-center">
                        <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                        Personal Projects
                      </span>
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>

                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => setAdvancedFilters({
                      priority: null,
                      dueDate: null,
                      hasMembers: null
                    })}
                    className="justify-center text-center text-sm text-muted-foreground hover:text-foreground"
                  >
                    Reset Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Sort Dropdown */}
              <DropdownMenu open={isSortMenuOpen} onOpenChange={setIsSortMenuOpen}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Sort Projects</DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  <DropdownMenuRadioGroup
                    value={sortOption.field}
                    onValueChange={(value) => setSortOption({
                      ...sortOption,
                      field: value as 'name' | 'dueDate' | 'priority' | 'progress'
                    })}
                  >
                    <DropdownMenuRadioItem value="name">
                      <span className="flex items-center">
                        Name
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="dueDate">
                      <span className="flex items-center">
                        Due Date
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="priority">
                      <span className="flex items-center">
                        Priority
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="progress">
                      <span className="flex items-center">
                        Progress
                      </span>
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>

                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={sortOption.direction}
                    onValueChange={(value) => setSortOption({
                      ...sortOption,
                      direction: value as 'asc' | 'desc'
                    })}
                  >
                    <DropdownMenuRadioItem value="asc">
                      <span className="flex items-center">
                        Ascending
                      </span>
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="desc">
                      <span className="flex items-center">
                        Descending
                      </span>
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        {isLoadingProjects ? (
          <div className="flex justify-center items-center h-64">
            <p className="text-muted-foreground">Loading projects...</p>
          </div>
        ) : filteredProjects.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <LayoutGrid className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No projects found</h3>
            <p className="text-muted-foreground max-w-md mb-6">
              {searchTerm
                ? `No projects match your search for "${searchTerm}"`
                : selectedFilter !== 'all'
                  ? `No projects in the "${selectedFilter}" category`
                  : "You haven't created any projects yet"
              }
            </p>
            <Button onClick={handleCreateProject}>
              <Plus size={16} className="mr-1.5" />
              Create your first project
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                {...project} // Pass the complete project data directly
              />
            ))}
          </div>
        )}
      </main>

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleProjectCreated}
      />
    </div>
  );
};

export default ProjectsPage;
