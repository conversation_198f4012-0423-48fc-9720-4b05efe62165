import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Project } from '@/entities/Project';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ProjectTypeSelector from './ProjectTypeSelector';
import ProjectMemberManager from './ProjectMemberManager';
import { isGroupProject } from '@/utils/projectTypeUtils';

interface ProjectSettingsProps {
  project: Project;
  onProjectUpdated?: (updatedProject: Project) => void;
  defaultTab?: 'general' | 'members' | 'advanced';
}

const ProjectSettings: React.FC<ProjectSettingsProps> = ({
  project,
  onProjectUpdated,
  defaultTab = 'general'
}) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [currentProject, setCurrentProject] = useState<Project>(project);

  // Update current project when the prop changes
  useEffect(() => {
    setCurrentProject(project);
  }, [project]);

  // Handle project updates
  const handleProjectUpdated = (updatedProject: Project) => {
    setCurrentProject(updatedProject);
    if (onProjectUpdated) {
      onProjectUpdated(updatedProject);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Type</CardTitle>
              <CardDescription>
                Choose whether this is a personal or group project
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectTypeSelector
                project={currentProject}
                currentUser={user}
                onProjectUpdated={handleProjectUpdated}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members" className="space-y-4 mt-4">
          <ProjectMemberManager
            project={currentProject}
            currentUser={user}
            onProjectUpdated={handleProjectUpdated}
          />
        </TabsContent>


      </Tabs>
    </div>
  );
};

export default ProjectSettings;
