import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useWorkspace } from '@/context/WorkspaceContext';

interface BackButtonProps {
  className?: string;
}

const BackButton: React.FC<BackButtonProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const { currentWorkspace } = useWorkspace();

  const handleBack = () => {
    // Check if there's a previous page in the history
    if (window.history.length > 2) {
      navigate(-1); // Go back to the previous page
    } else {
      // If no history, navigate to the workspace
      if (currentWorkspace?.id) {
        navigate(`/workspace/${currentWorkspace.id}`);
      } else {
        navigate('/dashboard');
      }
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleBack}
      className={`flex items-center ${className}`}
      aria-label="Go back"
    >
      <ArrowLeft className="h-4 w-4 mr-1" />
      <span>Back</span>
    </Button>
  );
};

export default BackButton;
