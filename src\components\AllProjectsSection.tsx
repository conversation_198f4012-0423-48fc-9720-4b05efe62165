import React, { useEffect, useState } from 'react';
import { ArrowUpRight, <PERSON><PERSON>heck, Plus, Search, ArrowUpDown } from 'lucide-react';
import ProjectCard, { ProjectCardProps } from './ProjectCard';
import { Project, ProjectProgress } from '@/entities/Project';
import { getAllProjects } from '@/api/projectsApi';
import { useToast } from './ui/use-toast';
import Loader from './Loader';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { useNavigate } from 'react-router-dom';
import { TurkishDateHelper } from '@/utils/dateUtils';

type StatusFilter = 'all' | 'completed' | 'in-progress' | 'not-started';

const AllProjectsSection: React.FC = () => {
    const [projects, setProjects] = useState<Project[]>([]);
    const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
    const [filter, setFilter] = useState<StatusFilter>('all');
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const { toast } = useToast();
    const navigate = useNavigate();

    // Helper function to map Project to ProjectCardProps
    const mapProjectToCardProps = (project: Project): ProjectCardProps => {
        // Convert progress from string to number if needed
        const progressNumber = typeof project.progress === 'string'
            ? parseFloat(project.progress) || 0
            : project.progress || 0;

        return {
            id: project.id.toString(),
            title: project.name,
            description: project.description || '',
            progress: progressNumber,
            dueDate: project.end_date ? TurkishDateHelper.formatDate(project.end_date) : 'No date',
            priority: (project.priority as 'low' | 'medium' | 'high') || 'medium',
            status: (project.status as 'completed' | 'in-progress' | 'not-started' | 'at-risk') || 'not-started',
            members: project.members || [],
            isStarred: project.is_starred || false,
            is_group_project: project.is_group_project, // Added this property
            onStar: () => {}, // Empty function as we don't handle starring here
            completion_percentage: project.completion_percentage,
            completed_tasks: project.completed_tasks,
            total_tasks: project.total_tasks
        };
    };

    useEffect(() => {
        const fetchAllProjects = async () => {
            setIsLoading(true);
            try {
                const data = await getAllProjects();
                setProjects(data);
                setFilteredProjects(data);
            } catch (error) {
                toast({
                    title: "Failed to load projects",
                    description: "Could not load your projects. Please try again.",
                    variant: "destructive",
                });
            } finally {
                setIsLoading(false);
            }
        };

        fetchAllProjects();
    }, [toast]);

    // Apply search filter whenever search query changes
    useEffect(() => {
        if (searchQuery.trim() === '') {
            // If search is empty, just apply the status filter
            applyStatusFilter(filter);
        } else {
            // Apply both search and status filter
            const searchResults = projects.filter(project =>
                project.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
                (filter === 'all' ||
                 (filter === 'completed' && project.progress === ProjectProgress.Completed) ||
                 (filter === 'in-progress' && project.progress === ProjectProgress.InProgress) ||
                 (filter === 'not-started' && project.progress === ProjectProgress.NotStarted))
            );
            setFilteredProjects(searchResults);
        }
    }, [searchQuery, filter, projects]);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleCreateProject = () => {
        navigate('/projects/new');
    };

    const applyStatusFilter = (statusFilter: StatusFilter) => {
        switch(statusFilter) {
            case 'all':
                setFilteredProjects(projects);
                break;
            case 'completed':
                setFilteredProjects(projects.filter(project =>
                    project.progress === ProjectProgress.Completed
                ));
                break;
            case 'in-progress':
                setFilteredProjects(projects.filter(project =>
                    project.progress === ProjectProgress.InProgress
                ));
                break;
            case 'not-started':
                setFilteredProjects(projects.filter(project =>
                    project.progress === ProjectProgress.NotStarted
                ));
                break;
        }
    };

    const resetFilter = () => {
        if (filter !== 'all') {
            setFilter('all');
            applyStatusFilter('all');
        }
    }

    const inProgress = () => {
        if (filter !== 'in-progress') {
            setFilter('in-progress');
            applyStatusFilter('in-progress');
        }
    }

    const completedProjects = () => {
        if (filter !== 'completed') {
            setFilter('completed');
            applyStatusFilter('completed');
        }
    }

    const notStarted = () => {
        if (filter !== 'not-started') {
            setFilter('not-started');
            applyStatusFilter('not-started');
        }
    }

    return (
        <>
            <div className="bg-card border border-border rounded-xl shadow-subtle overflow-hidden mb-6">
                <div className="p-5 border-b border-border">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                        <h2 className="text-xl font-semibold mb-3 sm:mb-0">All My Projects</h2>
                        <Button onClick={handleCreateProject} className="flex items-center gap-1">
                            <Plus size={16} />
                            Create New Project
                        </Button>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 items-center">
                        <div className="relative w-full sm:w-64">
                            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="text"
                                placeholder="Search projects..."
                                className="pl-8"
                                value={searchQuery}
                                onChange={handleSearch}
                            />
                        </div>

                        <div className="flex flex-wrap gap-2">
                            <button
                                onClick={resetFilter}
                                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'all' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                    }`}
                            >
                                All Projects
                            </button>
                            <button
                                onClick={inProgress}
                                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'in-progress' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                    }`}
                            >
                                In Progress
                            </button>
                            <button
                                onClick={completedProjects}
                                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'completed' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                    }`}
                            >
                                Completed
                            </button>
                            <button
                                onClick={notStarted}
                                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${filter === 'not-started' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground hover:bg-muted/70'
                                    }`}
                            >
                                Not Started
                            </button>
                        </div>
                    </div>
                </div>

                <div className="p-5">
                    {isLoading ? (
                        <div className="flex justify-center py-8">
                            <Loader />
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                            {filteredProjects.length > 0 ? (
                                filteredProjects.map((project) => (
                                    <ProjectCard key={project.id} {...mapProjectToCardProps(project)} />
                                ))
                            ) : (
                                <div className="md:col-span-2 py-8 flex flex-col items-center justify-center text-center">
                                    <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-3">
                                        <FileCheck className="h-8 w-8 text-muted-foreground" />
                                    </div>
                                    <h3 className="text-lg font-medium text-foreground mb-1">No projects found</h3>
                                    <p className="text-muted-foreground max-w-md">
                                        No projects match your current filter. Try selecting a different status or create a new project.
                                    </p>
                                    <Button
                                        onClick={handleCreateProject}
                                        variant="outline"
                                        className="mt-4 flex items-center gap-1"
                                    >
                                        <Plus size={16} />
                                        Create New Project
                                    </Button>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default AllProjectsSection;
