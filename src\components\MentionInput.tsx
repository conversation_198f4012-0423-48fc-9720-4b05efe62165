import React, { useState, useRef, useEffect } from 'react';
import { User } from '@/entities/User';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { searchProjectMembers } from '@/api/usersApi';
import { cn } from '@/lib/utils';

interface MentionInputProps {
  projectId: number;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

interface MentionSuggestion {
  id: number;
  name: string;
  username: string;
  profile_picture?: string;
}

const MentionInput: React.FC<MentionInputProps> = ({
  projectId,
  value,
  onChange,
  placeholder = 'Add a comment...',
  className,
  disabled = false,
}) => {
  const [mentionSearch, setMentionSearch] = useState('');
  const [mentionSuggestions, setMentionSuggestions] = useState<MentionSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Handle input changes and detect @ mentions
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    // Get cursor position
    const cursorPos = e.target.selectionStart || 0;
    setCursorPosition(cursorPos);

    // Check if we're in a mention context
    const textBeforeCursor = newValue.substring(0, cursorPos);
    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      const query = mentionMatch[1];
      setMentionSearch(query);
      if (query.length > 0) {
        searchForMembers(query);
        setShowSuggestions(true);
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }
  };

  // Search for project members
  const searchForMembers = async (query: string) => {
    if (query.length === 0) return;

    try {
      const members = await searchProjectMembers(projectId, query);
      const suggestions = members.map(member => ({
        id: member.id,
        name: `${member.first_name} ${member.last_name}`,
        username: `${member.first_name.toLowerCase()}${member.last_name.toLowerCase()}`,
        profile_picture: member.profile_picture
      }));

      setMentionSuggestions(suggestions);
      setSelectedSuggestionIndex(0);
    } catch (error) {
      console.error('Error searching for members:', error);
    }
  };

  // Handle selecting a mention suggestion
  const selectMention = (suggestion: MentionSuggestion) => {
    if (!textareaRef.current) return;

    const cursorPos = cursorPosition;
    const textBeforeCursor = value.substring(0, cursorPos);
    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      const startPos = cursorPos - mentionMatch[0].length;
      const newText =
        value.substring(0, startPos) +
        `@${suggestion.username}` +
        value.substring(cursorPos);

      onChange(newText);

      // Set cursor position after the inserted mention
      setTimeout(() => {
        if (textareaRef.current) {
          const newCursorPos = startPos + suggestion.username.length + 1;
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        }
      }, 0);
    }

    setShowSuggestions(false);
  };

  // Handle keyboard navigation in suggestions
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < mentionSuggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        if (mentionSuggestions.length > 0) {
          e.preventDefault();
          selectMention(mentionSuggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        break;
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(e.target as Node) &&
        textareaRef.current &&
        !textareaRef.current.contains(e.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Calculate position for suggestions dropdown
  const getSuggestionsPosition = () => {
    if (!textareaRef.current) return { top: 0, left: 0 };

    const textareaRect = textareaRef.current.getBoundingClientRect();
    const lineHeight = parseInt(getComputedStyle(textareaRef.current).lineHeight) || 20;

    // Calculate which line the cursor is on
    const textBeforeCursor = value.substring(0, cursorPosition);
    const lines = textBeforeCursor.split('\n');
    const cursorLine = lines.length;

    return {
      top: textareaRect.top + (cursorLine * lineHeight) + 8,
      left: textareaRect.left + 10
    };
  };

  return (
    <div className="relative w-full">
      <Textarea
        ref={textareaRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={cn("min-h-[40px] resize-none text-sm w-full", className)}
        disabled={disabled}
      />

      {showSuggestions && mentionSuggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 bg-background border border-border rounded-md shadow-md max-h-[200px] overflow-y-auto w-[250px]"
          style={{
            top: `${textareaRef.current?.offsetHeight}px`,
            left: '0px',
            maxWidth: '100%'
          }}
        >
          <div className="p-1">
            {mentionSuggestions.map((suggestion, index) => (
              <div
                key={suggestion.id}
                className={cn(
                  "flex items-center gap-2 px-2 py-1.5 text-sm rounded cursor-pointer",
                  index === selectedSuggestionIndex ? "bg-accent text-accent-foreground" : "hover:bg-muted"
                )}
                onClick={() => selectMention(suggestion)}
              >
                <Avatar className="h-6 w-6">
                  <AvatarImage src={suggestion.profile_picture} alt={suggestion.name} />
                  <AvatarFallback>
                    {suggestion.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <span>{suggestion.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MentionInput;
