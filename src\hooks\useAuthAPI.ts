
import { useState } from 'react';
import { toast } from "@/components/ui/use-toast";
import api from "@/api/api";
import { useNavigate } from 'react-router-dom';
import { getLocalStorageData, setLocalStorageData } from '@/utils/sessionLocalStorageUtil';
import { normalizeUser, User } from '@/entities/User';
import { getFullImageUrl } from '@/utils/imageUtils';



export const useAuthAPI = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const navigate = useNavigate();




  const login = async (email: string, password: string, redirectUrl?: string) => {
    try {
      setIsLoading(true);
      let response = await api.post('/login', { email, password });

      const userData = {
        ...response.data.user,
      };

      const workspace = {
        ...response.data.workspace,
      };

      // Store user data
      setLocalStorageData('user', userData);

      // Store workspace with user ID in the key to prevent cross-user contamination
      const workspaceKey = `workspace_${userData.id}`;
      setLocalStorageData(workspaceKey, workspace);

      // Also store in the regular workspace key for backward compatibility
      setLocalStorageData('workspace', workspace);

      setLocalStorageData('token', response.data.access_token);

      toast({
        title: "Login successful",
        description: "Welcome back!",
      });

      // If there's a redirect URL, navigate to it, otherwise go to the workspace
      if (redirectUrl) {
        navigate(redirectUrl);
      } else {
        navigate('/workspace/' + workspace.id);
      }

      return normalizeUser(userData);
    } catch (error) {
      toast({
        variant: Number(error.response.data.type) == 1 ? "destructive" : "default",
        title: "Login Failed",
        description: error.response.data.message,
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const requestForAccount = async (firstName: string, lastName: string, email: string, password: string) => {
    try {
      setIsLoading(true);
      console.log('Attempting to create account with email:', email);

      let response = await api.post(`/request-for-account`, {
        first_name: firstName,
        last_name: lastName,
        email: email,
        password: password
      });

      console.log('Account creation response:', response.data);

      toast({
        title: "Account Created",
        description: "Your account has been created successfully. You can now log in.",
      });

      return response.data;
    } catch (error) {
      console.error('Account creation error:', error);

      // Handle different types of errors
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);

        toast({
          variant: "destructive",
          title: "Registration Failed",
          description: error.response.data.message || "An error occurred during registration",
        });
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);

        toast({
          variant: "destructive",
          title: "Connection Error",
          description: "Could not connect to the server. Please check your internet connection.",
        });
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);

        toast({
          variant: "destructive",
          title: "Registration Error",
          description: error.message || "An unexpected error occurred",
        });
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (userData: Partial<User>) => {
    try {
      setIsLoading(true);

      // Get the current user from local storage
      const currentUser = getLocalStorageData('user');

      if (!currentUser || !currentUser.id) {
        console.error('User authentication issue: No user data found in localStorage');
        throw new Error('User not found. Please log in again.');
      }

      // Check if userData is FormData (for profile picture uploads)
      const isFormData = userData instanceof FormData;

      // Log what we're sending for debugging
      if (isFormData) {
        console.log('Sending FormData with fields:',
          Array.from((userData as FormData).keys()).reduce((acc, key) => {
            acc[key] = (userData as FormData).get(key);
            return acc;
          }, {} as Record<string, any>)
        );
      }

      // Set appropriate headers for FormData
      const config = isFormData ? {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Accept': 'application/json'
        }
      } : undefined;

      const response = await api.put(`/update-user/${currentUser.id}`, userData, config);

      // Get the updated user data from the response
      const responseUserData = response.data.user;

      if (!responseUserData) {
        console.error('Invalid response data:', response.data);
        // Don't throw an error, try to recover with current data
        console.warn('Attempting to recover with current user data and updates');
      }

      // Update the user data in local storage
      const updatedUser = {
        ...currentUser,
        ...(isFormData ? {} : userData), // Only merge regular userData, not FormData
        ...(responseUserData || {}) // Apply server response data if available
      };

      // Log the updated user data for debugging
      console.log('Updated user data:', {
        fromCurrentUser: !!currentUser,
        fromFormData: isFormData,
        fromResponse: !!responseUserData,
        result: updatedUser
      });

      setLocalStorageData('user', updatedUser);

      // Only show toast if not called from another function that shows its own toast
      if (!isFormData) {
        toast({
          title: "Profile updated",
          description: "Your profile has been updated successfully.",
        });
      }

      return normalizeUser(updatedUser);
    } catch (error) {
      console.error('Profile update failed:', error);

      // Provide more specific error messages based on the error
      let errorMessage = "Could not update your profile. Please try again.";

      if (error.message.includes('User not found')) {
        errorMessage = error.message;
      } else if (error.response) {
        // Handle specific API error responses
        if (error.response.status === 401) {
          errorMessage = "Authentication error. Please log in again.";
        } else if (error.response.status === 422) {
          // Handle validation errors
          if (error.response.data && error.response.data.errors) {
            const errors = error.response.data.errors;
            const errorKeys = Object.keys(errors);
            if (errorKeys.length > 0) {
              // Show the first validation error
              errorMessage = errors[errorKeys[0]][0] || error.response.data.message;
              console.error('Validation errors:', errors);
            }
          } else if (error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          }
        } else if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }

      toast({
        variant: "destructive",
        title: "Update failed",
        description: errorMessage,
      });

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updatePassword = async (currentPassword: string, newPassword: string, confirmPassword: string) => {
    try {
      setIsLoading(true);

      const response = await api.post('/update-password', {
        current_password: currentPassword,
        new_password: newPassword,
        confirm_password: confirmPassword
      });

      toast({
        title: "Password updated",
        description: "Your password has been updated successfully.",
      });

      return response.data;
    } catch (error) {
      console.error('Password update failed:', error);

      // Handle validation errors
      if (error.response && error.response.data && error.response.data.errors) {
        const errors = error.response.data.errors;

        // Show the first error message
        const firstErrorField = Object.keys(errors)[0];
        const firstErrorMessage = errors[firstErrorField][0];

        toast({
          variant: "destructive",
          title: "Password update failed",
          description: firstErrorMessage || "Could not update your password. Please try again.",
        });
      } else {
        toast({
          variant: "destructive",
          title: "Password update failed",
          description: error.response?.data?.message || "Could not update your password. Please try again.",
        });
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);

      // Get user ID before clearing localStorage to ensure we clear user-specific workspace
      const userData = getLocalStorageData('user');
      const userId = userData?.id;

      // Call the logout API endpoint
      await api.post('/logout');

      // Explicitly remove workspace-related data first
      if (userId) {
        const userWorkspaceKey = `workspace_${userId}`;
        localStorage.removeItem(userWorkspaceKey);
      }
      localStorage.removeItem('workspace');
      localStorage.removeItem('user');
      localStorage.removeItem('token');

      // Clear all localStorage as a fallback
      localStorage.clear();

      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
      navigate('/login');

      return true;
    } catch (error) {
      console.error('Logout failed:', error);

      // Even if the API call fails, clear localStorage to ensure user is logged out on the client side
      localStorage.clear();

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const checkAuth = () => {
    const token = getLocalStorageData('token');

    // Validate token format
    if (!token || typeof token !== 'string' || token.trim() === '') {
      console.log('Invalid or missing token during checkAuth');
      return null;
    }

    try {
      const userData = getLocalStorageData('user');
      if (userData && userData.id) {
        // Normalize the user data to ensure consistent structure
        return normalizeUser(userData);
      }

      // If we have a token but no user data, try to fetch the user data
      // This is a silent attempt, so we don't show loading state
      console.log('Token exists but no user data, fetching user data from API');
      api.get('/user')
        .then(response => {
          if (response.data && response.data.id) {
            console.log('Successfully fetched user data from API');
            const userData = response.data;
            setLocalStorageData('user', userData);
            return normalizeUser(userData);
          } else {
            console.error('API returned invalid user data');
            // Clear invalid data
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            return null;
          }
        })
        .catch(error => {
          console.error('Failed to fetch user data:', error);

          // If we get a 401 or 403, the token is invalid, so clear it
          if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            console.log('Clearing invalid token');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
          }

          return null;
        });
    } catch (error) {
      console.error('Error in checkAuth:', error);
      return null;
    }

    return null;
  };




  /**
   * Upload a profile picture for the current user
   * This uses a dedicated endpoint that only requires the profile picture file
   */
  const uploadProfilePicture = async (file: File) => {
    try {
      setIsLoading(true);

      // Get the current user from local storage
      const currentUser = getLocalStorageData('user');

      if (!currentUser || !currentUser.id) {
        console.error('User authentication issue: No user data found in localStorage');
        throw new Error('User not found. Please log in again.');
      }

      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('profile_picture', file);

      console.log('Uploading profile picture using dedicated endpoint');

      // Use the dedicated profile picture upload endpoint
      const response = await api.post(`/update-profile-picture/${currentUser.id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Accept': 'application/json'
        }
      });

      // Get the updated user data from the response
      const responseUserData = response.data.user;

      if (!responseUserData) {
        console.error('Invalid response data:', response.data);
        throw new Error('Server returned invalid user data');
      }

      // Update the user data in local storage
      const updatedUser = {
        ...currentUser,
        profile_picture: responseUserData.profile_picture
      };

      setLocalStorageData('user', updatedUser);

      toast({
        title: "Profile picture updated",
        description: "Your profile picture has been updated successfully.",
      });

      return normalizeUser(updatedUser);
    } catch (error) {
      console.error('Profile picture upload failed:', error);

      // Provide more specific error messages based on the error
      let errorMessage = "Could not upload your profile picture. Please try again.";

      if (error.message.includes('User not found')) {
        errorMessage = error.message;
      } else if (error.response) {
        // Handle specific API error responses
        if (error.response.status === 401) {
          errorMessage = "Authentication error. Please log in again.";
        } else if (error.response.status === 413) {
          errorMessage = "The image is too large for the server to process.";
        } else if (error.response.status === 422) {
          // Handle validation errors
          if (error.response.data && error.response.data.errors) {
            const errors = error.response.data.errors;
            const errorKeys = Object.keys(errors);
            if (errorKeys.length > 0) {
              // Show the first validation error
              errorMessage = errors[errorKeys[0]][0] || error.response.data.message;
              console.error('Validation errors during profile picture upload:', errors);
            }
          } else if (error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          }
        } else if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      } else if (!navigator.onLine) {
        errorMessage = "Network error. Please check your internet connection.";
      }

      toast({
        variant: "destructive",
        title: "Upload failed",
        description: errorMessage,
      });

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete the user's account permanently
   * This requires the user's password for confirmation
   */
  const deleteAccount = async (password: string) => {
    try {
      setIsLoading(true);

      // Call the delete account API endpoint
      await api.post('/delete-account', { password });

      // Clear all localStorage
      localStorage.clear();

      toast({
        title: "Account deleted",
        description: "Your account has been permanently deleted.",
      });

      // Navigate to the login page
      navigate('/login');

      return true;
    } catch (error) {
      console.error('Account deletion failed:', error);

      // Handle validation errors
      if (error.response && error.response.data && error.response.data.errors) {
        const errors = error.response.data.errors;

        // Show the first error message
        const firstErrorField = Object.keys(errors)[0];
        const firstErrorMessage = errors[firstErrorField][0];

        toast({
          variant: "destructive",
          title: "Account deletion failed",
          description: firstErrorMessage || "Could not delete your account. Please try again.",
        });
      } else {
        toast({
          variant: "destructive",
          title: "Account deletion failed",
          description: error.response?.data?.message || "Could not delete your account. Please try again.",
        });
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    login,
    logout,
    requestForAccount,
    updateProfile,
    updatePassword,
    checkAuth,
    uploadProfilePicture,
    deleteAccount,
  };
};
