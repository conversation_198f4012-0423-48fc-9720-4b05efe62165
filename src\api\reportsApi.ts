import api from './api';

/**
 * Get project overview report
 * @param projectId Project ID
 * @returns Project overview data
 */
export const getProjectOverview = async (projectId: number) => {
  const response = await api.get(`/reports/projects/${projectId}/overview`);
  return response.data;
};

/**
 * Get task distribution report
 * @param projectId Project ID
 * @returns Task distribution data
 */
export const getTaskDistribution = async (projectId: number) => {
  const response = await api.get(`/reports/projects/${projectId}/task-distribution`);
  return response.data;
};

/**
 * Get project progress over time
 * @param projectId Project ID
 * @returns Progress data over time
 */
export const getProgressOverTime = async (projectId: number) => {
  const response = await api.get(`/reports/projects/${projectId}/progress`);
  return response.data;
};

/**
 * Get user productivity report
 * @param projectId Project ID
 * @returns User productivity data
 */
export const getUserProductivity = async (projectId: number) => {
  const response = await api.get(`/reports/projects/${projectId}/user-productivity`);
  return response.data;
};
