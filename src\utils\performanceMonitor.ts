/**
 * Performance monitoring utilities to help identify bottlenecks
 */

// Only enable in development mode
const isDevelopment = import.meta.env.MODE === 'development';

// Store performance marks and measures
const performanceMarks: Record<string, number> = {};

/**
 * Start timing a performance mark
 * @param name The name of the mark
 */
export const startMark = (name: string): void => {
  if (!isDevelopment) return;

  performanceMarks[name] = performance.now();

  // Also use the Performance API if available
  if (typeof performance !== 'undefined' && performance.mark) {
    performance.mark(`${name}-start`);
  }
};

/**
 * End timing a performance mark and log the result
 * @param name The name of the mark
 * @param logLevel The log level to use (default: 'info')
 */
export const endMark = (name: string, logLevel: 'log' | 'info' | 'warn' | 'error' = 'info'): number | null => {
  if (!isDevelopment) return null;

  const startTime = performanceMarks[name];
  if (!startTime) {
    console.warn(`No start mark found for "${name}"`);
    return null;
  }

  const endTime = performance.now();
  const duration = endTime - startTime;

  // Use the Performance API if available
  if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
    performance.mark(`${name}-end`);
    try {
      performance.measure(name, `${name}-start`, `${name}-end`);
    } catch (e) {
      // Some browsers might throw if the marks don't exist
      console.warn(`Error measuring performance for "${name}":`, e);
    }
  }

  // Log the result with the appropriate log level
  console[logLevel](`⏱️ ${name}: ${duration.toFixed(2)}ms`);

  // Clean up
  delete performanceMarks[name];

  return duration;
};

/**
 * Measure the execution time of a function
 * @param fn The function to measure
 * @param name The name of the measurement
 * @param logLevel The log level to use (default: 'info')
 * @returns The result of the function
 */
export function measureExecution<T>(
  fn: () => T,
  name: string,
  logLevel: 'log' | 'info' | 'warn' | 'error' = 'info'
): T {
  if (!isDevelopment) return fn();

  startMark(name);
  const result = fn();
  endMark(name, logLevel);

  return result;
}

/**
 * Measure the execution time of an async function
 * @param fn The async function to measure
 * @param name The name of the measurement
 * @param logLevel The log level to use (default: 'info')
 * @returns A promise that resolves to the result of the function
 */
export async function measureAsyncExecution<T>(
  fn: () => Promise<T>,
  name: string,
  logLevel: 'log' | 'info' | 'warn' | 'error' = 'info'
): Promise<T> {
  if (!isDevelopment) return fn();

  startMark(name);
  try {
    const result = await fn();
    endMark(name, logLevel);
    return result;
  } catch (error) {
    endMark(name, 'error');
    throw error;
  }
}



/**
 * @see performanceMonitor.tsx for the React-specific implementations
 * This includes:
 * - withPerformanceTracking HOC
 * - useRenderTracking hook
 */

/**
 * Log performance metrics to the console
 */
export function logPerformanceMetrics(): void {
  if (!isDevelopment || typeof performance === 'undefined' || !performance.getEntriesByType) return;

  console.group('📊 Performance Metrics');

  // Log navigation timing
  const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  if (navigationTiming) {
    console.log('🔄 Page Load:', navigationTiming.loadEventEnd - navigationTiming.startTime, 'ms');
    console.log('🔄 DOM Content Loaded:', navigationTiming.domContentLoadedEventEnd - navigationTiming.startTime, 'ms');
    console.log('🔄 First Paint:', navigationTiming.responseEnd - navigationTiming.startTime, 'ms');
  }

  // Log custom measures
  const measures = performance.getEntriesByType('measure');
  if (measures.length > 0) {
    console.log('📏 Custom Measurements:');
    measures.forEach(measure => {
      console.log(`  - ${measure.name}: ${measure.duration.toFixed(2)}ms`);
    });
  }

  console.groupEnd();
}

// Initialize performance monitoring
if (isDevelopment) {
  // Log metrics when the page loads
  window.addEventListener('load', () => {
    setTimeout(logPerformanceMetrics, 1000);
  });
}


